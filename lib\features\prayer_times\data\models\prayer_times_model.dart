import 'package:hive/hive.dart';
import '../../domain/entities/prayer_times.dart';

part 'prayer_times_model.g.dart';

@HiveType(typeId: 0)
class PrayerTimesModel extends PrayerTimes {
  @HiveField(0)
  final DateTime dateField;
  @HiveField(1)
  final String fajrField;
  @HiveField(2)
  final String sunriseField;
  @HiveField(3)
  final String dhuhrField;
  @HiveField(4)
  final String asrField;
  @HiveField(5)
  final String sunsetField;
  @HiveField(6)
  final String maghribField;
  @HiveField(7)
  final String ishaField;
  @HiveField(8)
  final String midnightField;
  @HiveField(9)
  final String imsakField;
  @HiveField(10)
  final int calculationMethodField;
  @HiveField(11)
  final double latitudeField;
  @HiveField(12)
  final double longitudeField;
  @HiveField(13)
  final String cityField;
  @HiveField(14)
  final String countryField;
  @HiveField(15)
  final String timezoneField;

  const PrayerTimesModel({
    required this.dateField,
    required this.fajrField,
    required this.sunriseField,
    required this.dhuhrField,
    required this.asrField,
    required this.sunsetField,
    required this.maghrib<PERSON>ield,
    required this.ishaField,
    required this.midnightField,
    required this.imsakField,
    required this.calculationMethodField,
    required this.latitudeField,
    required this.longitudeField,
    required this.cityField,
    required this.countryField,
    required this.timezoneField,
  }) : super(
          date: dateField,
          fajr: fajrField,
          sunrise: sunriseField,
          dhuhr: dhuhrField,
          asr: asrField,
          sunset: sunsetField,
          maghrib: maghribField,
          isha: ishaField,
          midnight: midnightField,
          imsak: imsakField,
          calculationMethod: calculationMethodField,
          latitude: latitudeField,
          longitude: longitudeField,
          city: cityField,
          country: countryField,
          timezone: timezoneField,
        );

  factory PrayerTimesModel.fromJson(Map<String, dynamic> json) {
    print('Parsing JSON: $json'); // Debug print
    final data = json['data'];
    final timings = data['timings'];
    final date = data['date'];
    final meta = data['meta'];

    // Parse date safely
    DateTime parsedDate;
    try {
      final gregorian = date['gregorian'];
      final day = int.parse(gregorian['day'].toString());
      final month = int.parse(gregorian['month']['number'].toString());
      final year = int.parse(gregorian['year'].toString());
      parsedDate = DateTime(year, month, day);
    } catch (e) {
      // Fallback to current date if parsing fails
      parsedDate = DateTime.now();
    }

    return PrayerTimesModel(
      dateField: parsedDate,
      fajrField: _formatTime(timings['Fajr']),
      sunriseField: _formatTime(timings['Sunrise']),
      dhuhrField: _formatTime(timings['Dhuhr']),
      asrField: _formatTime(timings['Asr']),
      sunsetField: _formatTime(timings['Sunset']),
      maghribField: _formatTime(timings['Maghrib']),
      ishaField: _formatTime(timings['Isha']),
      midnightField: _formatTime(timings['Midnight']),
      imsakField: _formatTime(timings['Imsak']),
      calculationMethodField: meta['method']['id'] ?? 4,
      latitudeField: double.tryParse(meta['latitude']?.toString() ?? '0') ?? 0.0,
      longitudeField: double.tryParse(meta['longitude']?.toString() ?? '0') ?? 0.0,
      cityField: _extractLocationInfo(meta, 'city'),
      countryField: _extractLocationInfo(meta, 'country'),
      timezoneField: meta['timezone'] ?? 'UTC',
    );
  }

  factory PrayerTimesModel.fromCalendarJson(Map<String, dynamic> json, DateTime date) {
    final timings = json['timings'];
    final meta = json['meta'];

    return PrayerTimesModel(
      dateField: date,
      fajrField: _formatTime(timings['Fajr']),
      sunriseField: _formatTime(timings['Sunrise']),
      dhuhrField: _formatTime(timings['Dhuhr']),
      asrField: _formatTime(timings['Asr']),
      sunsetField: _formatTime(timings['Sunset']),
      maghribField: _formatTime(timings['Maghrib']),
      ishaField: _formatTime(timings['Isha']),
      midnightField: _formatTime(timings['Midnight']),
      imsakField: _formatTime(timings['Imsak']),
      calculationMethodField: meta['method']['id'] ?? 4,
      latitudeField: double.tryParse(meta['latitude']?.toString() ?? '0') ?? 0.0,
      longitudeField: double.tryParse(meta['longitude']?.toString() ?? '0') ?? 0.0,
      cityField: _extractLocationInfo(meta, 'city'),
      countryField: _extractLocationInfo(meta, 'country'),
      timezoneField: meta['timezone'] ?? 'UTC',
    );
  }

  static String _formatTime(String time) {
    // Remove timezone info and return just HH:MM
    return time.split(' ')[0];
  }

  static String _extractLocationInfo(Map<String, dynamic> meta, String type) {
    // Try to extract location info from meta data
    if (meta.containsKey(type)) {
      return meta[type]?.toString() ?? '';
    }

    // Fallback: try to extract from timezone
    final timezone = meta['timezone']?.toString() ?? '';
    if (timezone.isNotEmpty) {
      final parts = timezone.split('/');
      if (parts.length > 1) {
        return parts.last.replaceAll('_', ' ');
      }
    }

    return type == 'city' ? 'Unknown City' : 'Unknown Country';
  }

  Map<String, dynamic> toJson() {
    return {
      'date': dateField.toIso8601String(),
      'fajr': fajrField,
      'sunrise': sunriseField,
      'dhuhr': dhuhrField,
      'asr': asrField,
      'sunset': sunsetField,
      'maghrib': maghribField,
      'isha': ishaField,
      'midnight': midnightField,
      'imsak': imsakField,
      'calculationMethod': calculationMethodField,
      'latitude': latitudeField,
      'longitude': longitudeField,
      'city': cityField,
      'country': countryField,
      'timezone': timezoneField,
    };
  }

  PrayerTimes toEntity() {
    return PrayerTimes(
      date: dateField,
      fajr: fajrField,
      sunrise: sunriseField,
      dhuhr: dhuhrField,
      asr: asrField,
      sunset: sunsetField,
      maghrib: maghribField,
      isha: ishaField,
      midnight: midnightField,
      imsak: imsakField,
      calculationMethod: calculationMethodField,
      latitude: latitudeField,
      longitude: longitudeField,
      city: cityField,
      country: countryField,
      timezone: timezoneField,
    );
  }
}

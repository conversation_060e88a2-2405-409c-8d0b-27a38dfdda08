// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'prayer_times_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PrayerTimesModelAdapter extends TypeAdapter<PrayerTimesModel> {
  @override
  final int typeId = 0;

  @override
  PrayerTimesModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PrayerTimesModel(
      dateField: fields[0] as DateTime,
      fajrField: fields[1] as String,
      sunriseField: fields[2] as String,
      dhuhrField: fields[3] as String,
      asrField: fields[4] as String,
      sunsetField: fields[5] as String,
      maghribField: fields[6] as String,
      ishaField: fields[7] as String,
      midnightField: fields[8] as String,
      imsakField: fields[9] as String,
      calculationMethodField: fields[10] as int,
      latitudeField: fields[11] as double,
      longitudeField: fields[12] as double,
      cityField: fields[13] as String,
      countryField: fields[14] as String,
      timezoneField: fields[15] as String,
    );
  }

  @override
  void write(BinaryWriter writer, PrayerTimesModel obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.dateField)
      ..writeByte(1)
      ..write(obj.fajrField)
      ..writeByte(2)
      ..write(obj.sunriseField)
      ..writeByte(3)
      ..write(obj.dhuhrField)
      ..writeByte(4)
      ..write(obj.asrField)
      ..writeByte(5)
      ..write(obj.sunsetField)
      ..writeByte(6)
      ..write(obj.maghribField)
      ..writeByte(7)
      ..write(obj.ishaField)
      ..writeByte(8)
      ..write(obj.midnightField)
      ..writeByte(9)
      ..write(obj.imsakField)
      ..writeByte(10)
      ..write(obj.calculationMethodField)
      ..writeByte(11)
      ..write(obj.latitudeField)
      ..writeByte(12)
      ..write(obj.longitudeField)
      ..writeByte(13)
      ..write(obj.cityField)
      ..writeByte(14)
      ..write(obj.countryField)
      ..writeByte(15)
      ..write(obj.timezoneField);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PrayerTimesModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:audioplayers/audioplayers.dart'; // 🔊 NEU
import '../../../../core/app_config.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../bloc/settings_bloc.dart';

class AdhanSoundSelector extends StatefulWidget {
  const AdhanSoundSelector({super.key});

  @override
  State<AdhanSoundSelector> createState() => _AdhanSoundSelectorState();
}

class _AdhanSoundSelectorState extends State<AdhanSoundSelector> {
  // 🔊 Ein Player für Previews
  late final AudioPlayer _audioPlayer;

  static const Map<String, String> adhanSounds = {
    'Adhan_1_short.mp3': 'Default Adhan',
    'Adhan_1_long.mp3': 'Long Adhan',
    '<PERSON>han_2_short.mp3': '<PERSON>han 2_<PERSON> Adhan',
    '<PERSON>han_2_long.mp3': '<PERSON>han 2_Long Adhan',
    '<PERSON>han_3_short.mp3': '<PERSON>han 3 Short Adhan',
    '<PERSON>han_3_long.mp3': '<PERSON>han 3 Long Adhan',
    'silent': 'Silent Notification',
  };

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    // Für kurze Previews keine Loop-Wiederholung:
    _audioPlayer.setReleaseMode(ReleaseMode.stop);
    _audioPlayer.setVolume(1.0);
  }

  @override
  void dispose() {
    _audioPlayer.stop();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GlassmorphismContainer(
      forceTransparent: true,
      borderRadius: 20,
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40, height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text('Adhan Sound', style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: AppConfig.smallPadding),
          Text(
            'Choose the Adhan sound for prayer notifications',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.defaultPadding),

          BlocBuilder<SettingsBloc, SettingsState>(
            builder: (context, state) {
              return Column(
                children: adhanSounds.entries.map((entry) {
                  final soundFile = entry.key;
                  final soundName = entry.value;
                  final isSelected = state.adhanSound == soundFile;
                  final isSilent = soundFile == 'silent';

                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Colors.grey[300],
                      child: Icon(
                        isSilent ? Icons.volume_off : Icons.volume_up,
                        color: isSelected ? Colors.white : Colors.grey[600],
                      ),
                    ),
                    title: Text(
                      soundName,
                      style: TextStyle(
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                    subtitle: isSilent ? const Text('No sound, notification only') : null,
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (!isSilent)
                          IconButton(
                            icon: const Icon(Icons.play_arrow),
                            onPressed: () => _playPreview(soundFile), // 🔊
                            tooltip: 'Preview',
                          ),
                        if (isSelected)
                          Icon(Icons.check, color: Theme.of(context).colorScheme.primary),
                      ],
                    ),
                    onTap: () {
                      // Auswahl speichern
                      context.read<SettingsBloc>().add(UpdateAdhanSound(sound: soundFile));
                      // Preview stoppen, falls gerade läuft
                      _audioPlayer.stop();
                      Navigator.pop(context);
                    },
                  );
                }).toList(),
              );
            },
          ),

          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'Tap the play button to preview sounds',
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _playPreview(String soundFile) async {
    try {
      if (soundFile == 'silent') {
        HapticFeedback.lightImpact();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.volume_off, color: Colors.white),
                  SizedBox(width: 8),
                  Text('Silent mode - no sound will be played'),
                ],
              ),
              duration: Duration(seconds: 2),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // 🔊 Asset abspielen (aus assets/audio/)
      await _audioPlayer.stop(); // falls vorher etwas spielte
      await _audioPlayer.play(AssetSource('audio/$soundFile'));

      HapticFeedback.mediumImpact();
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.volume_up, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text('Playing preview: ${adhanSounds[soundFile]}')),
            ],
          ),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error playing $soundFile. Ensure it exists in assets/audio/.'),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

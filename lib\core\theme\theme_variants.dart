import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../app_config.dart';
import 'design_tokens.dart';
import 'tokens.dart';
import 'cream_gold_theme.dart';

enum ThemeVariant { classic, elegant }

class ThemeVariants {
  
  // Get theme based on variant
  static ThemeData getTheme(ThemeVariant variant, bool isDark) {
    switch (variant) {
      case ThemeVariant.classic:
        return isDark ? _getClassicDarkTheme() : _getClassicLightTheme();
      case ThemeVariant.elegant:
        return isDark ? CreamGoldTheme.createDarkTheme() : CreamGoldTheme.createLightTheme();
    }
  }
  
  // Classic themes (traditional green design) - Updated with darker green
  static ThemeData _getClassicLightTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.fromSeed(
        seedColor: ColorTokens.classicLightPrimary, // Now using darker green #1B5E20
        brightness: Brightness.light,
        primary: ColorTokens.classicLightPrimary,
        secondary: ColorTokens.classicLightSecondary,
        surface: ColorTokens.classicLightSurface,
        onSurface: ColorTokens.classicLightText,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        outline: ColorTokens.classicLightDivider,
      ),
      textTheme: GoogleFonts.robotoTextTheme().copyWith(
        displayLarge: GoogleFonts.roboto(
          fontSize: 32,
          fontWeight: FontWeight.w700,
          color: ColorTokens.classicLightPrimary, // Darker green
        ),
        headlineLarge: GoogleFonts.roboto(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: ColorTokens.classicLightText,
        ),
        bodyLarge: GoogleFonts.roboto(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: ColorTokens.classicLightText,
        ),
      ),
      cardTheme: CardThemeData(
        elevation: 2,
        shadowColor: Colors.grey.shade300,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        color: ColorTokens.classicLightCard,
        surfaceTintColor: Colors.transparent,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: ColorTokens.classicLightPrimary, // Darker green
        foregroundColor: Colors.white,
        elevation: 2,
        centerTitle: true,
      ),
      popupMenuTheme: PopupMenuThemeData(
        color: ColorTokens.classicLightCard,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        textStyle: GoogleFonts.roboto(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: ColorTokens.classicLightText,
        ),
      ),
    );
  }
  
  static ThemeData _getClassicDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: ColorTokens.classicDarkPrimary,
        brightness: Brightness.dark,
        primary: ColorTokens.classicDarkPrimary,
        secondary: ColorTokens.classicDarkSecondary,
        surface: ColorTokens.classicDarkSurface,
        onSurface: Colors.white,
        onPrimary: Colors.black,
        onSecondary: Colors.black,
      ),
      textTheme: GoogleFonts.robotoTextTheme(ThemeData.dark().textTheme).copyWith(
        displayLarge: GoogleFonts.roboto(
          fontSize: 32,
          fontWeight: FontWeight.w700,
          color: ColorTokens.classicDarkAccent,
        ),
      ),
      cardTheme: CardThemeData(
        elevation: 4,
        shadowColor: Colors.black.withOpacity(0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        color: ColorTokens.classicDarkCard,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: ColorTokens.classicDarkSurface,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      popupMenuTheme: PopupMenuThemeData(
        color: ColorTokens.classicDarkCard,
        elevation: 8,
        shadowColor: Colors.black.withOpacity(0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        textStyle: GoogleFonts.roboto(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.white,
        ),
      ),
    );
  }
}
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/location_entity.dart';
import '../repositories/location_repository.dart';

class SearchLocation implements UseCase<List<LocationEntity>, SearchLocationParams> {
  final LocationRepository repository;

  SearchLocation(this.repository);

  @override
  Future<Either<Failure, List<LocationEntity>>> call(SearchLocationParams params) async {
    return await repository.searchLocation(params.query);
  }
}

class SearchLocationParams extends Equatable {
  final String query;

  const SearchLocationParams({required this.query});

  @override
  List<Object> get props => [query];
}

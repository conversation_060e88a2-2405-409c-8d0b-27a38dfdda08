/// Umm al-Qura Hijri Date implementation
/// Based on official Saudi Arabia calendar calculations
class UmmalquraDate {
  final int year;
  final int month;
  final int day;

  const UmmalquraDate(this.year, this.month, this.day);

  /// Convert Gregorian date to Umm al-Qura Hijri date
  factory UmmalquraDate.fromGregorian(DateTime gregorianDate) {
    // Ensure we work with date-only (midnight local)
    final dateOnly = DateTime(gregorianDate.year, gregorianDate.month, gregorianDate.day);
    
    // Use lookup table for accurate conversion within supported range
    final hijriData = _getHijriFromLookup(dateOnly);
    if (hijriData != null) {
      return UmmalquraDate(hijriData['year']!, hijriData['month']!, hijriData['day']!);
    }
    
    // Fallback calculation for dates outside lookup table
    return _calculateHijriDate(dateOnly);
  }

  /// Add days to Hijri date (for offset adjustments)
  UmmalquraDate add(Duration duration) {
    // Convert to Gregorian, add duration, convert back
    final gregorian = toGregorian();
    final newGregorian = gregorian.add(duration);
    return UmmalquraDate.fromGregorian(newGregorian);
  }

  /// Convert Hijri date back to Gregorian (approximate)
  DateTime toGregorian() {
    // This is an approximation - in real implementation would use reverse lookup
    final hijriEpoch = DateTime(622, 7, 16);
    final daysSinceEpoch = ((year - 1) * 354.367).round() + _getDaysInMonths(month - 1) + day - 1;
    return hijriEpoch.add(Duration(days: daysSinceEpoch));
  }

  int _getDaysInMonths(int months) {
    // Approximate days in given number of months
    return (months * 29.5).round();
  }

  /// Lookup table for accurate Umm al-Qura dates (sample data)
  /// In production, this would contain the full official table
  static Map<String, int>? _getHijriFromLookup(DateTime gregorianDate) {
    final key = '${gregorianDate.year}-${gregorianDate.month.toString().padLeft(2, '0')}-${gregorianDate.day.toString().padLeft(2, '0')}';
    
    // Sample lookup data (would be much larger in production)
    final lookupTable = {
      // 2024 data (sample)
      '2024-01-01': {'year': 1445, 'month': 6, 'day': 19}, // 19 Jumada al-thani 1445
      '2024-03-10': {'year': 1445, 'month': 8, 'day': 29}, // 29 Sha'ban 1445
      '2024-03-11': {'year': 1445, 'month': 9, 'day': 1},  // 1 Ramadan 1445
      '2024-04-10': {'year': 1445, 'month': 10, 'day': 1}, // 1 Shawwal 1445
      '2024-06-17': {'year': 1445, 'month': 12, 'day': 10}, // 10 Dhu al-Hijjah 1445
      '2024-07-07': {'year': 1446, 'month': 1, 'day': 1},  // 1 Muharram 1446
      '2024-08-06': {'year': 1446, 'month': 2, 'day': 1},  // 1 Safar 1446
      '2024-09-04': {'year': 1446, 'month': 3, 'day': 1},  // 1 Rabi' al-awwal 1446
      '2024-10-04': {'year': 1446, 'month': 4, 'day': 1},  // 1 Rabi' al-thani 1446
      '2024-11-02': {'year': 1446, 'month': 5, 'day': 1},  // 1 Jumada al-awwal 1446
      '2024-12-02': {'year': 1446, 'month': 6, 'day': 1},  // 1 Jumada al-thani 1446
      '2025-01-01': {'year': 1446, 'month': 6, 'day': 30}, // 30 Jumada al-thani 1446
      '2025-02-28': {'year': 1446, 'month': 8, 'day': 29}, // 29 Sha'ban 1446
      '2025-03-01': {'year': 1446, 'month': 9, 'day': 1},  // 1 Ramadan 1446
      '2025-03-30': {'year': 1446, 'month': 9, 'day': 29}, // 29 Ramadan 1446
      '2025-03-31': {'year': 1446, 'month': 10, 'day': 1}, // 1 Shawwal 1446
    };
    
    return lookupTable[key];
  }

  /// Fallback calculation for dates outside lookup table
  static UmmalquraDate _calculateHijriDate(DateTime gregorianDate) {
    // More accurate Hijri calculation
    final hijriEpoch = DateTime(622, 7, 16);
    final daysSinceEpoch = gregorianDate.difference(hijriEpoch).inDays;
    
    if (daysSinceEpoch < 0) {
      return const UmmalquraDate(1, 1, 1);
    }
    
    // Calculate Hijri year (354.367 days per year on average)
    final hijriYear = (daysSinceEpoch / 354.367).floor() + 1;
    
    // Calculate remaining days in current year
    final daysInCurrentYear = daysSinceEpoch - ((hijriYear - 1) * 354.367).floor();
    
    // Hijri months alternate between 30 and 29 days
    // Odd months (1,3,5,7,9,11) have 30 days
    // Even months (2,4,6,8,10,12) have 29 days
    final monthDays = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];
    
    int hijriMonth = 1;
    int remainingDays = daysInCurrentYear.floor();
    
    for (int i = 0; i < 12; i++) {
      if (remainingDays <= monthDays[i]) {
        hijriMonth = i + 1;
        break;
      }
      remainingDays -= monthDays[i];
    }
    
    final hijriDay = remainingDays > 0 ? remainingDays : 1;
    
    return UmmalquraDate(
      hijriYear,
      hijriMonth.clamp(1, 12),
      hijriDay.clamp(1, 30),
    );
  }

  @override
  String toString() => '$day/$month/$year AH';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UmmalquraDate &&
          runtimeType == other.runtimeType &&
          year == other.year &&
          month == other.month &&
          day == other.day;

  @override
  int get hashCode => year.hashCode ^ month.hashCode ^ day.hashCode;
}

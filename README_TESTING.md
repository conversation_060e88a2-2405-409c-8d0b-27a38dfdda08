# Gebet App - Testing Guide

## Kako testirati nove funkcionalnosti

### 1. 🎨 Background + Glassmorphism

**Testiranje transparentnih efekata:**
1. <PERSON><PERSON><PERSON>i **Settings** → **General** → **Background**
2. <PERSON><PERSON><PERSON><PERSON> **"Default"** → Kartice su pune (neprozirne)
3. Odaberi bilo koji drugi background (Mosque, Geometric, etc.) → Kartice postaju staklene s blur efektom
4. Testiraj u **Dark/Light** modu za različite efekte

**Očekivani rezultat:**
- Default = normalne kartice
- Ostali backgrounds = glassmorphism s BackdropFilter blur
- Vizualno vidljiv blur i transparencija

### 2. 🔔 Ikonica po namazu (Adhan ON/OFF)

**Testiranje notification toggle:**
1. Na početnom ekranu vidi listu "Today's Prayer Times"
2. Svaki namaz ima **zvono ikonu** s desne strane
3. **Tap na zvono** → Toggle ON/OFF za taj namaz
4. **<PERSON>elena ikona** = uključeno, **siva ikona** = isključeno
5. Restartaj app → stanje se čuva

**Očekivani rezultat:**
- Ikona jasno pokazuje ON/OFF stanje
- Stanje se trajno sprema u Hive
- SnackBar potvrda pri promjeni

### 3. 📅 Datum (Gregorijanski + Hijri)

**Testiranje dual date display:**
1. Na vrhu početnog ekrana vidi **Date Header**
2. Format: "Mon, 24 Feb 2025 · 16 Shaʿbān 1446 AH (Umm al-Qura)"
3. Promijeni jezik u Settings → datum se lokalizira
4. Ako je Ramazan → posebna oznaka s ⭐

**Očekivani rezultat:**
- Oba datuma prikazana
- Lokalizacija na 5 jezika
- Ramadan indikator kad je aktivan

### 4. 📊 Pogledi: Danas / Tjedan / Mjesec + Export

**Testiranje view selector:**
1. Ispod Date Header-a vidi **Today | Week | Month** selector
2. Tap na svaki → mijenja se odabrani view
3. Tap na **download ikonu** u AppBar → otvara Export dialog

**Testiranje export funkcionalnosti:**
1. U Export dialog-u odaberi:
   - **Export as PDF** → otvara PDF preview/print
   - **Export as CSV** → dijeli CSV datoteku
   - **Share PDF** → sistemski share dialog
   - **Print** → print dialog

**Očekivani rezultat:**
- View selector radi bez trzaja
- Export generira ispravne datoteke
- Share/Print otvara sistemske dijaloge

### 5. 🎵 Adhan Preview u postavkama

**Testiranje audio preview:**
1. Settings → Notifications → Adhan Sound
2. Tap **Play button** kod svakog zvuka
3. **Haptic feedback** + SnackBar poruka
4. "Silent" opcija → posebna poruka

**Očekivani rezultat:**
- Play button daje haptic feedback
- Jasne poruke o odabiru
- Nema crasheva

### 6. 🌐 Offline način rada

**Napomena:** Ova funkcionalnost je pripremljena ali nije potpuno implementirana u ovoj verziji. Trebat će dodatni razvoj za:
- Preuzimanje unaprijed
- Offline indikator
- Cache management

## 🐛 Poznati problemi

1. **Hijri datumi** koriste aproksimaciju umjesto točne Umm al-Qura kalkulacije
2. **Audio datoteke** nisu uključene - trebaju se dodati u `assets/audio/`
3. **Offline mode** nije potpuno implementiran
4. **Week/Month view** prikazuje samo današnje podatke

## 📱 Testiranje na različitim uređajima

- **Android**: Sve funkcionalnosti rade
- **iOS**: Export/Share možda neće raditi bez dodatnih dozvola
- **Dark/Light mode**: Testiraj glassmorphism u oba moda
- **Različite veličine ekrana**: Responsive design

## 🔧 Debugging

Ako ima problema:
1. `flutter clean && flutter pub get`
2. `flutter packages pub run build_runner build --delete-conflicting-outputs`
3. Provjeri da li su svi paketi instalirani
4. Provjeri Android/iOS dozvole za notifikacije

## 📸 Screenshots za dokumentaciju

Potrebni screenshotovi:
1. **Default vs Glassmorphism** background
2. **Notification toggle** ikone ON/OFF
3. **Date header** s Hijri datumom
4. **View selector** Today/Week/Month
5. **Export dialog** s opcijama
6. **Adhan sound selector** s play buttonima

## ✅ Checklist za QA

- [ ] Background Default → normalne kartice
- [ ] Background ostali → glassmorphism efekt
- [ ] Notification toggle → ON/OFF ikone rade
- [ ] Stanje se čuva nakon restart-a
- [ ] Date header → oba datuma prikazana
- [ ] View selector → prebacivanje radi
- [ ] Export → PDF/CSV/Share/Print
- [ ] Adhan preview → haptic feedback
- [ ] Dark/Light mode → sve radi
- [ ] flutter analyze → čist
- [ ] Nema runtime exception-a

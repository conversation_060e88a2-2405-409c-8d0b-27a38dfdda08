import 'package:flutter_test/flutter_test.dart';
import 'package:gebet_app/features/prayer_times/domain/entities/prayer_times.dart';

void main() {
  group('PrayerTimes', () {
    late PrayerTimes prayerTimes;

    setUp(() {
      prayerTimes = const PrayerTimes(
        date: DateTime(2024, 1, 15),
        fajr: '05:30',
        sunrise: '07:00',
        dhuhr: '12:15',
        asr: '15:30',
        sunset: '18:00',
        maghrib: '18:15',
        isha: '19:45',
        midnight: '00:15',
        imsak: '05:20',
        calculationMethod: 4,
        latitude: 21.3891,
        longitude: 39.8579,
        city: 'Mecca',
        country: 'Saudi Arabia',
        timezone: 'Asia/Riyadh',
      );
    });

    test('should return correct prayers list', () {
      final prayers = prayerTimes.prayersList;
      
      expect(prayers.length, 5);
      expect(prayers[0].name, 'Fajr');
      expect(prayers[0].time, '05:30');
      expect(prayers[1].name, '<PERSON>huhr');
      expect(prayers[1].time, '12:15');
      expect(prayers[2].name, 'Asr');
      expect(prayers[2].time, '15:30');
      expect(prayers[3].name, 'Maghrib');
      expect(prayers[3].time, '18:15');
      expect(prayers[4].name, 'Isha');
      expect(prayers[4].time, '19:45');
    });

    test('should return next prayer correctly', () {
      // This test would need to be adjusted based on current time
      // For now, just test that it returns a prayer
      final nextPrayer = prayerTimes.getNextPrayer();
      expect(nextPrayer, isNotNull);
      expect(nextPrayer!.name, isIn(['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha']));
    });

    test('should calculate time until next prayer', () {
      final timeRemaining = prayerTimes.getTimeUntilNextPrayer();
      expect(timeRemaining, isNotNull);
      expect(timeRemaining!.inSeconds, greaterThanOrEqualTo(0));
    });

    test('should have correct properties', () {
      expect(prayerTimes.city, 'Mecca');
      expect(prayerTimes.country, 'Saudi Arabia');
      expect(prayerTimes.calculationMethod, 4);
      expect(prayerTimes.latitude, 21.3891);
      expect(prayerTimes.longitude, 39.8579);
    });
  });

  group('PrayerTime', () {
    test('should create prayer time correctly', () {
      const prayerTime = PrayerTime(name: 'Fajr', time: '05:30');
      
      expect(prayerTime.name, 'Fajr');
      expect(prayerTime.time, '05:30');
    });

    test('should be equal when properties are same', () {
      const prayerTime1 = PrayerTime(name: 'Fajr', time: '05:30');
      const prayerTime2 = PrayerTime(name: 'Fajr', time: '05:30');
      
      expect(prayerTime1, equals(prayerTime2));
    });
  });
}

import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../theme/design_tokens.dart';
import '../theme/tokens.dart';
import '../../features/settings/presentation/bloc/settings_bloc.dart';

class GlassmorphismCard extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final bool forceTransparent;

  const GlassmorphismCard({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius = DesignTokens.radiusM,
    this.forceTransparent = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = DesignTokens.isDarkMode(context);
    
    return BlocBuilder<SettingsBloc, SettingsState>(
      builder: (context, state) {
        // Check if background is set to photos (not default)
        final hasPhotoBackground = state.backgroundType != 'default';
        
        // Use glassmorphism only with photo background and when not forced transparent
        final useGlassmorphism = hasPhotoBackground && isDark && !forceTransparent;
        
        if (useGlassmorphism) {
          return Container(
            width: width,
            height: height,
            margin: margin,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(borderRadius),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15), // Sigma 15 as specified
                child: Container(
                  padding: padding ?? const EdgeInsets.all(DesignTokens.spacingM),
                  decoration: BoxDecoration(
                    color: isDark 
                        ? ColorTokens.glassDarkFill  // rgba(255,255,255,0.08)
                        : ColorTokens.glassLightFill, // rgba(255,255,255,0.15)
                    borderRadius: BorderRadius.circular(borderRadius),
                    border: Border.all(
                      color: ColorTokens.glassBorder, // rgba(255,255,255,0.30)
                      width: 1.2, // 1.2px as specified
                    ),
                  ),
                  child: child,
                ),
              ),
            ),
          );
        } else {
          // Default background: use solid cards with no opacity
          return Container(
            width: width,
            height: height,
            margin: margin,
            child: Card(
              elevation: isDark ? 6 : 2,
              shadowColor: isDark
                  ? Colors.black.withOpacity(0.3)
                  : Colors.grey.shade300, // Solid shadow for light mode
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(borderRadius),
              ),
              color: DesignTokens.getCardColor(context),
              child: Padding(
                padding: padding ?? const EdgeInsets.all(DesignTokens.spacingM),
                child: child,
              ),
            ),
          );
        }
      },
    );
  }
}

class GlassmorphismContainer extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final bool forceTransparent;

  const GlassmorphismContainer({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius = 20,
    this.forceTransparent = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = DesignTokens.isDarkMode(context);
    
    return BlocBuilder<SettingsBloc, SettingsState>(
      builder: (context, state) {
        // Check if background is set to photos (not default)
        final hasPhotoBackground = state.backgroundType != 'default';
        
        // Use glassmorphism only with photo background and when not forced transparent
        final useGlassmorphism = hasPhotoBackground && isDark && !forceTransparent;
        
        if (useGlassmorphism) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15), // Sigma 15 as specified
              child: Container(
                width: width,
                height: height,
                margin: margin,
                padding: padding,
                decoration: BoxDecoration(
                  color: isDark 
                      ? ColorTokens.glassDarkFill  // rgba(255,255,255,0.08)
                      : ColorTokens.glassLightFill, // rgba(255,255,255,0.15)
                  borderRadius: BorderRadius.circular(borderRadius),
                  border: Border.all(
                    color: ColorTokens.glassBorder, // rgba(255,255,255,0.30)
                    width: 1.2, // 1.2px as specified
                  ),
                ),
                child: child,
              ),
            ),
          );
        } else {
          // Default background: use solid container with no opacity
          return Container(
            width: width,
            height: height,
            margin: margin,
            padding: padding,
            decoration: BoxDecoration(
              color: DesignTokens.getCardColor(context),
              borderRadius: BorderRadius.circular(borderRadius),
              boxShadow: [
                BoxShadow(
                  color: isDark
                      ? Colors.black.withOpacity(0.3)
                      : Colors.grey.shade300, // Solid shadow for light mode
                  blurRadius: isDark ? 12 : 6,
                  offset: Offset(0, isDark ? 6 : 3),
                ),
              ],
            ),
            child: child,
          );
        }
      },
    );
  }
}

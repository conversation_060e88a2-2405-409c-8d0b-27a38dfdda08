import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../../../core/l10n/app_localizations.dart';
import '../../../../core/theme/design_tokens.dart';
import '../../../../core/utils/hijri_date.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../../../settings/presentation/bloc/settings_bloc.dart';
import '../../domain/entities/prayer_times.dart';
import '../bloc/prayer_view_state.dart';
import '../widgets/export_dialog.dart';
import '../../export/prayer_times_exporter.dart';
import '../widgets/prayer_times_table.dart';

class MonthViewPage extends StatefulWidget {
  final List<PrayerTimes> monthPrayerTimes;
  final String cityName;

  const MonthViewPage({
    super.key,
    required this.monthPrayerTimes,
    this.cityName = 'Current Location',
  });

  @override
  State<MonthViewPage> createState() => _MonthViewPageState();
}

class _MonthViewPageState extends State<MonthViewPage> {
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.monthlyPrayerTimes),
        backgroundColor: DesignTokens.getCardColor(context),
        foregroundColor: DesignTokens.getTextColor(context),
        elevation: 0,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'export_pdf':
                  _exportPDF();
                  break;
                case 'export_csv':
                  _exportCSV();
                  break;
                case 'share_pdf':
                  _sharePDF();
                  break;
                case 'print':
                  _printPrayerTimes();
                  break;
                case 'export_pdf_landscape':
                  _exportPDFLandscape();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export_pdf',
                child: Row(
                  children: [
                    Icon(Icons.picture_as_pdf),
                    SizedBox(width: 8),
                    Text('Export PDF'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_csv',
                child: Row(
                  children: [
                    Icon(Icons.table_chart),
                    SizedBox(width: 8),
                    Text('Export CSV'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'share_pdf',
                child: Row(
                  children: [
                    Icon(Icons.share),
                    SizedBox(width: 8),
                    Text('Share PDF'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'print',
                child: Row(
                  children: [
                    Icon(Icons.print),
                    SizedBox(width: 8),
                    Text('Print'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_pdf_landscape',
                child: Row(
                  children: [
                    Icon(Icons.crop_rotate),
                    SizedBox(width: 8),
                    Text('PDF Landscape'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      backgroundColor: DesignTokens.getSurfaceColor(context),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(DesignTokens.spacingM),
          child: Column(
            children: [
              // Month header
              _buildMonthHeader(context, l10n),
              const SizedBox(height: DesignTokens.spacingM),

              // Prayer times table
              Expanded(
                child: PrayerTimesTable(
                  prayerTimesList: widget.monthPrayerTimes,
                  viewType: 'month',
                  cityName: widget.cityName,
                  showExportActions: false, // Export now only available through three-dot menu
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMonthHeader(BuildContext context, AppLocalizations l10n) {
    if (widget.monthPrayerTimes.isEmpty) return const SizedBox.shrink();

    final firstDay = widget.monthPrayerTimes.first.date;
    final lastDay = widget.monthPrayerTimes.last.date;

    return GlassmorphismCard(
      child: Row(
        children: [
          Icon(
            Icons.calendar_month,
            color: DesignTokens.lightPrimary,
            size: 24,
          ),
          const SizedBox(width: DesignTokens.spacingS),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  DateFormat('MMMM yyyy').format(firstDay),
                  style: DesignTokens.titleLarge.copyWith(
                    color: DesignTokens.getTextColor(context),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${widget.monthPrayerTimes.length} days • ${widget.cityName}',
                  style: DesignTokens.bodyMedium.copyWith(
                    color: DesignTokens.getTextColor(context, secondary: true),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingS,
              vertical: DesignTokens.spacingXS,
            ),
            decoration: BoxDecoration(
              color: DesignTokens.lightPrimary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(DesignTokens.radiusM),
              border: Border.all(
                color: DesignTokens.lightPrimary.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Text(
              'Month View',
              style: DesignTokens.labelSmall.copyWith(
                color: DesignTokens.lightPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _exportPDF() {
    final settingsState = context.read<SettingsBloc>().state;
    PrayerTimesExporter.exportToPdf(
      context: context,
      prayerTimesList: widget.monthPrayerTimes,
      viewType: 'month',
      cityName: widget.cityName,
      hijriOffset: settingsState.hijriOffset,
      landscape: false,
    );
  }

  void _exportCSV() {
    final settingsState = context.read<SettingsBloc>().state;
    PrayerTimesExporter.exportToCsv(
      context: context,
      prayerTimesList: widget.monthPrayerTimes,
      viewType: 'month',
      cityName: widget.cityName,
      hijriOffset: settingsState.hijriOffset,
    );
  }

  void _sharePDF() {
    final settingsState = context.read<SettingsBloc>().state;
    PrayerTimesExporter.sharePdf(
      context: context,
      prayerTimesList: widget.monthPrayerTimes,
      viewType: 'month',
      cityName: widget.cityName,
      hijriOffset: settingsState.hijriOffset,
    );
  }

  void _printPrayerTimes() {
    final settingsState = context.read<SettingsBloc>().state;
    PrayerTimesExporter.printPrayerTimes(
      context: context,
      prayerTimesList: widget.monthPrayerTimes,
      viewType: 'month',
      cityName: widget.cityName,
      hijriOffset: settingsState.hijriOffset,
      landscape: false,
    );
  }

  void _exportPDFLandscape() {
    final settingsState = context.read<SettingsBloc>().state;
    PrayerTimesExporter.exportToPdf(
      context: context,
      prayerTimesList: widget.monthPrayerTimes,
      viewType: 'month',
      cityName: widget.cityName,
      hijriOffset: settingsState.hijriOffset,
      landscape: true,
    );
  }

}

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/utils/hijri_date.dart';
import 'prayer_view_event.dart';
import 'prayer_view_state.dart';

class PrayerViewBloc extends Bloc<PrayerViewEvent, PrayerViewState> {
  PrayerViewBloc() : super(PrayerViewState(
    currentView: PrayerViewType.today,
    selectedDate: HijriDateUtils.getTodayLocal(),
    location: 'Unknown',
    calculationMethod: 1,
  )) {
    on<ChangeViewType>(_onChangeViewType);
    on<NavigateToWeekView>(_onNavigateToWeekView);
    on<NavigateToMonthView>(_onNavigateToMonthView);
    on<UpdateSelectedDate>(_onUpdateSelectedDate);
  }

  void _onChangeViewType(ChangeViewType event, Emitter<PrayerViewState> emit) {
    emit(state.copyWith(currentView: event.viewType));
  }

  void _onNavigateToWeekView(NavigateToWeekView event, Emitter<PrayerViewState> emit) {
    emit(state.copyWith(
      currentView: PrayerViewType.week,
      selectedDate: event.date,
    ));
  }

  void _onNavigateToMonthView(NavigateToMonthView event, Emitter<PrayerViewState> emit) {
    emit(state.copyWith(
      currentView: PrayerViewType.month,
      selectedDate: event.date,
    ));
  }

  void _onUpdateSelectedDate(UpdateSelectedDate event, Emitter<PrayerViewState> emit) {
    emit(state.copyWith(selectedDate: event.date));
  }

  /// Get date range for current view type
  DateRange getDateRange() {
    final today = HijriDateUtils.getTodayLocal();
    
    switch (state.currentView) {
      case PrayerViewType.today:
        return DateRange(start: today, end: today);
      
      case PrayerViewType.week:
        final weekStart = state.selectedDate;
        final weekEnd = weekStart.add(const Duration(days: 6));
        return DateRange(start: weekStart, end: weekEnd);
      
      case PrayerViewType.month:
        final monthStart = DateTime(state.selectedDate.year, state.selectedDate.month, 1);
        final monthEnd = DateTime(state.selectedDate.year, state.selectedDate.month + 1, 0);
        return DateRange(start: monthStart, end: monthEnd);
    }
  }
}

class DateRange {
  final DateTime start;
  final DateTime end;

  const DateRange({required this.start, required this.end});

  int get dayCount => end.difference(start).inDays + 1;

  List<DateTime> get dates {
    final result = <DateTime>[];
    for (int i = 0; i < dayCount; i++) {
      result.add(start.add(Duration(days: i)));
    }
    return result;
  }
}

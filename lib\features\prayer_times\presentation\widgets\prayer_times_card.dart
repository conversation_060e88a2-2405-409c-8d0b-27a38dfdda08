import 'package:flutter/material.dart';
import '../../../../core/app_config.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../../domain/entities/prayer_times.dart';

class PrayerTimesCard extends StatelessWidget {
  final PrayerTimes prayerTimes;

  const PrayerTimesCard({
    super.key,
    required this.prayerTimes,
  });

  @override
  Widget build(BuildContext context) {
    return GlassmorphismCard(
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Prayer Times',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppConfig.smallPadding),
            Text(
              '${prayerTimes.city}, ${prayerTimes.country}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: AppConfig.defaultPadding),
            // Prayer times will be displayed here
            // This is a placeholder implementation
          ],
        ),
    );
  }
}

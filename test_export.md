# Export Functionality Test Guide

This guide helps you manually test the export functionality of the Gebet app.

## Prerequisites

1. Build and run the app on a physical device or emulator
2. Ensure the app has prayer times data loaded
3. Make sure you have internet connection for initial data loading

## Test Cases

### 1. PDF Export - Portrait Mode

**Steps:**
1. Open the app and navigate to Week View or Month View
2. Look for the "Export as PDF" button in the export actions row
3. Tap "Export as PDF"
4. Verify that the system print/share dialog appears
5. Check that the PDF preview shows:
   - Correct header with city name and date range
   - Prayer times table with all 5 prayers
   - Proper formatting and readability
   - Hijri dates alongside Gregorian dates

**Expected Result:**
- PDF generates successfully without errors
- Content is properly formatted and readable
- All prayer times are correctly displayed

### 2. PDF Export - Landscape Mode

**Steps:**
1. In Week View or Month View, tap the "⋮" (more options) button
2. Select "Export as PDF (Landscape)"
3. Verify the landscape PDF preview
4. Check that the table fits better in landscape orientation

**Expected Result:**
- PDF generates in landscape orientation
- Table columns have more space
- Content remains readable and well-formatted

### 3. CSV Export

**Steps:**
1. In Week View or Month View, tap "Export as CSV"
2. Verify that the system share dialog appears
3. Share to a file manager or email app
4. Open the CSV file and verify content

**Expected CSV Format:**
```
Date,Hijri Date,Fajr,Dhuhr,Asr,Maghrib,Isha
2024-01-01,"1 Rajab 1445",05:30,12:15,15:30,18:00,19:30
2024-01-02,"2 Rajab 1445",05:31,12:16,15:31,18:01,19:31
...
```

**Expected Result:**
- CSV file contains all prayer times data
- Dates are properly formatted
- Hijri dates are included
- File can be opened in spreadsheet applications

### 4. Share PDF

**Steps:**
1. Tap the "⋮" (more options) button
2. Select "Share PDF"
3. Choose a sharing method (email, messaging, etc.)
4. Verify the PDF is attached and can be sent

**Expected Result:**
- PDF is generated and attached to the sharing method
- Recipient can open and view the PDF correctly

### 5. Print Functionality

**Steps:**
1. Tap "Print" from the export options
2. Verify that the system print dialog appears
3. Check print preview
4. If you have a printer available, test actual printing

**Expected Result:**
- Print dialog opens successfully
- Print preview shows the prayer times table
- Actual printing produces readable output (if tested)

### 6. Export Dialog

**Steps:**
1. Navigate to any view with export functionality
2. Tap the export/download icon in the app bar
3. Verify the export dialog appears with all options

**Expected Dialog Content:**
- "Export & Share" title
- "Export as PDF" option with description
- "Export as CSV" option with description  
- "Share PDF" option with description
- "Print" option with description
- "Export as PDF (Landscape)" option with description
- "Close" button

### 7. Error Handling

**Test Scenarios:**
1. Try exporting with no internet connection
2. Try exporting with insufficient storage space
3. Try exporting when no sharing apps are available

**Expected Behavior:**
- App should show appropriate error messages
- App should not crash
- User should be able to retry or cancel

## Test Data Verification

For each export, verify that the following data is correctly included:

### Header Information
- City name (e.g., "Current Location" or actual city)
- Date range (e.g., "Jan 1 - Jan 7, 2024")
- View type indicator ("Week View", "Month View", etc.)

### Prayer Times Table
- Date column with Gregorian dates
- Hijri date information
- All 5 prayer times: Fajr, Dhuhr, Asr, Maghrib, Isha
- Times in correct format (HH:MM)
- Today's date highlighted (if applicable)

### Footer Information (PDF only)
- App name and version
- Generation timestamp
- Proper page margins and formatting

## Performance Testing

1. **Large Data Sets**: Test with a full month of prayer times (30+ days)
2. **Multiple Exports**: Perform several exports in succession
3. **Memory Usage**: Monitor app memory usage during export operations

## Platform-Specific Testing

### Android
- Test with different Android versions (API 21+)
- Verify file permissions are handled correctly
- Test with different file managers and sharing apps

### iOS
- Test with iOS share sheet
- Verify Files app integration
- Test with different iOS versions

## Troubleshooting Common Issues

### PDF Generation Fails
- Check device storage space
- Verify app permissions
- Restart the app and try again

### CSV Export Not Working
- Check if device has apps that can handle CSV files
- Try sharing to email or cloud storage
- Verify the CSV content is properly formatted

### Print Dialog Not Appearing
- Check if device has print services enabled
- Verify printer connectivity (if applicable)
- Try from different views (week vs month)

## Test Results Template

```
Test Date: ___________
Device: ___________
OS Version: ___________
App Version: ___________

[ ] PDF Export - Portrait: PASS/FAIL
[ ] PDF Export - Landscape: PASS/FAIL  
[ ] CSV Export: PASS/FAIL
[ ] Share PDF: PASS/FAIL
[ ] Print: PASS/FAIL
[ ] Export Dialog: PASS/FAIL
[ ] Error Handling: PASS/FAIL

Notes:
_________________________________
_________________________________
_________________________________
```

## Automated Testing

Run the automated tests with:

```bash
# Unit tests
flutter test test/features/prayer_times/export/

# Integration tests (requires device/emulator)
flutter test integration_test/export_integration_test.dart
```

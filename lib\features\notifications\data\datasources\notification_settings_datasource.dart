import 'dart:convert';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';

/// Datasource for managing notification settings per prayer time
/// Uses format: notify:yyyyMMdd:prayerId -> bool
class NotificationSettingsDatasource {
  static const String _boxName = 'notification_settings';
  
  late Box<String> _box;
  
  /// Initialize the datasource
  Future<void> init() async {
    _box = await Hive.openBox<String>(_boxName);
  }
  
  /// Generate key for notification setting
  String _generateKey({
    required DateTime date,
    required String prayerId,
  }) {
    final dateStr = DateFormat('yyyyMMdd').format(date);
    return 'notify:${dateStr}:$prayerId';
  }
  
  /// Set notification enabled for a specific prayer time
  Future<void> setNotificationEnabled({
    required DateTime date,
    required String prayerId,
    required bool enabled,
  }) async {
    final key = _generateKey(date: date, prayerId: prayerId);
    await _box.put(key, enabled.toString());
  }
  
  /// Get notification enabled status for a specific prayer time
  bool isNotificationEnabled({
    required DateTime date,
    required String prayerId,
  }) {
    final key = _generateKey(date: date, prayerId: prayerId);
    final value = _box.get(key);
    
    if (value == null) {
      // Default to enabled for new entries
      return true;
    }
    
    return value.toLowerCase() == 'true';
  }
  
  /// Set all notifications for a date
  Future<void> setAllNotificationsForDate({
    required DateTime date,
    required bool enabled,
  }) async {
    final prayerIds = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
    
    for (final prayerId in prayerIds) {
      await setNotificationEnabled(
        date: date,
        prayerId: prayerId,
        enabled: enabled,
      );
    }
  }
  
  /// Get all notification settings for a date
  Map<String, bool> getNotificationsForDate(DateTime date) {
    final prayerIds = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
    final result = <String, bool>{};
    
    for (final prayerId in prayerIds) {
      result[prayerId] = isNotificationEnabled(date: date, prayerId: prayerId);
    }
    
    return result;
  }
  
  /// Check if all notifications are enabled for a date
  bool areAllNotificationsEnabled(DateTime date) {
    final notifications = getNotificationsForDate(date);
    return notifications.values.every((enabled) => enabled);
  }
  
  /// Check if any notifications are enabled for a date
  bool areAnyNotificationsEnabled(DateTime date) {
    final notifications = getNotificationsForDate(date);
    return notifications.values.any((enabled) => enabled);
  }
  
  /// Get enabled prayer IDs for a date
  List<String> getEnabledPrayerIds(DateTime date) {
    final notifications = getNotificationsForDate(date);
    return notifications.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();
  }
  
  /// Get disabled prayer IDs for a date
  List<String> getDisabledPrayerIds(DateTime date) {
    final notifications = getNotificationsForDate(date);
    return notifications.entries
        .where((entry) => !entry.value)
        .map((entry) => entry.key)
        .toList();
  }
  
  /// Clear old notification settings (older than 30 days)
  Future<void> clearOldSettings() async {
    final keys = _box.keys.toList();
    final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
    final cutoffDateStr = DateFormat('yyyyMMdd').format(cutoffDate);
    
    for (final key in keys) {
      if (key.toString().startsWith('notify:')) {
        try {
          final parts = key.toString().split(':');
          if (parts.length >= 2) {
            final dateStr = parts[1];
            if (dateStr.compareTo(cutoffDateStr) < 0) {
              await _box.delete(key);
            }
          }
        } catch (e) {
          // Remove corrupted entries
          await _box.delete(key);
        }
      }
    }
  }
  
  /// Get all notification settings (for debugging)
  Map<String, bool> getAllSettings() {
    final result = <String, bool>{};
    
    for (final key in _box.keys) {
      final value = _box.get(key);
      if (value != null) {
        result[key.toString()] = value.toLowerCase() == 'true';
      }
    }
    
    return result;
  }
  
  /// Clear all notification settings
  Future<void> clearAllSettings() async {
    await _box.clear();
  }
  
  /// Get settings count
  int getSettingsCount() {
    return _box.length;
  }
  
  /// Export settings as JSON (for backup)
  String exportSettings() {
    final settings = getAllSettings();
    return json.encode(settings);
  }
  
  /// Import settings from JSON (for restore)
  Future<void> importSettings(String jsonString) async {
    try {
      final settings = json.decode(jsonString) as Map<String, dynamic>;
      
      for (final entry in settings.entries) {
        await _box.put(entry.key, entry.value.toString());
      }
    } catch (e) {
      throw Exception('Failed to import settings: $e');
    }
  }
  
  /// Get notification settings for a date range
  Map<DateTime, Map<String, bool>> getNotificationsForDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) {
    final result = <DateTime, Map<String, bool>>{};
    
    DateTime currentDate = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day);
    
    while (currentDate.isBefore(end) || currentDate.isAtSameMomentAs(end)) {
      result[currentDate] = getNotificationsForDate(currentDate);
      currentDate = currentDate.add(const Duration(days: 1));
    }
    
    return result;
  }
  
  /// Set notification settings for a date range
  Future<void> setNotificationsForDateRange({
    required DateTime startDate,
    required DateTime endDate,
    required String prayerId,
    required bool enabled,
  }) async {
    DateTime currentDate = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day);
    
    while (currentDate.isBefore(end) || currentDate.isAtSameMomentAs(end)) {
      await setNotificationEnabled(
        date: currentDate,
        prayerId: prayerId,
        enabled: enabled,
      );
      currentDate = currentDate.add(const Duration(days: 1));
    }
  }
  
  /// Get statistics about notification settings
  Map<String, dynamic> getStatistics() {
    final allSettings = getAllSettings();
    final totalSettings = allSettings.length;
    final enabledSettings = allSettings.values.where((enabled) => enabled).length;
    final disabledSettings = totalSettings - enabledSettings;
    
    // Count by prayer type
    final prayerCounts = <String, Map<String, int>>{};
    final prayerIds = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
    
    for (final prayerId in prayerIds) {
      prayerCounts[prayerId] = {'enabled': 0, 'disabled': 0};
    }
    
    for (final entry in allSettings.entries) {
      final key = entry.key;
      final enabled = entry.value;
      
      if (key.startsWith('notify:')) {
        final parts = key.split(':');
        if (parts.length >= 3) {
          final prayerId = parts[2];
          if (prayerCounts.containsKey(prayerId)) {
            if (enabled) {
              prayerCounts[prayerId]!['enabled'] = 
                  (prayerCounts[prayerId]!['enabled'] ?? 0) + 1;
            } else {
              prayerCounts[prayerId]!['disabled'] = 
                  (prayerCounts[prayerId]!['disabled'] ?? 0) + 1;
            }
          }
        }
      }
    }
    
    return {
      'totalSettings': totalSettings,
      'enabledSettings': enabledSettings,
      'disabledSettings': disabledSettings,
      'enabledPercentage': totalSettings > 0 ? (enabledSettings / totalSettings * 100).round() : 0,
      'prayerCounts': prayerCounts,
    };
  }
}

import 'package:flutter/material.dart';
import '../../../../core/utils/notification_debug.dart';

class NotificationTestingWidget extends StatefulWidget {
  const NotificationTestingWidget({super.key});

  @override
  State<NotificationTestingWidget> createState() => _NotificationTestingWidgetState();
}

class _NotificationTestingWidgetState extends State<NotificationTestingWidget> {
  Map<String, dynamic>? _statusInfo;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      title: const Text('Notification Testing'),
      subtitle: const Text('Debug notification system'),
      leading: const Icon(Icons.bug_report),
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Status Check
              ElevatedButton.icon(
                onPressed: _isLoading ? null : _checkStatus,
                icon: _isLoading 
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.info),
                label: const Text('Check Notification Status'),
              ),
              
              const SizedBox(height: 8),
              
              // Test Immediate Notification
              ElevatedButton.icon(
                onPressed: _testImmediateNotification,
                icon: const Icon(Icons.notification_important),
                label: const Text('Test Immediate Notification'),
              ),
              
              const SizedBox(height: 8),
              
              // Test Scheduled Notification
              ElevatedButton.icon(
                onPressed: _testScheduledNotification,
                icon: const Icon(Icons.schedule),
                label: const Text('Test Scheduled (10s)'),
              ),
              
              const SizedBox(height: 8),
              
              // Request Permissions
              ElevatedButton.icon(
                onPressed: _requestPermissions,
                icon: const Icon(Icons.security),
                label: const Text('Request Permissions'),
              ),
              
              const SizedBox(height: 8),
              
              // Clear Test Notifications
              TextButton.icon(
                onPressed: _clearTestNotifications,
                icon: const Icon(Icons.clear),
                label: const Text('Clear Test Notifications'),
              ),
              
              if (_statusInfo != null) ...[
                const SizedBox(height: 16),
                const Divider(),
                const Text(
                  'Status Information:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Text(
                    _formatStatusInfo(_statusInfo!),
                    style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _checkStatus() async {
    setState(() => _isLoading = true);
    
    try {
      final status = await NotificationDebugUtils.checkNotificationStatus();
      setState(() => _statusInfo = status);
    } catch (e) {
      _showError('Failed to check status: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testImmediateNotification() async {
    try {
      final success = await NotificationDebugUtils.sendTestNotification();
      if (success) {
        _showSuccess('Test notification sent! Check your notification panel.');
      } else {
        _showError('Failed to send test notification');
      }
    } catch (e) {
      _showError('Error sending test notification: $e');
    }
  }

  Future<void> _testScheduledNotification() async {
    try {
      final success = await NotificationDebugUtils.scheduleTestNotification();
      if (success) {
        _showSuccess('Scheduled test notification for 10 seconds from now!');
      } else {
        _showError('Failed to schedule test notification');
      }
    } catch (e) {
      _showError('Error scheduling test notification: $e');
    }
  }

  Future<void> _requestPermissions() async {
    try {
      final granted = await NotificationDebugUtils.requestPermissions();
      if (granted) {
        _showSuccess('Notification permissions granted!');
      } else {
        _showError('Notification permissions denied or failed');
      }
    } catch (e) {
      _showError('Error requesting permissions: $e');
    }
  }

  Future<void> _clearTestNotifications() async {
    try {
      await NotificationDebugUtils.clearTestNotifications();
      _showSuccess('Test notifications cleared');
    } catch (e) {
      _showError('Error clearing test notifications: $e');
    }
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  String _formatStatusInfo(Map<String, dynamic> info) {
    final buffer = StringBuffer();
    
    info.forEach((key, value) {
      buffer.writeln('$key: $value');
    });
    
    return buffer.toString();
  }
}
part of 'notification_bloc.dart';

abstract class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object> get props => [];
}

class ScheduleNotificationsEvent extends NotificationEvent {
  final PrayerTimes prayerTimes;
  final bool adhanEnabled;

  const ScheduleNotificationsEvent({
    required this.prayerTimes,
    required this.adhanEnabled,
  });

  @override
  List<Object> get props => [prayerTimes, adhanEnabled];
}

class ScheduleSelectiveNotificationsEvent extends NotificationEvent {
  final PrayerTimes prayerTimes;
  final bool adhanEnabled;

  const ScheduleSelectiveNotificationsEvent({
    required this.prayerTimes,
    required this.adhanEnabled,
  });

  @override
  List<Object> get props => [prayerTimes, adhanEnabled];
}

class CancelNotificationsEvent extends NotificationEvent {}

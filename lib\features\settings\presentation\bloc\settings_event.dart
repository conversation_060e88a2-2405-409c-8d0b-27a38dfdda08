import 'package:flutter/material.dart';
import '../../../../core/theme/app_themes.dart';

part of 'settings_bloc.dart';

abstract class SettingsEvent extends Equatable {
  const SettingsEvent();

  @override
  List<Object> get props => [];
}

class LoadSettings extends SettingsEvent {}

class UpdateThemeMode extends SettingsEvent {
  final ThemeMode themeMode;

  const UpdateThemeMode({required this.themeMode});

  @override
  List<Object> get props => [themeMode];
}

class UpdateAppDesign extends SettingsEvent {
  final AppDesign appDesign;

  const UpdateAppDesign({required this.appDesign});

  @override
  List<Object> get props => [appDesign];
}

class UpdateLocale extends SettingsEvent {
  final Locale locale;

  const UpdateLocale({required this.locale});

  @override
  List<Object> get props => [locale];
}

class UpdateCalculationMethod extends SettingsEvent {
  final int method;

  const UpdateCalculationMethod({required this.method});

  @override
  List<Object> get props => [method];
}

class UpdateNotificationsEnabled extends SettingsEvent {
  final bool enabled;

  const UpdateNotificationsEnabled({required this.enabled});

  @override
  List<Object> get props => [enabled];
}

class UpdateAdhanEnabled extends SettingsEvent {
  final bool enabled;

  const UpdateAdhanEnabled({required this.enabled});

  @override
  List<Object> get props => [enabled];
}

class UpdateAdhanSound extends SettingsEvent {
  final String sound;

  const UpdateAdhanSound({required this.sound});

  @override
  List<Object> get props => [sound];
}

class UpdateHijriOffset extends SettingsEvent {
  final int offset;

  const UpdateHijriOffset({required this.offset});

  @override
  List<Object> get props => [offset];
}



class UpdateAutoLocation extends SettingsEvent {
  final bool enabled;

  const UpdateAutoLocation({required this.enabled});

  @override
  List<Object> get props => [enabled];
}

class UpdateCustomLocation extends SettingsEvent {
  final double? latitude;
  final double? longitude;
  final String? locationName;

  const UpdateCustomLocation({
    this.latitude,
    this.longitude,
    this.locationName,
  });

  @override
  List<Object> get props => [latitude ?? 0, longitude ?? 0, locationName ?? ''];
}

class ResetSettings extends SettingsEvent {}

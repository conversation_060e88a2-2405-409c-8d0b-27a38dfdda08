import 'package:equatable/equatable.dart';

enum PrayerViewType { today, week, month }

class PrayerViewState extends Equatable {
  final PrayerViewType currentView;
  final DateTime selectedDate;
  final String location;
  final int calculationMethod;

  const PrayerViewState({
    this.currentView = PrayerViewType.today,
    required this.selectedDate,
    required this.location,
    required this.calculationMethod,
  });

  PrayerViewState copyWith({
    PrayerViewType? currentView,
    DateTime? selectedDate,
    String? location,
    int? calculationMethod,
  }) {
    return PrayerViewState(
      currentView: currentView ?? this.currentView,
      selectedDate: selectedDate ?? this.selectedDate,
      location: location ?? this.location,
      calculationMethod: calculationMethod ?? this.calculationMethod,
    );
  }

  @override
  List<Object> get props => [currentView, selectedDate, location, calculationMethod];
}

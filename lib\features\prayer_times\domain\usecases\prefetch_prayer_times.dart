import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/prayer_times_repository.dart';

class PrefetchPrayerTimes implements UseCase<void, PrefetchPrayerTimesParams> {
  final PrayerTimesRepository repository;

  PrefetchPrayerTimes(this.repository);

  @override
  Future<Either<Failure, void>> call(PrefetchPrayerTimesParams params) async {
    try {
      final startDate = DateTime.now();
      final endDate = startDate.add(Duration(days: params.daysAhead));
      
      // Prefetch prayer times for the specified range
      final result = await repository.getPrayerTimesRange(
        startDate: startDate,
        endDate: endDate,
        latitude: params.latitude,
        longitude: params.longitude,
        calculationMethod: params.calculationMethod,
      );

      return result.fold(
        (failure) => Left(failure),
        (prayerTimesList) => const Right(null),
      );
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to prefetch prayer times: ${e.toString()}'));
    }
  }
}

class PrefetchPrayerTimesParams extends Equatable {
  final double latitude;
  final double longitude;
  final int calculationMethod;
  final int daysAhead;

  const PrefetchPrayerTimesParams({
    required this.latitude,
    required this.longitude,
    required this.calculationMethod,
    this.daysAhead = 30,
  });

  @override
  List<Object> get props => [latitude, longitude, calculationMethod, daysAhead];
}

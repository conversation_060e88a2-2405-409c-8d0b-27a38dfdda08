import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../core/error/exceptions.dart';
import '../../../prayer_times/domain/entities/prayer_times.dart';
import 'notification_settings_datasource.dart';
import '../../../../core/dependency_injection.dart';
import '../../../settings/data/datasources/settings_local_datasource.dart';

abstract class NotificationDataSource {
  Future<void> schedulePrayerNotifications(PrayerTimes prayerTimes, bool adhanEnabled);
  Future<void> scheduleSelectivePrayerNotifications(
    PrayerTimes prayerTimes,
    bool adhanEnabled,
    NotificationSettingsDatasource settingsDataSource,
  );
  Future<void> cancelAllNotifications();
  Future<void> cancelNotificationById(int id);
  Future<bool> requestPermissions();
  Future<List<PendingNotificationRequest>> getPendingNotifications();
}

class NotificationDataSourceImpl implements NotificationDataSource {
  final FlutterLocalNotificationsPlugin _notifications;

  NotificationDataSourceImpl() : _notifications = FlutterLocalNotificationsPlugin();

  @override
  Future<void> schedulePrayerNotifications(PrayerTimes prayerTimes, bool adhanEnabled) async {
    try {
      // Cancel existing notifications for this date
      await _cancelNotificationsForDate(prayerTimes.date);

      final prayers = prayerTimes.prayersList;

      for (int i = 0; i < prayers.length; i++) {
        final prayer = prayers[i];
        await _scheduleIndividualPrayer(prayerTimes, prayer, i, adhanEnabled);
      }
    } catch (e) {
      throw NotificationException(message: 'Failed to schedule notifications: ${e.toString()}');
    }
  }

  @override
  Future<void> scheduleSelectivePrayerNotifications(
    PrayerTimes prayerTimes,
    bool adhanEnabled,
    NotificationSettingsDatasource settingsDataSource,
  ) async {
    try {
      // Cancel existing notifications for this date
      await _cancelNotificationsForDate(prayerTimes.date);

      final prayers = prayerTimes.prayersList;
      final prayerIds = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];

      for (int i = 0; i < prayers.length; i++) {
        final prayer = prayers[i];
        final prayerId = prayerIds[i];

        // Check if notification is enabled for this specific prayer time
        final isEnabled = settingsDataSource.isNotificationEnabled(
          date: prayerTimes.date,
          prayerId: prayerId,
        );

        if (isEnabled) {
          await _scheduleIndividualPrayer(prayerTimes, prayer, i, adhanEnabled);
        }
      }
    } catch (e) {
      throw NotificationException(message: 'Failed to schedule selective notifications: ${e.toString()}');
    }
  }

  Future<void> _scheduleIndividualPrayer(
    PrayerTimes prayerTimes,
    dynamic prayer,
    int index,
    bool adhanEnabled,
  ) async {
    final timeParts = prayer.time.split(':');
    final hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);

    final scheduledDate = tz.TZDateTime(
      tz.local,
      prayerTimes.date.year,
      prayerTimes.date.month,
      prayerTimes.date.day,
      hour,
      minute,
    );

    // Only schedule if the time hasn't passed
    if (scheduledDate.isAfter(tz.TZDateTime.now(tz.local))) {
      final notificationId = _generateNotificationId(prayerTimes.date, index);

      // Get the selected adhan sound from settings
      String? adhanSoundResource;
      String? iosAdhanSound;

      if (adhanEnabled) {
        try {
          final settingsDataSource = DependencyInjection.instance<SettingsLocalDataSource>();
          final settings = await settingsDataSource.getSettings();
          final selectedSound = settings.adhanSound;

          // Map selected sound to raw resource name and iOS sound
          adhanSoundResource = _mapToRawResourceName(selectedSound);
          iosAdhanSound = selectedSound;

          print('Scheduling notification for ${prayer.name} at $scheduledDate');
          print('Using adhan sound: $selectedSound -> $adhanSoundResource');
        } catch (e) {
          print('Failed to get adhan sound setting, using default: $e');
          adhanSoundResource = 'adhan_1_short';
          iosAdhanSound = 'Adhan_1_short.mp3';
        }
      } else {
        print('Scheduling silent notification for ${prayer.name} at $scheduledDate');
      }

      // Debug logging
      print('Scheduling notification for ${prayer.name} at $scheduledDate');
      print('Adhan enabled: $adhanEnabled');
      print('Adhan sound resource: $adhanSoundResource');
      print('Notification ID: $notificationId');

      await _notifications.zonedSchedule(
        notificationId,
        'Prayer Time - ${prayer.name}',
        'It\'s time for ${prayer.name} prayer',
        scheduledDate,
        NotificationDetails(
          android: AndroidNotificationDetails(
            'prayer_times',
            'Prayer Times',
            channelDescription: 'Notifications for prayer times',
            importance: Importance.max,
            priority: Priority.high,
            showWhen: true,
            when: scheduledDate.millisecondsSinceEpoch,
            enableVibration: true,
            playSound: true,
            fullScreenIntent: true,
            category: AndroidNotificationCategory.alarm,
            visibility: NotificationVisibility.public,
            autoCancel: false,
            ongoing: false,
            silent: false,
            channelShowBadge: true,
            sound: adhanSoundResource != null
                ? RawResourceAndroidNotificationSound(adhanSoundResource)
                : null, // Use default system sound if no adhan or invalid sound
            largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
          ),
          iOS: DarwinNotificationDetails(
            sound: iosAdhanSound,
            presentAlert: true,
            presentBadge: true,
            presentSound: adhanEnabled,
            categoryIdentifier: 'prayer_time',
          ),
        ),
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      );
      
      print('Successfully scheduled notification for ${prayer.name}');
    } else {
      print('Skipping ${prayer.name} - time has already passed');
    }
  }

  @override
  Future<void> cancelAllNotifications() async {
    try {
      await _notifications.cancelAll();
    } catch (e) {
      throw NotificationException(message: 'Failed to cancel notifications: ${e.toString()}');
    }
  }

  @override
  Future<void> cancelNotificationById(int id) async {
    try {
      await _notifications.cancel(id);
    } catch (e) {
      throw NotificationException(message: 'Failed to cancel notification $id: ${e.toString()}');
    }
  }

  @override
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    try {
      return await _notifications.pendingNotificationRequests();
    } catch (e) {
      throw NotificationException(message: 'Failed to get pending notifications: ${e.toString()}');
    }
  }

  @override
  Future<bool> requestPermissions() async {
    try {
      // Request notification permission using permission_handler for better compatibility
      final notificationStatus = await Permission.notification.request();
      
      // Request exact alarm permission for Android 12+ (required for precise scheduling)
      final exactAlarmStatus = await Permission.scheduleExactAlarm.request();
      
      // Also use the plugin's permission method as a fallback
      final androidImplementation = _notifications.resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>();

      bool pluginPermissionGranted = true;
      if (androidImplementation != null) {
        final granted = await androidImplementation.requestNotificationsPermission();
        pluginPermissionGranted = granted ?? false;
      }

      final iosImplementation = _notifications.resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin>();

      if (iosImplementation != null) {
        final granted = await iosImplementation.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
        pluginPermissionGranted = granted ?? false;
      }

      final allPermissionsGranted = notificationStatus.isGranted && 
                                   exactAlarmStatus.isGranted && 
                                   pluginPermissionGranted;
      
      print('Notification permissions:');
      print('  - notification: ${notificationStatus}');
      print('  - exactAlarm: ${exactAlarmStatus}');
      print('  - plugin: $pluginPermissionGranted');
      print('  - all granted: $allPermissionsGranted');
      
      return allPermissionsGranted;
    } catch (e) {
      print('Error requesting permissions: $e');
      throw NotificationException(message: 'Failed to request permissions: ${e.toString()}');
    }
  }

  /// Map user-selected adhan sound to raw resource name
  String? _mapToRawResourceName(String selectedSound) {
    // Handle silent notification
    if (selectedSound == 'silent') {
      return null; // No sound for silent notifications
    }
    
    // Map asset file names to raw resource names (without .mp3 extension)
    switch (selectedSound) {
      case 'Adhan_1_short.mp3':
      case 'default_adhan.mp3':
        return 'adhan_1_short';
      case 'Adhan_1_long.mp3':
        return 'adhan_1_short'; // Use short version for notifications
      case 'Adhan_2_short.mp3':
        return 'adhan_2_short'; // Now available!
      case 'Adhan_2_long.mp3':
        return 'adhan_2_short'; // Use short version for notifications
      case 'Adhan_3_short.mp3':
        return 'adhan_3_short'; // Now available!
      case 'Adhan_3_long.mp3':
        return 'adhan_3_short'; // Use short version for notifications
      default:
        print('Unknown adhan sound: $selectedSound, using default');
        return 'adhan_1_short'; // Default fallback
    }
  }

  /// Generate unique notification ID based on date and prayer index
  int _generateNotificationId(DateTime date, int prayerIndex) {
    final dateStr = DateFormat('yyyyMMdd').format(date);
    final dateInt = int.parse(dateStr);
    // Combine date and prayer index to create unique ID
    return dateInt * 10 + prayerIndex;
  }

  /// Cancel notifications for a specific date
  Future<void> _cancelNotificationsForDate(DateTime date) async {
    try {
      for (int i = 0; i < 5; i++) { // 5 prayers
        final notificationId = _generateNotificationId(date, i);
        await _notifications.cancel(notificationId);
      }
    } catch (e) {
      // Ignore errors when canceling individual notifications
    }
  }
}

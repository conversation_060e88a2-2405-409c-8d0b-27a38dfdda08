import 'package:intl/intl.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:hijri/hijri_calendar.dart';

class HijriDateUtils {
  static const Map<String, List<String>> hijriMonthNames = {
    'en': [
      '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>\' al-awwal', '<PERSON><PERSON>\' al-thani',
      '<PERSON><PERSON> al-awwal', '<PERSON><PERSON> al-thani', '<PERSON><PERSON>', '<PERSON>ha\'ban',
      '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON> al-Qi\'dah', '<PERSON>hu al-Hijjah'
    ],
    'de': [
      '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>\' al-awwal', '<PERSON><PERSON>\' al-thani',
      '<PERSON><PERSON> al-awwal', '<PERSON><PERSON> al-thani', '<PERSON><PERSON>', '<PERSON>ha\'ban',
      '<PERSON><PERSON>', '<PERSON>wal', '<PERSON>hu al-Qi\'dah', '<PERSON>hu al-Hijjah'
    ],
    'hr': [
      '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>\' el-evvel', '<PERSON><PERSON>\' el-ahir',
      '<PERSON><PERSON><PERSON><PERSON> el-ula', '<PERSON><PERSON><PERSON><PERSON>-<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>\'ban',
      '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>-<PERSON>-<PERSON>\'de', '<PERSON>u-l-hidždže'
    ],
    'fr': [
      'Mouharram', 'Safar', 'Rabi\' al-awwal', 'Rabi\' al-thani',
      'Joumada al-oula', 'Joumada ath-thania', 'Rajab', 'Cha\'ban',
      'Ramadan', 'Chawwal', 'Dhou al-qi\'da', 'Dhou al-hijja'
    ],
    'ar': [
      'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني',
      'جمادى الأولى', 'جمادى الثانية', 'رجب', 'شعبان',
      'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
    ],
  };

  static const Map<String, List<String>> weekdayNames = {
    'en': ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    'de': ['Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa', 'So'],
    'hr': ['Pon', 'Uto', 'Sri', 'Čet', 'Pet', 'Sub', 'Ned'],
    'fr': ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
    'ar': ['الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'],
  };

  /// Get today's date in local timezone (date-only, midnight local)
  /// This ensures no DST/UTC side effects for date calculations
  static DateTime getTodayLocal() {
    try {
      // Get current local time and create date-only object
      final now = DateTime.now();
      // Return date-only (midnight local) to avoid DST/UTC issues
      return DateTime(now.year, now.month, now.day);
    } catch (e) {
      // Fallback to system local time
      final now = DateTime.now();
      return DateTime(now.year, now.month, now.day);
    }
  }

  /// Get Hijri date using Umm al-Qura calendar with optional offset
  /// Uses the hijri package which implements official Umm al-Qura tables
  static HijriCalendar getHijriDate(DateTime gregorianDate, {int offset = 0}) {
    try {
      // Ensure we use date-only (midnight local) to avoid DST issues
      final dateOnly = DateTime(gregorianDate.year, gregorianDate.month, gregorianDate.day);

      // Convert using Umm al-Qura (hijri package uses official KSA tables)
      final hijriDate = HijriCalendar.fromDate(dateOnly);

      // Apply offset if specified (visual adjustment only)
      if (offset != 0) {
        // Create new HijriCalendar with offset applied
        final offsetDate = dateOnly.add(Duration(days: offset));
        return HijriCalendar.fromDate(offsetDate);
      }

      return hijriDate;
    } catch (e) {
      // Fallback for edge cases
      return _fallbackHijriCalculation(gregorianDate, offset);
    }
  }

  /// Get formatted Gregorian date string
  /// Format: "So., 24. Aug. 2025"
  static String getGregorianDateString(DateTime date, String languageCode) {
    final weekday = weekdayNames[languageCode]?[date.weekday - 1] ??
                   weekdayNames['en']![date.weekday - 1];

    String format;
    switch (languageCode) {
      case 'de':
        format = 'd. MMM yyyy';
        break;
      case 'hr':
        format = 'd. MMM yyyy';
        break;
      case 'fr':
        format = 'd MMM yyyy';
        break;
      case 'ar':
        format = 'd MMM yyyy';
        break;
      default:
        format = 'd MMM yyyy';
    }

    final gregorianDate = DateFormat(format).format(date);
    return '$weekday, $gregorianDate';
  }

  /// Get formatted Hijri date string using Umm al-Qura
  /// Format: "29 Ṣafar 1447 AH (Umm al-Qura)"
  static String getHijriDateString(DateTime date, String languageCode, {int offset = 0}) {
    try {
      final hijriDate = getHijriDate(date, offset: offset);
      final hijriMonthNames = HijriDateUtils.hijriMonthNames[languageCode] ??
                             HijriDateUtils.hijriMonthNames['en']!;
      final hijriMonthName = hijriMonthNames[hijriDate.hMonth - 1];

      return '${hijriDate.hDay} $hijriMonthName ${hijriDate.hYear} AH (Umm al-Qura)';
    } catch (e) {
      // Fallback if Hijri calculation fails
      return 'Hijri date unavailable';
    }
  }

  /// Check if current date is in Ramadan using Umm al-Qura
  static bool isRamadan([DateTime? date, int offset = 0]) {
    try {
      final checkDate = date ?? getTodayLocal();
      final hijriDate = getHijriDate(checkDate, offset: offset);
      return hijriDate.hMonth == 9; // Ramadan is the 9th month
    } catch (e) {
      return false;
    }
  }

  /// Get Ramadan blessing text in different languages
  static String getRamadanBlessingText(String languageCode) {
    switch (languageCode) {
      case 'de':
        return 'Gesegneter Monat des Fastens';
      case 'hr':
        return 'Blagoslovljeni mjesec posta';
      case 'fr':
        return 'Mois béni du jeûne';
      case 'ar':
        return 'شهر الصيام المبارك';
      default:
        return 'Blessed month of fasting';
    }
  }

  /// Get current Islamic month name using Umm al-Qura
  static String getCurrentIslamicMonth(String languageCode, {int offset = 0}) {
    try {
      final hijriDate = getHijriDate(getTodayLocal(), offset: offset);
      final hijriMonthNames = HijriDateUtils.hijriMonthNames[languageCode] ??
                             HijriDateUtils.hijriMonthNames['en']!;
      return hijriMonthNames[hijriDate.hMonth - 1];
    } catch (e) {
      return 'Unknown';
    }
  }

  /// Fallback Hijri calculation for edge cases
  static HijriCalendar _fallbackHijriCalculation(DateTime gregorianDate, int offset) {
    try {
      // Use basic HijriCalendar as fallback
      final dateOnly = DateTime(gregorianDate.year, gregorianDate.month, gregorianDate.day);
      final offsetDate = offset != 0 ? dateOnly.add(Duration(days: offset)) : dateOnly;
      return HijriCalendar.fromDate(offsetDate);
    } catch (e) {
      // Ultimate fallback - return current Hijri date
      return HijriCalendar.now();
    }
  }

  /// Reference test data from official Umm al-Qura calendar (KSA)
  /// Source: https://www.ummulqura.org.sa/
  static final List<Map<String, dynamic>> _referenceData = [
    {
      'gregorian': DateTime(2024, 1, 1),
      'hijri': {'year': 1445, 'month': 6, 'day': 19}, // 19 Jumada al-thani 1445
      'description': '1 Jan 2024 = 19 Jumada al-thani 1445',
    },
    {
      'gregorian': DateTime(2024, 3, 11),
      'hijri': {'year': 1445, 'month': 9, 'day': 1}, // 1 Ramadan 1445
      'description': '11 Mar 2024 = 1 Ramadan 1445',
    },
    {
      'gregorian': DateTime(2024, 4, 10),
      'hijri': {'year': 1445, 'month': 10, 'day': 1}, // 1 Shawwal 1445 (Eid al-Fitr)
      'description': '10 Apr 2024 = 1 Shawwal 1445 (Eid al-Fitr)',
    },
    {
      'gregorian': DateTime(2024, 6, 17),
      'hijri': {'year': 1445, 'month': 12, 'day': 10}, // 10 Dhu al-Hijjah 1445 (Eid al-Adha)
      'description': '17 Jun 2024 = 10 Dhu al-Hijjah 1445 (Eid al-Adha)',
    },
    {
      'gregorian': DateTime(2024, 7, 7),
      'hijri': {'year': 1446, 'month': 1, 'day': 1}, // 1 Muharram 1446 (New Hijri Year)
      'description': '7 Jul 2024 = 1 Muharram 1446 (New Hijri Year)',
    },
  ];

  /// Validate Hijri conversion against official Umm al-Qura reference data
  static Map<String, dynamic> validateHijriConversion({int offset = 0}) {
    final results = <String, dynamic>{
      'passed': 0,
      'failed': 0,
      'total': _referenceData.length,
      'details': <Map<String, dynamic>>[],
    };

    for (final ref in _referenceData) {
      try {
        final gregorian = ref['gregorian'] as DateTime;
        final expectedHijri = ref['hijri'] as Map<String, int>;
        final description = ref['description'] as String;
        final actualHijri = getHijriDate(gregorian, offset: offset);

        final isMatch = actualHijri.hYear == expectedHijri['year'] &&
                       actualHijri.hMonth == expectedHijri['month'] &&
                       actualHijri.hDay == expectedHijri['day'];

        if (isMatch) {
          results['passed']++;
        } else {
          results['failed']++;
        }

        results['details'].add({
          'description': description,
          'expected': '${expectedHijri['day']} ${_getMonthName(expectedHijri['month']!)} ${expectedHijri['year']}',
          'actual': '${actualHijri.hDay} ${_getMonthName(actualHijri.hMonth)} ${actualHijri.hYear}',
          'passed': isMatch,
          'offset': offset,
        });
      } catch (e) {
        results['failed']++;
        results['details'].add({
          'description': ref['description'],
          'error': e.toString(),
          'passed': false,
        });
      }
    }

    return results;
  }

  static String _getMonthName(int month) {
    const monthNames = [
      'Muharram', 'Safar', 'Rabi\' al-awwal', 'Rabi\' al-thani',
      'Jumada al-awwal', 'Jumada al-thani', 'Rajab', 'Sha\'ban',
      'Ramadan', 'Shawwal', 'Dhu al-Qi\'dah', 'Dhu al-Hijjah'
    ];
    return monthNames[month - 1];
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/app_config.dart';
import '../../../../core/theme/app_themes.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../bloc/settings_bloc.dart';

class DesignSelector extends StatelessWidget {
  const DesignSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return GlassmorphismContainer(
      forceTransparent: true,
      borderRadius: 20,
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'Select Design Style',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: AppConfig.smallPadding),
          Text(
            'Choose between fresh emerald or warm latte design',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          BlocBuilder<SettingsBloc, SettingsState>(
            builder: (context, state) {
              return Column(
                children: AppDesign.values.map((design) {
                  final isSelected = state.appDesign == design;
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () {
                          context.read<SettingsBloc>().add(UpdateAppDesign(appDesign: design));
                          Navigator.pop(context);
                        },
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: isSelected 
                                  ? Theme.of(context).colorScheme.primary
                                  : Colors.grey.withOpacity(0.3),
                              width: isSelected ? 2 : 1,
                            ),
                            borderRadius: BorderRadius.circular(12),
                            color: isSelected 
                                ? Theme.of(context).colorScheme.primary.withOpacity(0.05)
                                : null,
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 50,
                                height: 50,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: isSelected 
                                      ? Theme.of(context).colorScheme.primary
                                      : _getDesignPreviewColor(design),
                                ),
                                child: Icon(
                                  _getDesignIcon(design),
                                  color: isSelected 
                                      ? Colors.white 
                                      : Colors.white,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _getDesignName(design),
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                        color: isSelected 
                                            ? Theme.of(context).colorScheme.primary
                                            : null,
                                      ),
                                    ),
                                    const SizedBox(height: 2),
                                    Text(
                                      _getDesignDescription(design),
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (isSelected)
                                Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).colorScheme.primary,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: const Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              );
            },
          ),
          const SizedBox(height: AppConfig.smallPadding),
          // Preview cards showing the design styles
          _buildPreviewSection(context),
        ],
      ),
    );
  }

  Widget _buildPreviewSection(BuildContext context) {
    return BlocBuilder<SettingsBloc, SettingsState>(
      builder: (context, state) {
        return Column(
          children: [
            const Divider(),
            const SizedBox(height: 16),
            Text(
              'Preview',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: AppDesign.values.map((design) {
                final isSelected = state.appDesign == design;
                return Expanded(
                  child: Container(
                    margin: EdgeInsets.only(
                      right: design == AppDesign.emerald ? 8 : 0,
                      left: design == AppDesign.latte ? 8 : 0,
                    ),
                    child: Column(
                      children: [
                        Container(
                          height: 60,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isSelected 
                                  ? Theme.of(context).colorScheme.primary
                                  : Colors.grey.withOpacity(0.3),
                              width: isSelected ? 2 : 1,
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(7),
                            child: _buildMiniPreview(context, design),
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          _getDesignName(design),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            color: isSelected 
                                ? Theme.of(context).colorScheme.primary
                                : null,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMiniPreview(BuildContext context, AppDesign design) {
    final theme = getAppTheme(design, context);
    
    return Container(
      color: theme.colorScheme.surface,
      child: Column(
        children: [
          // App bar mini
          Container(
            height: 20,
            color: theme.appBarTheme.backgroundColor,
            child: Center(
              child: Container(
                width: 40,
                height: 2,
                color: theme.appBarTheme.foregroundColor,
              ),
            ),
          ),
          // Content area
          Expanded(
            child: Container(
              color: theme.colorScheme.surface,
              padding: const EdgeInsets.all(4),
              child: Column(
                children: [
                  Container(
                    height: 12,
                    margin: const EdgeInsets.only(bottom: 2),
                    decoration: BoxDecoration(
                      color: theme.cardTheme.color,
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: Center(
                      child: Container(
                        width: 20,
                        height: 1,
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ),
                  Container(
                    height: 12,
                    decoration: BoxDecoration(
                      color: theme.cardTheme.color,
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: Center(
                      child: Container(
                        width: 24,
                        height: 1,
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getDesignName(AppDesign design) {
    switch (design) {
      case AppDesign.emerald:
        return 'Emerald Light';
      case AppDesign.latte:
        return 'Latte Cream & Gold';
    }
  }

  String _getDesignDescription(AppDesign design) {
    switch (design) {
      case AppDesign.emerald:
        return 'Fresh, elegant green design with round cards';
      case AppDesign.latte:
        return 'Warm latte with gold accents - living ambience';
    }
  }

  IconData _getDesignIcon(AppDesign design) {
    switch (design) {
      case AppDesign.emerald:
        return Icons.eco_outlined;
      case AppDesign.latte:
        return Icons.coffee_outlined;
    }
  }

  Color _getDesignPreviewColor(AppDesign design) {
    switch (design) {
      case AppDesign.emerald:
        return AppPalettes.emeraldPrimary;
      case AppDesign.latte:
        return AppPalettes.latteSecondary; // Gold color
    }
  }
}
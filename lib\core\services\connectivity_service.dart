import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

abstract class ConnectivityService {
  Future<bool> get isConnected;
  Stream<bool> get connectivityStream;
}

class ConnectivityServiceImpl implements ConnectivityService {
  final Connectivity _connectivity;
  StreamController<bool>? _connectivityController;

  ConnectivityServiceImpl() : _connectivity = Connectivity() {
    _initConnectivityStream();
  }

  void _initConnectivityStream() {
    _connectivityController = StreamController<bool>.broadcast();

    _connectivity.onConnectivityChanged.listen((List<ConnectivityResult> results) {
      final isConnected = results.isNotEmpty && !results.contains(ConnectivityResult.none);
      _connectivityController?.add(isConnected);
    });
  }

  @override
  Future<bool> get isConnected async {
    final results = await _connectivity.checkConnectivity();
    return results.isNotEmpty && !results.contains(ConnectivityResult.none);
  }

  @override
  Stream<bool> get connectivityStream {
    return _connectivityController?.stream ?? Stream.value(false);
  }

  void dispose() {
    _connectivityController?.close();
  }
}

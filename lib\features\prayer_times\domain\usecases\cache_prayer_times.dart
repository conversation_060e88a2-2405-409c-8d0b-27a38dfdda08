import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/prayer_times.dart';
import '../repositories/prayer_times_repository.dart';

class CachePrayerTimes implements UseCase<List<PrayerTimes>, CachePrayerTimesParams> {
  final PrayerTimesRepository repository;

  CachePrayerTimes(this.repository);

  @override
  Future<Either<Failure, List<PrayerTimes>>> call(CachePrayerTimesParams params) async {
    // Get monthly prayer times from API
    final result = await repository.getMonthlyPrayerTimes(
      month: params.month,
      latitude: params.latitude,
      longitude: params.longitude,
      calculationMethod: params.calculationMethod,
    );

    return result.fold(
      (failure) => Left(failure),
      (prayerTimesList) async {
        // Cache the monthly data
        final cacheKey = _generateCacheKey(
          params.latitude,
          params.longitude,
          params.calculationMethod,
        );

        final cacheResult = await repository.cachePrayerTimes(
          prayerTimes: prayerTimesList,
          cacheKey: cacheKey,
        );

        return cacheResult.fold(
          (failure) => Left(failure),
          (_) => Right(prayerTimesList),
        );
      },
    );
  }

  String _generateCacheKey(double latitude, double longitude, int method) {
    return '${latitude.toStringAsFixed(4)}_${longitude.toStringAsFixed(4)}_$method';
  }
}

class CachePrayerTimesParams extends Equatable {
  final DateTime month;
  final double latitude;
  final double longitude;
  final int calculationMethod;

  const CachePrayerTimesParams({
    required this.month,
    required this.latitude,
    required this.longitude,
    required this.calculationMethod,
  });

  @override
  List<Object> get props => [month, latitude, longitude, calculationMethod];
}

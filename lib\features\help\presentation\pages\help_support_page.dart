import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';
import '../../../../core/l10n/app_localizations.dart';
import '../../../../core/theme/design_tokens.dart';
import '../../../../core/widgets/glassmorphism_card.dart';

class HelpSupportPage extends StatefulWidget {
  const HelpSupportPage({super.key});

  @override
  State<HelpSupportPage> createState() => _HelpSupportPageState();
}

class _HelpSupportPageState extends State<HelpSupportPage> {
  PackageInfo? _packageInfo;
  
  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
  }
  
  Future<void> _loadPackageInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _packageInfo = packageInfo;
    });
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.helpSupport),
        backgroundColor: DesignTokens.getCardColor(context),
        foregroundColor: DesignTokens.getTextColor(context),
        elevation: 0,
      ),
      backgroundColor: DesignTokens.getSurfaceColor(context),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(DesignTokens.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // FAQ Section
            _buildFaqSection(context, l10n),
            const SizedBox(height: DesignTokens.spacingXL),
            
            // Contact Section
            _buildContactSection(context, l10n),
            const SizedBox(height: DesignTokens.spacingXL),
            
            // Report Issue Section
            _buildReportSection(context, l10n),
            const SizedBox(height: DesignTokens.spacingXL),
            
            // Legal Section
            _buildLegalSection(context, l10n),
            const SizedBox(height: DesignTokens.spacingXL),
            
            // App Info
            _buildAppInfo(context, l10n),
          ],
        ),
      ),
    );
  }
  
  Widget _buildFaqSection(BuildContext context, AppLocalizations l10n) {
    return GlassmorphismCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.faq,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: DesignTokens.getTextColor(context),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingM),
          
          _buildFaqItem(
            context,
            l10n.faqPrayerTimesAccuracy,
            l10n.faqPrayerTimesAccuracyAnswer,
          ),
          
          _buildFaqItem(
            context,
            l10n.faqHijriDateOffset,
            l10n.faqHijriDateOffsetAnswer,
          ),
          
          _buildFaqItem(
            context,
            l10n.faqNotificationsNotWorking,
            l10n.faqNotificationsNotWorkingAnswer,
          ),
          
          _buildFaqItem(
            context,
            l10n.faqOfflineMode,
            l10n.faqOfflineModeAnswer,
          ),
          
          _buildFaqItem(
            context,
            l10n.faqLocationAccuracy,
            l10n.faqLocationAccuracyAnswer,
          ),
        ],
      ),
    );
  }
  
  Widget _buildFaqItem(BuildContext context, String question, String answer) {
    return ExpansionTile(
      title: Text(
        question,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: DesignTokens.getTextColor(context),
          fontWeight: FontWeight.w500,
        ),
      ),
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(
            DesignTokens.spacingM,
            0,
            DesignTokens.spacingM,
            DesignTokens.spacingM,
          ),
          child: Text(
            answer,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: DesignTokens.getTextColor(context, secondary: true),
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildContactSection(BuildContext context, AppLocalizations l10n) {
    return GlassmorphismCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.contact,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: DesignTokens.getTextColor(context),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingM),
          
          ListTile(
            leading: const Icon(Icons.email_outlined),
            title: Text(l10n.contactEmail),
            subtitle: Text(l10n.contactEmailDescription),
            onTap: () => _sendEmail(context, l10n),
          ),
          
          ListTile(
            leading: const Icon(Icons.language),
            title: Text(l10n.website),
            subtitle: const Text('https://gebet-app.com'),
            onTap: () => _launchUrl('https://gebet-app.com'),
          ),
        ],
      ),
    );
  }
  
  Widget _buildReportSection(BuildContext context, AppLocalizations l10n) {
    return GlassmorphismCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.reportIssue,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: DesignTokens.getTextColor(context),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingM),
          
          ListTile(
            leading: const Icon(Icons.bug_report_outlined),
            title: Text(l10n.reportBug),
            subtitle: Text(l10n.reportBugDescription),
            onTap: () => _reportIssue(context, l10n),
          ),
          
          ListTile(
            leading: const Icon(Icons.feedback_outlined),
            title: Text(l10n.sendFeedback),
            subtitle: Text(l10n.sendFeedbackDescription),
            onTap: () => _sendFeedback(context, l10n),
          ),
        ],
      ),
    );
  }
  
  Widget _buildLegalSection(BuildContext context, AppLocalizations l10n) {
    return GlassmorphismCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.legal,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: DesignTokens.getTextColor(context),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingM),
          
          ListTile(
            leading: const Icon(Icons.privacy_tip_outlined),
            title: Text(l10n.privacyPolicy),
            onTap: () => _launchUrl('https://gebet-app.com/privacy'),
          ),
          
          ListTile(
            leading: const Icon(Icons.description_outlined),
            title: Text(l10n.termsOfService),
            onTap: () => _launchUrl('https://gebet-app.com/terms'),
          ),
          
          ListTile(
            leading: const Icon(Icons.info_outlined),
            title: Text(l10n.licenses),
            onTap: () => _showLicenses(context),
          ),
        ],
      ),
    );
  }
  
  Widget _buildAppInfo(BuildContext context, AppLocalizations l10n) {
    return GlassmorphismCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.appInfo,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: DesignTokens.getTextColor(context),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingM),
          
          if (_packageInfo != null) ...[
            _buildInfoRow(l10n.version, '${_packageInfo!.version} (${_packageInfo!.buildNumber})'),
            _buildInfoRow(l10n.appName, _packageInfo!.appName),
            _buildInfoRow(l10n.packageName, _packageInfo!.packageName),
          ],
          
          _buildInfoRow(l10n.platform, Platform.operatingSystem),
          _buildInfoRow(l10n.buildDate, _getBuildDate()),
        ],
      ),
    );
  }
  
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: DesignTokens.spacingXS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: DesignTokens.getTextColor(context, secondary: true),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: DesignTokens.getTextColor(context),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Future<void> _sendEmail(BuildContext context, AppLocalizations l10n) async {
    final deviceInfo = await _getDeviceInfo();
    final subject = Uri.encodeComponent('${l10n.appName} Support - v${_packageInfo?.version ?? 'Unknown'} - $deviceInfo');
    final body = Uri.encodeComponent(l10n.emailTemplate);
    
    final emailUrl = 'mailto:<EMAIL>?subject=$subject&body=$body';
    
    if (await canLaunchUrl(Uri.parse(emailUrl))) {
      await launchUrl(Uri.parse(emailUrl));
    } else {
      _showEmailFallback(context, l10n, '<EMAIL>', subject, body);
    }
  }
  
  Future<void> _reportIssue(BuildContext context, AppLocalizations l10n) async {
    final diagnostics = await _generateDiagnostics();
    
    await Share.share(
      diagnostics,
      subject: '${l10n.appName} - ${l10n.reportBug}',
    );
  }
  
  Future<void> _sendFeedback(BuildContext context, AppLocalizations l10n) async {
    final deviceInfo = await _getDeviceInfo();
    final subject = Uri.encodeComponent('${l10n.appName} Feedback - v${_packageInfo?.version ?? 'Unknown'}');
    final body = Uri.encodeComponent('${l10n.feedbackTemplate}\n\n---\nDevice: $deviceInfo');
    
    final emailUrl = 'mailto:<EMAIL>?subject=$subject&body=$body';
    
    if (await canLaunchUrl(Uri.parse(emailUrl))) {
      await launchUrl(Uri.parse(emailUrl));
    } else {
      _showEmailFallback(context, l10n, '<EMAIL>', subject, body);
    }
  }
  
  Future<void> _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
  
  void _showLicenses(BuildContext context) {
    showLicensePage(
      context: context,
      applicationName: _packageInfo?.appName ?? 'AlFalah App',
      applicationVersion: _packageInfo?.version ?? '1.0.0',
    );
  }
  
  void _showEmailFallback(BuildContext context, AppLocalizations l10n, String email, String subject, String body) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.noEmailClient),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(l10n.noEmailClientDescription),
            const SizedBox(height: DesignTokens.spacingM),
            SelectableText(email),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: email));
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(l10n.emailCopied)),
              );
            },
            child: Text(l10n.copyEmail),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.close),
          ),
        ],
      ),
    );
  }
  
  Future<String> _getDeviceInfo() async {
    return '${Platform.operatingSystem} ${Platform.operatingSystemVersion}';
  }
  
  Future<String> _generateDiagnostics() async {
    final deviceInfo = await _getDeviceInfo();
    final now = DateTime.now().toIso8601String();
    
    return '''
${AppLocalizations.of(context)!.appName} - Bug Report

Generated: $now
Version: ${_packageInfo?.version ?? 'Unknown'} (${_packageInfo?.buildNumber ?? 'Unknown'})
Platform: $deviceInfo

Please describe the issue:
[Describe what happened and what you expected to happen]

Steps to reproduce:
1. [First step]
2. [Second step]
3. [Third step]

Additional information:
[Any other relevant information]
''';
  }
  
  String _getBuildDate() {
    // This would typically come from build configuration
    return DateTime.now().toString().split(' ')[0];
  }
}

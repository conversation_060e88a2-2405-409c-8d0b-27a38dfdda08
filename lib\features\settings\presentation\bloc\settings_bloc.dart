import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/app_settings.dart';
import '../../domain/usecases/get_settings.dart';
import '../../domain/usecases/save_settings.dart';

part 'settings_event.dart';
part 'settings_state.dart';

class SettingsBloc extends Bloc<SettingsEvent, SettingsState> {
  final GetSettings getSettings;
  final SaveSettings saveSettings;

  SettingsBloc({
    required this.getSettings,
    required this.saveSettings,
  }) : super(const SettingsState(isLoading: true)) {
    on<LoadSettings>(_onLoadSettings);
    on<UpdateThemeMode>(_onUpdateThemeMode);
    on<UpdateAppDesign>(_onUpdateAppDesign);
    on<UpdateLocale>(_onUpdateLocale);
    on<UpdateCalculationMethod>(_onUpdateCalculationMethod);
    on<UpdateNotificationsEnabled>(_onUpdateNotificationsEnabled);
    on<UpdateAdhanEnabled>(_onUpdateAdhanEnabled);
    on<UpdateAdhanSound>(_onUpdateAdhanSound);
    on<UpdateHijriOffset>(_onUpdateHijriOffset);

    on<UpdateAutoLocation>(_onUpdateAutoLocation);
    on<UpdateCustomLocation>(_onUpdateCustomLocation);
    on<ResetSettings>(_onResetSettings);
  }

  Future<void> _onLoadSettings(
    LoadSettings event,
    Emitter<SettingsState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    final result = await getSettings(const NoParams());

    result.fold(
      (failure) => emit(state.copyWith(
        isLoading: false,
        error: failure.message,
      )),
      (settings) => emit(SettingsState(
        settings: settings,
        isLoading: false,
      )),
    );
  }

  Future<void> _onUpdateThemeMode(
    UpdateThemeMode event,
    Emitter<SettingsState> emit,
  ) async {
    final updatedSettings = state.settings.copyWith(themeMode: event.themeMode);
    await _saveSettings(updatedSettings, emit);
  }

  Future<void> _onUpdateAppDesign(
    UpdateAppDesign event,
    Emitter<SettingsState> emit,
  ) async {
    final updatedSettings = state.settings.copyWith(appDesign: event.appDesign);
    await _saveSettings(updatedSettings, emit);
  }

  Future<void> _onUpdateLocale(
    UpdateLocale event,
    Emitter<SettingsState> emit,
  ) async {
    final updatedSettings = state.settings.copyWith(locale: event.locale);
    await _saveSettings(updatedSettings, emit);
  }

  Future<void> _onUpdateCalculationMethod(
    UpdateCalculationMethod event,
    Emitter<SettingsState> emit,
  ) async {
    final updatedSettings = state.settings.copyWith(calculationMethod: event.method);
    await _saveSettings(updatedSettings, emit);
  }

  Future<void> _onUpdateNotificationsEnabled(
    UpdateNotificationsEnabled event,
    Emitter<SettingsState> emit,
  ) async {
    final updatedSettings = state.settings.copyWith(notificationsEnabled: event.enabled);
    await _saveSettings(updatedSettings, emit);
  }

  Future<void> _onUpdateAdhanEnabled(
    UpdateAdhanEnabled event,
    Emitter<SettingsState> emit,
  ) async {
    final updatedSettings = state.settings.copyWith(adhanEnabled: event.enabled);
    await _saveSettings(updatedSettings, emit);
  }

  Future<void> _onUpdateAdhanSound(
    UpdateAdhanSound event,
    Emitter<SettingsState> emit,
  ) async {
    final updatedSettings = state.settings.copyWith(adhanSound: event.sound);
    await _saveSettings(updatedSettings, emit);
  }

  Future<void> _onUpdateHijriOffset(
    UpdateHijriOffset event,
    Emitter<SettingsState> emit,
  ) async {
    // Clamp offset to valid range (-3 to +3)
    final clampedOffset = event.offset.clamp(-3, 3);
    final updatedSettings = state.settings.copyWith(hijriOffset: clampedOffset);
    await _saveSettings(updatedSettings, emit);
  }

  Future<void> _onUpdateAutoLocation(
    UpdateAutoLocation event,
    Emitter<SettingsState> emit,
  ) async {
    final updatedSettings = state.settings.copyWith(autoLocation: event.enabled);
    await _saveSettings(updatedSettings, emit);
  }

  Future<void> _onUpdateCustomLocation(
    UpdateCustomLocation event,
    Emitter<SettingsState> emit,
  ) async {
    final updatedSettings = state.settings.copyWith(
      customLatitude: event.latitude,
      customLongitude: event.longitude,
      customLocationName: event.locationName,
    );
    await _saveSettings(updatedSettings, emit);
  }

  Future<void> _onResetSettings(
    ResetSettings event,
    Emitter<SettingsState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    // Reset to system defaults
    final defaultSettings = AppSettings.withSystemDefaults();
    await _saveSettings(defaultSettings, emit);
  }

  Future<void> _saveSettings(AppSettings settings, Emitter<SettingsState> emit) async {
    final result = await saveSettings(SaveSettingsParams(settings: settings));

    result.fold(
      (failure) => emit(state.copyWith(
        isLoading: false,
        error: failure.message,
      )),
      (_) => emit(state.copyWith(
        settings: settings,
        isLoading: false,
        error: null,
      )),
    );
  }
}

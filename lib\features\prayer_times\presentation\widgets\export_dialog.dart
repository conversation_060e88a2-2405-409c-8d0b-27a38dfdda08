import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/app_config.dart';
import '../../../../core/l10n/app_localizations.dart';
import '../../../../core/theme/design_tokens.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../../../settings/presentation/bloc/settings_bloc.dart';
import '../../domain/entities/prayer_times.dart';
import '../../export/prayer_times_exporter.dart';
import '../bloc/prayer_view_state.dart';

class ExportDialog extends StatefulWidget {
  final List<PrayerTimes> prayerTimesList;
  final PrayerViewType viewType;
  final String cityName;

  const ExportDialog({
    super.key,
    required this.prayerTimesList,
    required this.viewType,
    this.cityName = 'Current Location',
  });

  @override
  State<ExportDialog> createState() => _ExportDialogState();
}

class _ExportDialogState extends State<ExportDialog> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Dialog(
      backgroundColor: DesignTokens.getCardColor(context),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusL),
      ),
      child: Container(
        padding: const EdgeInsets.all(DesignTokens.spacingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: DesignTokens.getTextColor(context, secondary: true),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: DesignTokens.spacingM),

            // Title
            Text(
              l10n.exportShare,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: DesignTokens.getTextColor(context),
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: DesignTokens.spacingXS),

            // Subtitle
            Text(
              l10n.exportSubtitle(widget.prayerTimesList.length),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: DesignTokens.getTextColor(context, secondary: true),
              ),
            ),
            const SizedBox(height: DesignTokens.spacingM),
            
            if (_isLoading) ...[
              const CircularProgressIndicator(),
              const SizedBox(height: DesignTokens.spacingM),
              Text(
                l10n.preparingExport,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: DesignTokens.getTextColor(context, secondary: true),
                ),
              ),
            ] else ...[
              // Export options
              _buildExportOption(
                context,
                icon: Icons.picture_as_pdf,
                title: l10n.exportPdf,
                subtitle: l10n.exportPdfSubtitle,
                onTap: () => _exportPDF(context),
              ),
              const SizedBox(height: DesignTokens.spacingS),

              _buildExportOption(
                context,
                icon: Icons.table_chart,
                title: l10n.exportCsv,
                subtitle: l10n.exportCsvSubtitle,
                onTap: () => _exportCSV(context),
              ),
              const SizedBox(height: DesignTokens.spacingS),

              _buildExportOption(
                context,
                icon: Icons.share,
                title: l10n.sharePdf,
                subtitle: l10n.sharePdfSubtitle,
                onTap: () => _sharePDF(context),
              ),
              const SizedBox(height: DesignTokens.spacingS),

              _buildExportOption(
                context,
                icon: Icons.print,
                title: l10n.print,
                subtitle: l10n.printSubtitle,
                onTap: () => _printPrayerTimes(context),
              ),
              const SizedBox(height: DesignTokens.spacingS),

              _buildExportOption(
                context,
                icon: Icons.crop_rotate,
                title: l10n.exportPdfLandscape,
                subtitle: l10n.exportPdfLandscapeSubtitle,
                onTap: () => _exportPDFLandscape(context),
              ),
            ],
            
            const SizedBox(height: DesignTokens.spacingL),

            // Close button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () => Navigator.pop(context),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: DesignTokens.spacingM),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.radiusM),
                  ),
                ),
                child: Text(l10n.close),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExportOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        child: Container(
          padding: const EdgeInsets.all(DesignTokens.spacingM),
          decoration: BoxDecoration(
            color: DesignTokens.isDarkMode(context)
                ? Colors.white.withOpacity(0.05)
                : Colors.black.withOpacity(0.02),
            borderRadius: BorderRadius.circular(DesignTokens.radiusM),
            border: Border.all(
              color: DesignTokens.lightDivider,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(DesignTokens.spacingS + 2),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(DesignTokens.radiusS + 2),
                ),
                child: Icon(
                  icon,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: DesignTokens.spacingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: DesignTokens.getTextColor(context),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: DesignTokens.getTextColor(context, secondary: true),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: DesignTokens.getTextColor(context, secondary: true),
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _exportPDF(BuildContext context) async {
    await _performExportAction(() async {
      final settingsState = context.read<SettingsBloc>().state;

      await PrayerTimesExporter.exportToPdf(
        context: context,
        prayerTimesList: widget.prayerTimesList,
        viewType: _getViewTypeString(),
        cityName: widget.cityName,
        hijriOffset: settingsState.hijriOffset,
        landscape: false,
      );
    });
  }

  Future<void> _exportPDFLandscape(BuildContext context) async {
    await _performExportAction(() async {
      final settingsState = context.read<SettingsBloc>().state;

      await PrayerTimesExporter.exportToPdf(
        context: context,
        prayerTimesList: widget.prayerTimesList,
        viewType: _getViewTypeString(),
        cityName: widget.cityName,
        hijriOffset: settingsState.hijriOffset,
        landscape: true,
      );
    });
  }

  Future<void> _exportCSV(BuildContext context) async {
    await _performExportAction(() async {
      final settingsState = context.read<SettingsBloc>().state;

      await PrayerTimesExporter.exportToCsv(
        context: context,
        prayerTimesList: widget.prayerTimesList,
        viewType: _getViewTypeString(),
        cityName: widget.cityName,
        hijriOffset: settingsState.hijriOffset,
      );
    });
  }

  Future<void> _sharePDF(BuildContext context) async {
    await _performExportAction(() async {
      final settingsState = context.read<SettingsBloc>().state;

      await PrayerTimesExporter.sharePdf(
        context: context,
        prayerTimesList: widget.prayerTimesList,
        viewType: _getViewTypeString(),
        cityName: widget.cityName,
        hijriOffset: settingsState.hijriOffset,
        landscape: false,
      );
    });
  }

  Future<void> _printPrayerTimes(BuildContext context) async {
    await _performExportAction(() async {
      final settingsState = context.read<SettingsBloc>().state;

      await PrayerTimesExporter.printPrayerTimes(
        context: context,
        prayerTimesList: widget.prayerTimesList,
        viewType: _getViewTypeString(),
        cityName: widget.cityName,
        hijriOffset: settingsState.hijriOffset,
        landscape: false,
      );
    });
  }

  Future<void> _performExportAction(Future<void> Function() action) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await action();
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Export completed successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _getTitle() {
    switch (widget.viewType) {
      case PrayerViewType.today:
        return 'Today\'s Prayer Times';
      case PrayerViewType.week:
        return 'Weekly Prayer Times';
      case PrayerViewType.month:
        return 'Monthly Prayer Times';
    }
  }

  String _getViewTypeString() {
    switch (widget.viewType) {
      case PrayerViewType.today:
        return 'today';
      case PrayerViewType.week:
        return 'week';
      case PrayerViewType.month:
        return 'month';
    }
  }

  String _getExportViewTypeLabeleString() {
    switch (widget.viewType) {
      case PrayerViewType.today:
        return 'Today';
      case PrayerViewType.week:
        return 'Week';
      case PrayerViewType.month:
        return 'Month';
    }
  }
}



import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// App Design variants
enum AppDesign { emerald, latte }

/// Color palettes for both design variants
class AppPalettes {
  // Emerald Light (Design 1 - Fresh elegant green)
  static const emeraldPrimary        = Color(0xFF0E5F4B);
  static const emeraldOnPrimary      = Colors.white;
  static const emeraldPrimaryCont    = Color(0xFFDBEFE8);
  static const emeraldOnPrimaryCont  = Color(0xFF0E2E26);
  static const emeraldSecondary      = Color(0xFF2E7D6B);
  static const emeraldSecondaryCont  = Color(0xFFE7F3EF);
  static const emeraldBackground     = Color(0xFFF6F8F7);
  static const emeraldSurface        = Color(0xFFFFFFFF);
  static const emeraldSurfaceVar     = Color(0xFFEEF3F1);
  static const emeraldOnSurface      = Color(0xFF1B1F1E);
  static const emeraldOnSurfaceVar   = Color(0xFF5F6B66);
  static const emeraldOutline        = Color(0xFFD8E1DD);
  static const emeraldSuccess        = Color(0xFF3AAE8E);

  // Emerald Dark (Design 1 - Dark mode green)
  static const emeraldDarkPrimary       = Color(0xFF0E5F4B); // Same as light mode
  static const emeraldDarkOnPrimary     = Color(0xFFFFFFFF); // White text on dark green
  static const emeraldDarkPrimaryCont   = Color(0xFF1E4A3B);
  static const emeraldDarkOnPrimaryCont = Color(0xFFDBEFE8);
  static const emeraldDarkSecondary     = Color(0xFF66B29A);
  static const emeraldDarkSecondaryCont = Color(0xFF1A332B);
  static const emeraldDarkBackground    = Color(0xFF0F1410);
  static const emeraldDarkSurface       = Color(0xFF161B17);
  static const emeraldDarkSurfaceVar    = Color(0xFF252B26);
  static const emeraldDarkOnSurface     = Color(0xFFE1E6E2);
  static const emeraldDarkOnSurfaceVar  = Color(0xFF9BA59D);
  static const emeraldDarkOutline       = Color(0xFF3E4A43);
  static const emeraldDarkSuccess       = Color(0xFF5BC4A4);

  // Latte Cream & Gold (Design 2 - Warm living ambience)
  static const lattePrimary          = Color(0xFF8A7B68);
  static const latteOnPrimary        = Colors.white;
  static const lattePrimaryCont      = Color(0xFFD6CEC2);
  static const latteOnPrimaryCont    = Color(0xFF2C2A27);
  static const latteSecondary        = Color(0xFFC7A351);
  static const latteSecondaryCont    = Color(0xFFF1E6C6);
  static const latteBackground       = Color(0xFFFAF7F1);
  static const latteSurface          = Color(0xFFFFFCF6);
  static const latteSurfaceVar       = Color(0xFFF1ECE2);
  static const latteOnSurface        = Color(0xFF2C2A27);
  static const latteOnSurfaceVar     = Color(0xFF7A746B);
  static const latteOutline          = Color(0xFFCFC7BA);

  // Latte Dark (Design 2 - Dark mode warm tones)
  static const latteDarkPrimary       = Color(0xFFDCC79A);
  static const latteDarkOnPrimary     = Color(0xFF0E0D0B);
  static const latteDarkPrimaryCont   = Color(0xFF3A3226);
  static const latteDarkOnPrimaryCont = Color(0xFFEDE9E3);
  static const latteDarkSecondary     = Color(0xFFCFAF56);
  static const latteDarkOnSecondary   = Color(0xFF1A1405);
  static const latteDarkSecondaryCont = Color(0xFF3F3317);
  static const latteDarkOnSecondaryCont = Color(0xFFEDE9E3);
  static const latteDarkBackground    = Color(0xFF151311);
  static const latteDarkSurface       = Color(0xFF1C1916);
  static const latteDarkSurfaceVar    = Color(0xFF2B2620);
  static const latteDarkOnSurface     = Color(0xFFEDE9E3);
  static const latteDarkOnSurfaceVar  = Color(0xFFA59D90);
  static const latteDarkOutline       = Color(0xFF5A5044);
  static const latteDarkSuccess       = Color(0xFFB5922F);
}

/// Common text theme factory
TextTheme _createTextTheme(BuildContext context) =>
    GoogleFonts.robotoTextTheme(Theme.of(context).textTheme).copyWith(
      titleLarge: GoogleFonts.roboto(
        fontWeight: FontWeight.w600,
        fontSize: 20,
        letterSpacing: -0.15,
      ),
      titleMedium: GoogleFonts.roboto(
        fontWeight: FontWeight.w500,
        fontSize: 16,
        letterSpacing: -0.1,
      ),
      bodyLarge: GoogleFonts.roboto(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        height: 1.4,
      ),
      bodyMedium: GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        height: 1.33,
      ),
      labelLarge: GoogleFonts.roboto(
        fontWeight: FontWeight.w600,
        fontSize: 14,
        letterSpacing: 0.1,
      ),
    );

/// Helper function to get theme based on design and brightness
ThemeData getAppTheme(AppDesign design, BuildContext context, {bool isDark = false}) {
  switch (design) {
    case AppDesign.emerald:
      return isDark ? emeraldDarkTheme(context) : emeraldLightTheme(context);
    case AppDesign.latte:
      return isDark ? latteDarkTheme(context) : latteLightTheme(context);
  }
}

/// Emerald Light Theme (Fresh elegant green design)
ThemeData emeraldLightTheme(BuildContext context) {
  const cs = ColorScheme(
    brightness: Brightness.light,
    primary: AppPalettes.emeraldPrimary,
    onPrimary: AppPalettes.emeraldOnPrimary,
    primaryContainer: AppPalettes.emeraldPrimaryCont,
    onPrimaryContainer: AppPalettes.emeraldOnPrimaryCont,
    secondary: AppPalettes.emeraldSecondary,
    onSecondary: Colors.white,
    secondaryContainer: AppPalettes.emeraldSecondaryCont,
    onSecondaryContainer: AppPalettes.emeraldOnSurface,
    tertiary: AppPalettes.emeraldSuccess,
    onTertiary: Colors.white,
    error: Color(0xFFB3261E),
    onError: Colors.white,
    errorContainer: Color(0xFFFFDAD6),
    onErrorContainer: Color(0xFF410002),
    background: AppPalettes.emeraldBackground,
    onBackground: AppPalettes.emeraldOnSurface,
    surface: AppPalettes.emeraldSurface,
    onSurface: AppPalettes.emeraldOnSurface,
    surfaceVariant: AppPalettes.emeraldSurfaceVar,
    onSurfaceVariant: AppPalettes.emeraldOnSurfaceVar,
    outline: AppPalettes.emeraldOutline,
    outlineVariant: AppPalettes.emeraldSurfaceVar,
    inverseSurface: Color(0xFF23302C),
    onInverseSurface: Colors.white,
    inversePrimary: Color(0xFF7FCCB3),
    shadow: Colors.black54,
    scrim: Colors.black54,
  );

  const radius = 16.0;

  return ThemeData(
    useMaterial3: true,
    colorScheme: cs,
    scaffoldBackgroundColor: cs.background,
    textTheme: _createTextTheme(context),
    
    // AppBar with emerald primary
    appBarTheme: AppBarTheme(
      backgroundColor: AppPalettes.emeraldPrimary,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: false,
      titleTextStyle: _createTextTheme(context).titleLarge?.copyWith(
        color: Colors.white,
        fontWeight: FontWeight.w600,
      ),
      surfaceTintColor: Colors.transparent,
    ),
    
    // Cards with rounded corners
    cardTheme: CardThemeData(
      color: cs.surface,
      elevation: 2,
      shadowColor: Colors.grey.shade300,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(radius)),
      margin: const EdgeInsets.all(12),
      surfaceTintColor: Colors.transparent,
    ),
    
    // Button themes
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: cs.primary,
        foregroundColor: cs.onPrimary,
        elevation: 1,
        shadowColor: Colors.grey.shade300,
        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
      ),
    ),
    
    filledButtonTheme: FilledButtonThemeData(
      style: FilledButton.styleFrom(
        backgroundColor: cs.primaryContainer,
        foregroundColor: cs.onPrimaryContainer,
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w500,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
      ),
    ),
    
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: cs.primary,
        side: BorderSide(color: cs.primary, width: 1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w500,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
      ),
    ),
    
    // Chip theme with round corners
    chipTheme: ChipThemeData(
      backgroundColor: cs.secondaryContainer,
      selectedColor: cs.primary,
      disabledColor: cs.surfaceVariant,
      labelStyle: GoogleFonts.roboto(
        color: cs.onSurface,
        fontWeight: FontWeight.w500,
        fontSize: 12,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
      side: BorderSide.none,
      elevation: 0,
    ),
    
    // Input decoration with pill shape for search
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: cs.surfaceVariant,
      hintStyle: GoogleFonts.roboto(
        color: cs.onSurfaceVariant,
        fontSize: 14,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide(color: cs.outline, width: 1),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide(color: cs.outline, width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide(color: cs.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide(color: cs.error, width: 1),
      ),
    ),
    
    // Bottom navigation
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: cs.surface,
      selectedItemColor: cs.primary,
      unselectedItemColor: cs.onSurfaceVariant,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedLabelStyle: GoogleFonts.roboto(
        fontSize: 12,
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: GoogleFonts.roboto(
        fontSize: 12,
        fontWeight: FontWeight.w400,
      ),
    ),
    
    // SnackBar theme
    snackBarTheme: SnackBarThemeData(
      backgroundColor: cs.onSurface,
      contentTextStyle: GoogleFonts.roboto(
        color: cs.surface,
        fontSize: 14,
      ),
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 4,
    ),
    
    // Popup menu theme
    popupMenuTheme: PopupMenuThemeData(
      color: cs.surface,
      elevation: 8,
      shadowColor: Colors.grey.shade300,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: cs.outline, width: 1),
      ),
      textStyle: GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: cs.onSurface,
        letterSpacing: -0.1,
      ),
    ),
    
    // Divider
    dividerColor: cs.outline,
    dividerTheme: DividerThemeData(
      color: cs.outline,
      thickness: 0.5,
      space: 1,
    ),
  );
}

/// Latte Cream & Gold Theme (Warm living ambience design)
ThemeData latteLightTheme(BuildContext context) {
  const cs = ColorScheme(
    brightness: Brightness.light,
    primary: AppPalettes.lattePrimary,
    onPrimary: AppPalettes.latteOnPrimary,
    primaryContainer: AppPalettes.lattePrimaryCont,
    onPrimaryContainer: AppPalettes.latteOnPrimaryCont,
    secondary: AppPalettes.latteSecondary,
    onSecondary: Color(0xFF1A1405),
    secondaryContainer: AppPalettes.latteSecondaryCont,
    onSecondaryContainer: Color(0xFF1A1405),
    tertiary: AppPalettes.latteSecondary, // Gold accents
    onTertiary: Color(0xFF1A1405),
    error: Color(0xFFB3261E),
    onError: Colors.white,
    errorContainer: Color(0xFFFFDAD6),
    onErrorContainer: Color(0xFF410002),
    background: AppPalettes.latteBackground,
    onBackground: AppPalettes.latteOnSurface,
    surface: AppPalettes.latteSurface,
    onSurface: AppPalettes.latteOnSurface,
    surfaceVariant: AppPalettes.latteSurfaceVar,
    onSurfaceVariant: AppPalettes.latteOnSurfaceVar,
    outline: AppPalettes.latteOutline,
    outlineVariant: AppPalettes.latteSurfaceVar,
    inverseSurface: Color(0xFF2A2622),
    onInverseSurface: Colors.white,
    inversePrimary: Color(0xFFBEB39E),
    shadow: Colors.black45,
    scrim: Colors.black54,
  );

  const radius = 16.0;

  return ThemeData(
    useMaterial3: true,
    colorScheme: cs,
    scaffoldBackgroundColor: cs.background,
    textTheme: _createTextTheme(context),
    
    // AppBar with light surface (warm latte style)
    appBarTheme: AppBarTheme(
      backgroundColor: cs.surface,
      foregroundColor: cs.onSurface,
      elevation: 0,
      centerTitle: false,
      titleTextStyle: _createTextTheme(context).titleLarge?.copyWith(
        color: cs.onSurface,
        fontWeight: FontWeight.w600,
      ),
      surfaceTintColor: Colors.transparent,
    ),
    
    // Cards with warm tones
    cardTheme: CardThemeData(
      color: cs.surface,
      elevation: 2,
      shadowColor: Colors.brown.shade100,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(radius)),
      margin: const EdgeInsets.all(12),
      surfaceTintColor: Colors.transparent,
    ),
    
    // Button themes with gold accents
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: cs.secondary, // Gold button
        foregroundColor: const Color(0xFF1A1405),
        elevation: 1,
        shadowColor: Colors.brown.shade100,
        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
      ),
    ),
    
    filledButtonTheme: FilledButtonThemeData(
      style: FilledButton.styleFrom(
        backgroundColor: cs.primaryContainer,
        foregroundColor: cs.onPrimaryContainer,
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w500,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
      ),
    ),
    
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: cs.primary,
        side: BorderSide(color: cs.primary, width: 1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w500,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
      ),
    ),
    
    // Chip theme with warm styling
    chipTheme: ChipThemeData(
      backgroundColor: cs.surfaceVariant,
      selectedColor: cs.secondary,
      disabledColor: cs.outline,
      labelStyle: GoogleFonts.roboto(
        color: cs.onSurface,
        fontWeight: FontWeight.w500,
        fontSize: 12,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
      side: BorderSide.none,
      elevation: 0,
    ),
    
    // Input decoration with pill shape (like image 2)
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: cs.surfaceVariant,
      hintStyle: GoogleFonts.roboto(
        color: cs.onSurfaceVariant,
        fontSize: 14,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(22), // More pill-like
        borderSide: BorderSide(color: cs.outline, width: 1),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(22),
        borderSide: BorderSide(color: cs.outline, width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(22),
        borderSide: BorderSide(color: cs.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(22),
        borderSide: BorderSide(color: cs.error, width: 1),
      ),
    ),
    
    // Bottom navigation with warm tones
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: cs.surface,
      selectedItemColor: cs.primary,
      unselectedItemColor: cs.onSurfaceVariant,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedLabelStyle: GoogleFonts.roboto(
        fontSize: 12,
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: GoogleFonts.roboto(
        fontSize: 12,
        fontWeight: FontWeight.w400,
      ),
    ),
    
    // SnackBar theme
    snackBarTheme: SnackBarThemeData(
      backgroundColor: cs.onSurface,
      contentTextStyle: GoogleFonts.roboto(
        color: cs.surface,
        fontSize: 14,
      ),
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 4,
    ),
    
    // Popup menu theme
    popupMenuTheme: PopupMenuThemeData(
      color: cs.surface,
      elevation: 8,
      shadowColor: Colors.brown.shade100,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: cs.outline, width: 1),
      ),
      textStyle: GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: cs.onSurface,
        letterSpacing: -0.1,
      ),
    ),
    
    // Divider
    dividerColor: cs.outline,
    dividerTheme: DividerThemeData(
      color: cs.outline,
      thickness: 0.5,
      space: 1,
    ),
  );
}

/// Emerald Dark Theme (Dark mode for fresh green design)
ThemeData emeraldDarkTheme(BuildContext context) {
  const cs = ColorScheme(
    brightness: Brightness.dark,
    primary: AppPalettes.emeraldDarkPrimary,
    onPrimary: AppPalettes.emeraldDarkOnPrimary,
    primaryContainer: AppPalettes.emeraldDarkPrimaryCont,
    onPrimaryContainer: AppPalettes.emeraldDarkOnPrimaryCont,
    secondary: AppPalettes.emeraldDarkSecondary,
    onSecondary: Color(0xFF0A1F1A),
    secondaryContainer: AppPalettes.emeraldDarkSecondaryCont,
    onSecondaryContainer: AppPalettes.emeraldDarkOnSurface,
    tertiary: AppPalettes.emeraldDarkSuccess,
    onTertiary: Color(0xFF0A1F1A),
    error: Color(0xFFFFB4AB),
    onError: Color(0xFF690005),
    errorContainer: Color(0xFF93000A),
    onErrorContainer: Color(0xFFFFDAD6),
    background: AppPalettes.emeraldDarkBackground,
    onBackground: AppPalettes.emeraldDarkOnSurface,
    surface: AppPalettes.emeraldDarkSurface,
    onSurface: AppPalettes.emeraldDarkOnSurface,
    surfaceVariant: AppPalettes.emeraldDarkSurfaceVar,
    onSurfaceVariant: AppPalettes.emeraldDarkOnSurfaceVar,
    outline: AppPalettes.emeraldDarkOutline,
    outlineVariant: AppPalettes.emeraldDarkSurfaceVar,
    inverseSurface: Color(0xFFE1E6E2),
    onInverseSurface: AppPalettes.emeraldDarkSurface,
    inversePrimary: AppPalettes.emeraldPrimary,
    shadow: Colors.black,
    scrim: Colors.black,
  );

  const radius = 16.0;

  return ThemeData(
    useMaterial3: true,
    colorScheme: cs,
    scaffoldBackgroundColor: cs.background,
    textTheme: _createTextTheme(context),
    
    // AppBar with emerald dark theme
    appBarTheme: AppBarTheme(
      backgroundColor: cs.surface,
      foregroundColor: cs.onSurface,
      elevation: 0,
      centerTitle: false,
      titleTextStyle: _createTextTheme(context).titleLarge?.copyWith(
        color: cs.onSurface,
        fontWeight: FontWeight.w600,
      ),
      surfaceTintColor: Colors.transparent,
    ),
    
    // Cards with dark styling
    cardTheme: CardThemeData(
      color: cs.surface,
      elevation: 4,
      shadowColor: Colors.black.withOpacity(0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(radius)),
      margin: const EdgeInsets.all(12),
      surfaceTintColor: Colors.transparent,
    ),
    
    // Button themes for dark mode
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: cs.primary,
        foregroundColor: cs.onPrimary,
        elevation: 2,
        shadowColor: Colors.black.withOpacity(0.3),
        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
      ),
    ),
    
    filledButtonTheme: FilledButtonThemeData(
      style: FilledButton.styleFrom(
        backgroundColor: cs.primaryContainer,
        foregroundColor: cs.onPrimaryContainer,
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w500,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
      ),
    ),
    
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: cs.primary,
        side: BorderSide(color: cs.primary, width: 1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w500,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
      ),
    ),
    
    // Chip theme for dark mode
    chipTheme: ChipThemeData(
      backgroundColor: cs.secondaryContainer,
      selectedColor: AppPalettes.emeraldDarkSuccess, // #5BC4A4
      disabledColor: cs.surfaceVariant,
      labelStyle: GoogleFonts.roboto(
        color: cs.onSurface,
        fontWeight: FontWeight.w500,
        fontSize: 12,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
      side: BorderSide.none,
      elevation: 0,
    ),
    
    // Input decoration for dark mode
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: cs.surfaceVariant,
      hintStyle: GoogleFonts.roboto(
        color: cs.onSurfaceVariant,
        fontSize: 14,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide(color: cs.outline, width: 1),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide(color: cs.outline, width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide(color: cs.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide(color: cs.error, width: 1),
      ),
    ),
    
    // Bottom navigation for dark mode
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: cs.surface,
      selectedItemColor: cs.primary,
      unselectedItemColor: cs.onSurfaceVariant,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedLabelStyle: GoogleFonts.roboto(
        fontSize: 12,
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: GoogleFonts.roboto(
        fontSize: 12,
        fontWeight: FontWeight.w400,
      ),
    ),
    
    // SnackBar theme
    snackBarTheme: SnackBarThemeData(
      backgroundColor: cs.onSurface,
      contentTextStyle: GoogleFonts.roboto(
        color: cs.surface,
        fontSize: 14,
      ),
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 4,
    ),
    
    // Popup menu theme
    popupMenuTheme: PopupMenuThemeData(
      color: cs.surface,
      elevation: 8,
      shadowColor: Colors.black.withOpacity(0.5),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: cs.outline, width: 1),
      ),
      textStyle: GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: cs.onSurface,
        letterSpacing: -0.1,
      ),
    ),
    
    // Divider
    dividerColor: cs.outline,
    dividerTheme: DividerThemeData(
      color: cs.outline,
      thickness: 0.5,
      space: 1,
    ),
  );
}

/// Latte Dark Theme (Dark mode for warm latte design)
ThemeData latteDarkTheme(BuildContext context) {
  const cs = ColorScheme(
    brightness: Brightness.dark,
    primary: AppPalettes.latteDarkPrimary,
    onPrimary: AppPalettes.latteDarkOnPrimary,
    primaryContainer: AppPalettes.latteDarkPrimaryCont,
    onPrimaryContainer: AppPalettes.latteDarkOnPrimaryCont,
    secondary: AppPalettes.latteDarkSecondary,
    onSecondary: AppPalettes.latteDarkOnSecondary,
    secondaryContainer: AppPalettes.latteDarkSecondaryCont,
    onSecondaryContainer: AppPalettes.latteDarkOnSecondaryCont,
    tertiary: AppPalettes.latteDarkSuccess,
    onTertiary: Color(0xFF1A1405),
    error: Color(0xFFFFB4AB),
    onError: Color(0xFF690005),
    errorContainer: Color(0xFF93000A),
    onErrorContainer: Color(0xFFFFDAD6),
    background: AppPalettes.latteDarkBackground,
    onBackground: AppPalettes.latteDarkOnSurface,
    surface: AppPalettes.latteDarkSurface,
    onSurface: AppPalettes.latteDarkOnSurface,
    surfaceVariant: AppPalettes.latteDarkSurfaceVar,
    onSurfaceVariant: AppPalettes.latteDarkOnSurfaceVar,
    outline: AppPalettes.latteDarkOutline,
    outlineVariant: AppPalettes.latteDarkSurfaceVar,
    inverseSurface: AppPalettes.latteDarkOnSurface,
    onInverseSurface: AppPalettes.latteDarkSurface,
    inversePrimary: AppPalettes.lattePrimary,
    shadow: Colors.black,
    scrim: Colors.black,
  );

  const radius = 16.0;

  return ThemeData(
    useMaterial3: true,
    colorScheme: cs,
    scaffoldBackgroundColor: cs.background,
    textTheme: _createTextTheme(context),
    
    // AppBar with warm dark surface
    appBarTheme: AppBarTheme(
      backgroundColor: cs.surface,
      foregroundColor: cs.onSurface,
      elevation: 0,
      centerTitle: false,
      titleTextStyle: _createTextTheme(context).titleLarge?.copyWith(
        color: cs.onSurface,
        fontWeight: FontWeight.w600,
      ),
      surfaceTintColor: Colors.transparent,
    ),
    
    // Cards with warm dark tones
    cardTheme: CardThemeData(
      color: cs.surface,
      elevation: 4,
      shadowColor: Colors.black.withOpacity(0.4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(radius)),
      margin: const EdgeInsets.all(12),
      surfaceTintColor: Colors.transparent,
    ),
    
    // Button themes with gold accents in dark mode
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: cs.secondary, // Gold button in dark mode
        foregroundColor: const Color(0xFF1A1405),
        elevation: 2,
        shadowColor: Colors.black.withOpacity(0.4),
        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
      ),
    ),
    
    filledButtonTheme: FilledButtonThemeData(
      style: FilledButton.styleFrom(
        backgroundColor: cs.primaryContainer,
        foregroundColor: cs.onPrimaryContainer,
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w500,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
      ),
    ),
    
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: cs.primary,
        side: BorderSide(color: cs.primary, width: 1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w500,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
      ),
    ),
    
    // Chip theme with warm dark styling
    chipTheme: ChipThemeData(
      backgroundColor: cs.surfaceVariant,
      selectedColor: AppPalettes.latteDarkSuccess, // #B5922F
      disabledColor: cs.outline,
      labelStyle: GoogleFonts.roboto(
        color: cs.onSurface,
        fontWeight: FontWeight.w500,
        fontSize: 12,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
      side: BorderSide.none,
      elevation: 0,
    ),
    
    // Input decoration with warm dark pill shape
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: cs.surfaceVariant,
      hintStyle: GoogleFonts.roboto(
        color: cs.onSurfaceVariant,
        fontSize: 14,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(22),
        borderSide: BorderSide(color: cs.outline, width: 1),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(22),
        borderSide: BorderSide(color: cs.outline, width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(22),
        borderSide: BorderSide(color: cs.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(22),
        borderSide: BorderSide(color: cs.error, width: 1),
      ),
    ),
    
    // Bottom navigation with warm dark tones
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: cs.surface,
      selectedItemColor: cs.primary,
      unselectedItemColor: cs.onSurfaceVariant,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedLabelStyle: GoogleFonts.roboto(
        fontSize: 12,
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: GoogleFonts.roboto(
        fontSize: 12,
        fontWeight: FontWeight.w400,
      ),
    ),
    
    // SnackBar theme
    snackBarTheme: SnackBarThemeData(
      backgroundColor: cs.onSurface,
      contentTextStyle: GoogleFonts.roboto(
        color: cs.surface,
        fontSize: 14,
      ),
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 4,
    ),
    
    // Popup menu theme
    popupMenuTheme: PopupMenuThemeData(
      color: cs.surface,
      elevation: 8,
      shadowColor: Colors.black.withOpacity(0.5),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: cs.outline, width: 1),
      ),
      textStyle: GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: cs.onSurface,
        letterSpacing: -0.1,
      ),
    ),
    
    // Divider
    dividerColor: cs.outline,
    dividerTheme: DividerThemeData(
      color: cs.outline,
      thickness: 0.5,
      space: 1,
    ),
  );
}
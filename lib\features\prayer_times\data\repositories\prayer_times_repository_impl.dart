import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';
import '../../domain/entities/prayer_times.dart';
import '../../domain/repositories/prayer_times_repository.dart';
import '../datasources/prayer_times_remote_datasource.dart';
import '../datasources/prayer_times_local_datasource.dart';
import '../models/prayer_times_model.dart';

class PrayerTimesRepositoryImpl implements PrayerTimesRepository {
  final PrayerTimesRemoteDataSource remoteDataSource;
  final PrayerTimesLocalDataSource localDataSource;

  PrayerTimesRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, PrayerTimes>> getPrayerTimes({
    required DateTime date,
    required double latitude,
    required double longitude,
    required int calculationMethod,
  }) async {
    try {
      final prayerTimesModel = await remoteDataSource.getPrayerTimes(
        date: date,
        latitude: latitude,
        longitude: longitude,
        calculationMethod: calculationMethod,
      );
      return Right(prayerTimesModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, PrayerTimes>> getPrayerTimesByCity({
    required DateTime date,
    required String city,
    required String country,
    required int calculationMethod,
  }) async {
    try {
      final prayerTimesModel = await remoteDataSource.getPrayerTimesByCity(
        date: date,
        city: city,
        country: country,
        calculationMethod: calculationMethod,
      );
      return Right(prayerTimesModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, PrayerTimes>> getPrayerTimesByAddress({
    required DateTime date,
    required String address,
    required int calculationMethod,
  }) async {
    try {
      final prayerTimesModel = await remoteDataSource.getPrayerTimesByAddress(
        date: date,
        address: address,
        calculationMethod: calculationMethod,
      );
      return Right(prayerTimesModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<PrayerTimes>>> getMonthlyPrayerTimes({
    required DateTime month,
    required double latitude,
    required double longitude,
    required int calculationMethod,
  }) async {
    try {
      final prayerTimesModels = await remoteDataSource.getMonthlyPrayerTimes(
        month: month,
        latitude: latitude,
        longitude: longitude,
        calculationMethod: calculationMethod,
      );
      final entities = prayerTimesModels.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> cachePrayerTimes({
    required List<PrayerTimes> prayerTimes,
    required String cacheKey,
  }) async {
    try {
      final models = prayerTimes.map((entity) => _entityToModel(entity)).toList();
      await localDataSource.cachePrayerTimes(
        prayerTimes: models,
        cacheKey: cacheKey,
      );
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, PrayerTimes?>> getCachedPrayerTimes({
    required DateTime date,
    required String cacheKey,
  }) async {
    try {
      final cachedModel = await localDataSource.getCachedPrayerTimes(
        date: date,
        cacheKey: cacheKey,
      );
      return Right(cachedModel?.toEntity());
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<PrayerTimes>?>> getCachedMonthlyPrayerTimes({
    required DateTime month,
    required String cacheKey,
  }) async {
    try {
      final cachedModels = await localDataSource.getCachedMonthlyPrayerTimes(
        month: month,
        cacheKey: cacheKey,
      );
      final entities = cachedModels?.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> clearExpiredCache() async {
    try {
      await localDataSource.clearExpiredCache();
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<bool> isCacheValid({
    required DateTime date,
    required String cacheKey,
  }) async {
    try {
      return await localDataSource.isCacheValid(
        date: date,
        cacheKey: cacheKey,
      );
    } catch (e) {
      return false;
    }
  }

  // Helper method to convert entity to model
  PrayerTimesModel _entityToModel(PrayerTimes entity) {
    return PrayerTimesModel(
      dateField: entity.date,
      fajrField: entity.fajr,
      sunriseField: entity.sunrise,
      dhuhrField: entity.dhuhr,
      asrField: entity.asr,
      sunsetField: entity.sunset,
      maghribField: entity.maghrib,
      ishaField: entity.isha,
      midnightField: entity.midnight,
      imsakField: entity.imsak,
      calculationMethodField: entity.calculationMethod,
      latitudeField: entity.latitude,
      longitudeField: entity.longitude,
      cityField: entity.city,
      countryField: entity.country,
      timezoneField: entity.timezone,
    );
  }

  @override
  Future<Either<Failure, List<PrayerTimes>>> getPrayerTimesRange({
    required DateTime startDate,
    required DateTime endDate,
    required double latitude,
    required double longitude,
    required int calculationMethod,
  }) async {
    try {
      // Generate cache key for the range
      final cacheKey = _generateCacheKey(latitude, longitude, calculationMethod);

      // Try to get from cache first
      final cachedResult = await _getCachedRange(startDate, endDate, cacheKey);
      if (cachedResult.isNotEmpty) {
        return Right(cachedResult);
      }

      // Calculate prayer times for each day in the range
      final prayerTimesList = <PrayerTimes>[];
      final currentDate = DateTime(startDate.year, startDate.month, startDate.day);
      final endDateOnly = DateTime(endDate.year, endDate.month, endDate.day);

      DateTime iterDate = currentDate;
      while (iterDate.isBefore(endDateOnly) || iterDate.isAtSameMomentAs(endDateOnly)) {
        final result = await getPrayerTimes(
          date: iterDate,
          latitude: latitude,
          longitude: longitude,
          calculationMethod: calculationMethod,
        );

        result.fold(
          (failure) => throw Exception('Failed to get prayer times for $iterDate'),
          (prayerTimes) => prayerTimesList.add(prayerTimes),
        );

        iterDate = iterDate.add(const Duration(days: 1));
      }

      // Cache the results
      await _cacheRange(prayerTimesList, cacheKey);

      return Right(prayerTimesList);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  Future<List<PrayerTimes>> _getCachedRange(DateTime startDate, DateTime endDate, String cacheKey) async {
    final cachedList = <PrayerTimes>[];

    DateTime iterDate = DateTime(startDate.year, startDate.month, startDate.day);
    final endDateOnly = DateTime(endDate.year, endDate.month, endDate.day);

    while (iterDate.isBefore(endDateOnly) || iterDate.isAtSameMomentAs(endDateOnly)) {
      final result = await getCachedPrayerTimes(date: iterDate, cacheKey: cacheKey);
      result.fold(
        (failure) => {}, // Ignore cache misses
        (cached) {
          if (cached != null) {
            cachedList.add(cached);
          }
        },
      );
      iterDate = iterDate.add(const Duration(days: 1));
    }

    // Only return cached data if we have all days
    final expectedDays = endDate.difference(startDate).inDays + 1;
    return cachedList.length == expectedDays ? cachedList : [];
  }

  Future<void> _cacheRange(List<PrayerTimes> prayerTimesList, String cacheKey) async {
    for (final prayerTimes in prayerTimesList) {
      await cachePrayerTimes(
        prayerTimes: [prayerTimes],
        cacheKey: cacheKey,
      );
    }
  }

  String _generateCacheKey(double latitude, double longitude, int calculationMethod) {
    return 'prayer_times_${latitude.toStringAsFixed(4)}_${longitude.toStringAsFixed(4)}_$calculationMethod';
  }
}

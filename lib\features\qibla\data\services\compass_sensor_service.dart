import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:sensors_plus/sensors_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../domain/entities/qibla_direction.dart';
import '../../domain/services/qibla_calculation_service.dart';
import '../../domain/utils/sensor_fusion_utils.dart';

/// Service for handling compass sensor data and permissions
class CompassSensorService {
  StreamSubscription<MagnetometerEvent>? _magnetometerSubscription;
  StreamSubscription<AccelerometerEvent>? _accelerometerSubscription;

  final StreamController<CompassReading> _compassController =
      StreamController<CompassReading>.broadcast();

  // Sensor data
  double _currentHeading = 0.0;
  double _currentAccuracy = 0.0;
  double _pitch = 0.0;
  double _roll = 0.0;
  double _magneticX = 0.0;
  double _magneticY = 0.0;
  double _magneticZ = 0.0;
  double _accelX = 0.0;
  double _accelY = 0.0;
  double _accelZ = 0.0;

  // Smoothing and filtering
  final List<double> _recentReadings = [];
  double _smoothedHeading = 0.0;
  double _magneticDeclination = 0.0;

  // Status
  CompassStatus _status = CompassStatus.notAvailable;
  bool _isListening = false;

  // Enhanced calibration tracking
  bool _isCalibrating = false;
  DateTime? _calibrationStartTime;
  final List<CalibrationDataPoint> _calibrationData = [];
  int _calibrationQuality = 0;
  CalibrationPhase _calibrationPhase = CalibrationPhase.preparation;

  // Calibration persistence
  double _hardIronOffsetX = 0.0;
  double _hardIronOffsetY = 0.0;
  double _hardIronOffsetZ = 0.0;
  DateTime? _lastCalibrationTime;

  /// Stream of compass readings
  Stream<CompassReading> get compassStream => _compassController.stream;
  
  /// Current compass status
  CompassStatus get status => _status;
  
  /// Check if compass sensor is available on device
  Future<bool> isCompassAvailable() async {
    try {
      // Check if magnetometer stream is available
      final magnetometerStream = magnetometerEventStream();

      // Try to get a reading with timeout
      await magnetometerStream.timeout(
        const Duration(seconds: 2),
      ).first;

      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Request necessary permissions for compass
  Future<bool> requestPermissions() async {
    if (Platform.isIOS) {
      // iOS requires location permission for compass
      final locationStatus = await Permission.locationWhenInUse.request();
      if (!locationStatus.isGranted) {
        _status = CompassStatus.permissionDenied;
        return false;
      }
    }
    
    // Android doesn't require special permissions for compass
    return true;
  }
  
  /// Start listening to compass sensor with calibration data loading
  Future<void> startListening() async {
    if (_isListening) return;

    // Load saved calibration data
    await _loadCalibrationData();

    // Check permissions first
    final hasPermissions = await requestPermissions();
    if (!hasPermissions) return;

    // Check if compass is available
    final isAvailable = await isCompassAvailable();
    if (!isAvailable) {
      _status = CompassStatus.notAvailable;
      return;
    }

    _isListening = true;
    _status = CompassStatus.ok;

    // Start magnetometer stream
    _magnetometerSubscription = magnetometerEventStream().listen(
      _onMagnetometerEvent,
      onError: _onMagnetometerError,
    );

    // Start accelerometer for tilt compensation
    _accelerometerSubscription = accelerometerEventStream().listen(
      _onAccelerometerEvent,
      onError: (error) {
        // Accelerometer errors are not critical
        print('Accelerometer error: $error');
      },
    );

    print('🧭 Enhanced compass sensor started');
    if (_lastCalibrationTime != null) {
      final hoursSince = DateTime.now().difference(_lastCalibrationTime!).inHours;
      print('📱 Using calibration from ${hoursSince}h ago (Quality: ${_calibrationQuality}%)');
    } else {
      print('⚠️ No previous calibration found - calibration recommended');
    }
  }
  
  /// Stop listening to compass sensor
  void stopListening() {
    if (!_isListening) return;

    _isListening = false;
    _magnetometerSubscription?.cancel();
    _accelerometerSubscription?.cancel();
    _magnetometerSubscription = null;
    _accelerometerSubscription = null;
  }
  
  /// Reset calibration by clearing cached readings and forcing recalibration
  Future<void> resetCalibration() async {
    // Clear recent readings to force fresh calibration
    _recentReadings.clear();
    _smoothedHeading = 0.0;
    _currentHeading = 0.0;
    _currentAccuracy = 0.0;
    _pitch = 0.0;
    _roll = 0.0;

    // Start calibration process
    _startCalibrationProcess();

    // Emit calibrating status
    final calibratingReading = CompassReading(
      heading: 0.0,
      accuracy: 999.0,
      status: CompassStatus.calibrating,
      rawHeading: 0.0,
      pitch: 0.0,
      roll: 0.0,
    );

    _compassController.add(calibratingReading);
  }

  /// Start the enhanced calibration process with guided phases
  void _startCalibrationProcess() {
    _isCalibrating = true;
    _calibrationStartTime = DateTime.now();
    _calibrationData.clear();
    _calibrationQuality = 0;
    _calibrationPhase = CalibrationPhase.preparation;
    _status = CompassStatus.calibrating;

    print('🔄 Enhanced calibration started');
    print('📱 Phase 1: Hold device flat and steady for 2 seconds');

    // Start phase progression timer
    Timer(const Duration(seconds: 2), () {
      if (_isCalibrating) {
        _calibrationPhase = CalibrationPhase.figure8Movement;
        print('📱 Phase 2: Move device in figure-8 pattern (3 cycles)');
      }
    });

    Timer(const Duration(seconds: 12), () {
      if (_isCalibrating) {
        _calibrationPhase = CalibrationPhase.tiltMovement;
        print('📱 Phase 3: Tilt device in all directions');
      }
    });

    Timer(const Duration(seconds: 20), () {
      if (_isCalibrating) {
        _calibrationPhase = CalibrationPhase.finalizing;
        print('📱 Phase 4: Finalizing calibration...');
      }
    });
  }

  /// Enhanced calibration movement analysis with quality scoring
  void _checkCalibrationMovement(double heading, double magneticStrength) {
    if (!_isCalibrating) return;

    final now = DateTime.now();
    final dataPoint = CalibrationDataPoint(
      heading: heading,
      magneticStrength: magneticStrength,
      timestamp: now,
      pitch: _pitch,
      roll: _roll,
    );

    _calibrationData.add(dataPoint);

    // Keep reasonable amount of data (100 points = ~10 seconds)
    if (_calibrationData.length > 100) {
      _calibrationData.removeAt(0);
    }

    // Update calibration quality every 5 readings
    if (_calibrationData.length >= 5 && _calibrationData.length % 5 == 0) {
      _updateCalibrationQuality();
    }

    // Check phase-specific requirements
    _checkPhaseRequirements();

    // Auto-complete calibration when quality is sufficient
    if (_calibrationQuality >= 80 && _calibrationData.length >= 50) {
      print('✅ High quality calibration achieved (${_calibrationQuality}%)');
      _completeCalibration();
    }

    // Timeout after 45 seconds
    if (now.difference(_calibrationStartTime!).inSeconds > 45) {
      print('⏰ Calibration timeout - completing with quality: ${_calibrationQuality}%');
      _completeCalibration();
    }
  }

  /// Update calibration quality score in real-time
  void _updateCalibrationQuality() {
    _calibrationQuality = SensorFusionUtils.calculateCalibrationQuality(
      calibrationData: _calibrationData,
      currentAccuracy: _currentAccuracy,
    );

    // Emit quality update
    final qualityReading = CompassReading(
      heading: _smoothedHeading,
      accuracy: _currentAccuracy,
      status: CompassStatus.calibrating,
      rawHeading: _currentHeading,
      pitch: _pitch,
      roll: _roll,
      calibrationQuality: _calibrationQuality,
      calibrationPhase: _calibrationPhase,
    );

    _compassController.add(qualityReading);
  }

  /// Check requirements for current calibration phase
  void _checkPhaseRequirements() {
    final duration = DateTime.now().difference(_calibrationStartTime!).inSeconds;

    switch (_calibrationPhase) {
      case CalibrationPhase.preparation:
        // Just collecting baseline data
        break;

      case CalibrationPhase.figure8Movement:
        if (duration >= 12 && _calibrationQuality < 40) {
          print('⚠️ Need more figure-8 movement for better coverage');
        }
        break;

      case CalibrationPhase.tiltMovement:
        if (duration >= 20 && _calibrationQuality < 60) {
          print('⚠️ Need more tilt movement in different directions');
        }
        break;

      case CalibrationPhase.finalizing:
        // Final quality check
        break;

      case CalibrationPhase.complete:
        // Calibration is finished
        break;
    }
  }

  /// Analyze the movement pattern to detect figure-8 movements
  void _analyzeFigure8Pattern() {
    if (_calibrationData.length < 20) return;

    // Get recent points for analysis
    final recentPoints = _calibrationData.sublist(_calibrationData.length - 20);

    // Check for heading variation (should cover significant range)
    final headings = recentPoints.map((p) => p.heading).toList();
    final minHeading = headings.reduce(math.min);
    final maxHeading = headings.reduce(math.max);
    final headingRange = _normalizeHeadingDifference(maxHeading - minHeading);

    // Check for tilt variation (device should be tilted during figure-8)
    final pitches = recentPoints.map((p) => p.pitch.abs()).toList();
    final rolls = recentPoints.map((p) => p.roll.abs()).toList();
    final maxPitch = pitches.reduce(math.max);
    final maxRoll = rolls.reduce(math.max);

    // Check for magnetic field variation (important for calibration)
    final magneticStrengths = recentPoints.map((p) => p.magneticStrength).toList();
    final minMagnetic = magneticStrengths.reduce(math.min);
    final maxMagnetic = magneticStrengths.reduce(math.max);
    final magneticVariation = (maxMagnetic - minMagnetic) / minMagnetic;

    // Detect figure-8 cycle completion
    bool cycleDetected = false;

    if (headingRange > 180 && // Good heading coverage
        maxPitch > 15 && // Device was tilted
        maxRoll > 15 && // Device was rotated
        magneticVariation > 0.1) { // Magnetic field varied

      cycleDetected = true;
      // Increment quality score for detected movement
      _calibrationQuality += 10;

      print('✅ Good movement pattern detected');
      print('   Heading range: ${headingRange.toStringAsFixed(1)}°');
      print('   Max pitch: ${maxPitch.toStringAsFixed(1)}°');
      print('   Max roll: ${maxRoll.toStringAsFixed(1)}°');
      print('   Magnetic variation: ${(magneticVariation * 100).toStringAsFixed(1)}%');
    }

    // Provide feedback to user based on quality
    if (_calibrationQuality < 40 && _calibrationData.length > 30) {
      print('📱 Keep moving device in figure-8 pattern...');
    } else if (_calibrationQuality >= 40 && _calibrationQuality < 80) {
      print('🔄 Good progress! Continue movement for better accuracy...');
    }
  }

  /// Complete the enhanced calibration process with persistence
  void _completeCalibration() async {
    _isCalibrating = false;
    _calibrationPhase = CalibrationPhase.complete;
    _status = CompassStatus.ok;
    _lastCalibrationTime = DateTime.now();

    final duration = DateTime.now().difference(_calibrationStartTime!).inSeconds;

    // Calculate hard-iron offsets from calibration data
    if (_calibrationData.length >= 20) {
      _calculateHardIronOffsets();
    }

    // Save calibration data to persistent storage
    await _saveCalibrationData();

    print('✅ Enhanced calibration completed!');
    print('   Duration: ${duration}s');
    print('   Quality: ${_calibrationQuality}%');
    print('   Data points: ${_calibrationData.length}');
    print('   Hard-iron offsets: X=${_hardIronOffsetX.toStringAsFixed(2)}, Y=${_hardIronOffsetY.toStringAsFixed(2)}, Z=${_hardIronOffsetZ.toStringAsFixed(2)}');

    // Clear calibration data
    _calibrationData.clear();
    _calibrationStartTime = null;

    // Emit completion reading
    final completionReading = CompassReading(
      heading: _smoothedHeading,
      accuracy: _currentAccuracy,
      status: CompassStatus.ok,
      rawHeading: _currentHeading,
      pitch: _pitch,
      roll: _roll,
      calibrationQuality: _calibrationQuality,
      calibrationPhase: CalibrationPhase.complete,
    );

    _compassController.add(completionReading);
  }

  /// Calculate hard-iron calibration offsets
  void _calculateHardIronOffsets() {
    if (_calibrationData.isEmpty) return;

    // Simple hard-iron calibration: find center of magnetic field measurements
    double sumX = 0, sumY = 0, sumZ = 0;
    double minX = double.infinity, maxX = double.negativeInfinity;
    double minY = double.infinity, maxY = double.negativeInfinity;
    double minZ = double.infinity, maxZ = double.negativeInfinity;

    // This is a simplified approach - in production, use more sophisticated algorithms
    for (int i = 0; i < _calibrationData.length; i++) {
      // We need to store raw magnetic values in CalibrationDataPoint for this to work
      // For now, use a simplified approach
      final heading = _calibrationData[i].heading;
      final strength = _calibrationData[i].magneticStrength;

      // Convert back to approximate X,Y components (simplified)
      final x = strength * math.cos(heading * math.pi / 180);
      final y = strength * math.sin(heading * math.pi / 180);

      sumX += x; sumY += y;
      minX = math.min(minX, x); maxX = math.max(maxX, x);
      minY = math.min(minY, y); maxY = math.max(maxY, y);
    }

    // Hard-iron offset is the center of the measurement range
    _hardIronOffsetX = (minX + maxX) / 2;
    _hardIronOffsetY = (minY + maxY) / 2;
    _hardIronOffsetZ = 0; // Simplified for 2D calibration
  }

  /// Save calibration data to persistent storage
  Future<void> _saveCalibrationData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('compass_hard_iron_x', _hardIronOffsetX);
      await prefs.setDouble('compass_hard_iron_y', _hardIronOffsetY);
      await prefs.setDouble('compass_hard_iron_z', _hardIronOffsetZ);
      await prefs.setString('compass_last_calibration', _lastCalibrationTime!.toIso8601String());
      await prefs.setInt('compass_calibration_quality', _calibrationQuality);

      print('💾 Calibration data saved to persistent storage');
    } catch (e) {
      print('❌ Failed to save calibration data: $e');
    }
  }

  /// Load calibration data from persistent storage
  Future<void> _loadCalibrationData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _hardIronOffsetX = prefs.getDouble('compass_hard_iron_x') ?? 0.0;
      _hardIronOffsetY = prefs.getDouble('compass_hard_iron_y') ?? 0.0;
      _hardIronOffsetZ = prefs.getDouble('compass_hard_iron_z') ?? 0.0;

      final lastCalibrationStr = prefs.getString('compass_last_calibration');
      if (lastCalibrationStr != null) {
        _lastCalibrationTime = DateTime.parse(lastCalibrationStr);
      }

      _calibrationQuality = prefs.getInt('compass_calibration_quality') ?? 0;

      if (_lastCalibrationTime != null) {
        final hoursSince = DateTime.now().difference(_lastCalibrationTime!).inHours;
        print('📱 Loaded calibration data from ${hoursSince}h ago (Quality: ${_calibrationQuality}%)');
      }
    } catch (e) {
      print('❌ Failed to load calibration data: $e');
    }
  }

  /// Normalize heading difference to handle 0-360 wrap-around
  double _normalizeHeadingDifference(double diff) {
    if (diff > 180) {
      return 360 - diff;
    }
    return diff.abs();
  }
  
  /// Enhanced magnetometer event handler with tilt compensation
  void _onMagnetometerEvent(MagnetometerEvent event) {
    // Apply hard-iron calibration offsets
    _magneticX = event.x - _hardIronOffsetX;
    _magneticY = event.y - _hardIronOffsetY;
    _magneticZ = event.z - _hardIronOffsetZ;

    final magneticStrength = math.sqrt(_magneticX * _magneticX + _magneticY * _magneticY + _magneticZ * _magneticZ);

    // Use tilt-compensated heading calculation
    _currentHeading = SensorFusionUtils.calculateTiltCompensatedHeading(
      magneticX: _magneticX,
      magneticY: _magneticY,
      magneticZ: _magneticZ,
      accelX: _accelX,
      accelY: _accelY,
      accelZ: _accelZ,
    );

    // Estimate accuracy based on magnetic field strength and stability
    _currentAccuracy = SensorFusionUtils.estimateCompassAccuracy(
      magneticStrength: magneticStrength,
      recentReadings: _recentReadings,
    );

    // Add to recent readings for smoothing
    _recentReadings.add(_currentHeading);
    if (_recentReadings.length > 20) {
      _recentReadings.removeAt(0);
    }

    // Apply EMA smoothing
    if (_recentReadings.length > 1) {
      _smoothedHeading = SensorFusionUtils.applySmoothingEMA(
        currentReading: _currentHeading,
        previousReading: _smoothedHeading,
        smoothingFactor: 0.15,
      );
    } else {
      _smoothedHeading = _currentHeading;
    }

    // Check calibration movement if in calibration mode
    if (_isCalibrating) {
      _checkCalibrationMovement(_smoothedHeading, magneticStrength);
    } else {
      // Update status based on accuracy and interference
      _updateCompassStatus(magneticStrength);
    }

    // Emit reading with enhanced data
    final reading = CompassReading(
      heading: _smoothedHeading,
      accuracy: _currentAccuracy,
      status: _status,
      rawHeading: _currentHeading,
      pitch: _pitch,
      roll: _roll,
      magneticStrength: magneticStrength,
      calibrationQuality: _calibrationQuality,
      calibrationPhase: _calibrationPhase,
    );

    _compassController.add(reading);
  }

  /// Update compass status based on current conditions
  void _updateCompassStatus(double magneticStrength) {
    // Check for magnetic interference
    if (magneticStrength < SensorFusionUtils.minMagneticStrength ||
        magneticStrength > SensorFusionUtils.maxMagneticStrength) {
      _status = CompassStatus.disturbed;
      return;
    }

    // Check if calibration is needed
    final needsCalibration = _needsRecalibration();
    if (needsCalibration) {
      _status = CompassStatus.calibrating;
      return;
    }

    // Check accuracy
    if (_currentAccuracy > 15.0) {
      _status = CompassStatus.disturbed;
    } else {
      _status = CompassStatus.ok;
    }
  }

  /// Check if recalibration is needed
  bool _needsRecalibration() {
    // If never calibrated
    if (_lastCalibrationTime == null) return true;

    // If calibration is old (24 hours)
    final hoursSinceCalibration = DateTime.now().difference(_lastCalibrationTime!).inHours;
    if (hoursSinceCalibration > 24) return true;

    // If accuracy is consistently poor
    if (_recentReadings.length >= 10) {
      final variance = _calculateVariance(_recentReadings);
      if (variance > 200.0) return true; // High variance indicates need for calibration
    }

    return false;
  }
  
  /// Handle magnetometer sensor errors
  void _onMagnetometerError(dynamic error) {
    print('Magnetometer error: $error');
    _status = CompassStatus.disturbed;

    // Emit error status
    final reading = CompassReading(
      heading: _smoothedHeading,
      accuracy: 999.0, // High accuracy value indicates error
      status: _status,
      rawHeading: _currentHeading,
      pitch: _pitch,
      roll: _roll,
    );

    _compassController.add(reading);
  }
  
  /// Enhanced accelerometer event handler for tilt compensation
  void _onAccelerometerEvent(AccelerometerEvent event) {
    // Store raw accelerometer data for tilt compensation
    _accelX = event.x;
    _accelY = event.y;
    _accelZ = event.z;

    // Calculate pitch and roll from accelerometer data
    final magnitude = math.sqrt(_accelX * _accelX + _accelY * _accelY + _accelZ * _accelZ);

    if (magnitude > 0) {
      // Normalize accelerometer values
      final ax = _accelX / magnitude;
      final ay = _accelY / magnitude;
      final az = _accelZ / magnitude;

      // Calculate pitch (rotation around X-axis) - forward/backward tilt
      _pitch = math.atan2(-ax, math.sqrt(ay * ay + az * az)) * 180 / math.pi;

      // Calculate roll (rotation around Y-axis) - left/right tilt
      _roll = math.atan2(ay, az) * 180 / math.pi;
    }
  }

  /// Calculate heading from magnetometer data
  double _calculateHeading(double x, double y, double z) {
    // Calculate heading using atan2 (magnetic north)
    double heading = math.atan2(y, x) * 180 / math.pi;

    // Normalize to 0-360 degrees
    if (heading < 0) {
      heading += 360;
    }

    return heading;
  }

  /// Estimate accuracy based on magnetic field strength
  double _estimateAccuracy(double magneticStrength) {
    // Typical Earth's magnetic field strength is around 25-65 µT
    // Convert from µT to degrees of accuracy estimate
    const double typicalFieldStrength = 50.0; // µT
    const double minAccuracy = 5.0; // degrees
    const double maxAccuracy = 45.0; // degrees

    // Simple heuristic: stronger field = better accuracy
    final double strengthRatio = magneticStrength / typicalFieldStrength;
    final double accuracy = maxAccuracy / math.max(0.1, strengthRatio);

    return math.max(minAccuracy, math.min(maxAccuracy, accuracy));
  }

  /// Calculate variance of a list of values
  double _calculateVariance(List<double> values) {
    if (values.isEmpty) return 0.0;

    final mean = values.reduce((a, b) => a + b) / values.length;
    final squaredDifferences = values.map((x) => math.pow(x - mean, 2));
    return squaredDifferences.reduce((a, b) => a + b) / values.length;
  }

  /// Dispose resources
  void dispose() {
    stopListening();
    _compassController.close();
  }
}

/// Enhanced compass sensor reading with calibration data
class CompassReading {
  final double heading;
  final double accuracy;
  final CompassStatus status;
  final double rawHeading;
  final double pitch;
  final double roll;
  final double magneticStrength;
  final int calibrationQuality;
  final CalibrationPhase calibrationPhase;

  const CompassReading({
    required this.heading,
    required this.accuracy,
    required this.status,
    required this.rawHeading,
    required this.pitch,
    required this.roll,
    this.magneticStrength = 0.0,
    this.calibrationQuality = 0,
    this.calibrationPhase = CalibrationPhase.complete,
  });

  /// Get quality color based on calibration score
  Color get qualityColor {
    if (calibrationQuality >= 80) return Colors.green;
    if (calibrationQuality >= 60) return Colors.orange;
    return Colors.red;
  }

  /// Get quality description
  String get qualityDescription {
    if (calibrationQuality >= 90) return 'Excellent';
    if (calibrationQuality >= 80) return 'Good';
    if (calibrationQuality >= 60) return 'Fair';
    if (calibrationQuality >= 40) return 'Poor';
    return 'Very Poor';
  }
}

/// Calibration phases for guided calibration process
enum CalibrationPhase {
  preparation,    // Hold steady
  figure8Movement, // Figure-8 movements
  tiltMovement,   // Tilt in all directions
  finalizing,     // Processing data
  complete,       // Calibration finished
}

extension CalibrationPhaseExtension on CalibrationPhase {
  String get description {
    switch (this) {
      case CalibrationPhase.preparation:
        return 'Hold device flat and steady';
      case CalibrationPhase.figure8Movement:
        return 'Move in figure-8 pattern';
      case CalibrationPhase.tiltMovement:
        return 'Tilt device in all directions';
      case CalibrationPhase.finalizing:
        return 'Finalizing calibration...';
      case CalibrationPhase.complete:
        return 'Calibration complete';
    }
  }

  IconData get icon {
    switch (this) {
      case CalibrationPhase.preparation:
        return Icons.phone_android;
      case CalibrationPhase.figure8Movement:
        return Icons.all_out;
      case CalibrationPhase.tiltMovement:
        return Icons.screen_rotation;
      case CalibrationPhase.finalizing:
        return Icons.hourglass_empty;
      case CalibrationPhase.complete:
        return Icons.check_circle;
    }
  }
}

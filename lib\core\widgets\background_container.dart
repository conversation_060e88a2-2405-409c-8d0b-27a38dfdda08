import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../app_config.dart';
import '../../features/settings/presentation/bloc/settings_bloc.dart';

class BackgroundContainer extends StatelessWidget {
  final Widget child;

  const BackgroundContainer({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    // Always use normal background - no more custom backgrounds
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      decoration: _getBackgroundDecoration(context),
      child: child,
    );
  }

  BoxDecoration _getBackgroundDecoration(BuildContext context) {
    // Always use normal solid background
    return BoxDecoration(
      color: Theme.of(context).scaffoldBackgroundColor,
    );
  }
}

import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import '../app_config.dart';

class SystemLocaleUtils {
  /// Get the system locale with fallback to English if not supported
  static Locale getSystemLocale() {
    final systemLocale = ui.PlatformDispatcher.instance.locale;
    
    // Check if the exact system locale is supported
    for (final supportedLocale in AppConfig.supportedLocales) {
      if (supportedLocale.languageCode == systemLocale.languageCode &&
          supportedLocale.countryCode == systemLocale.countryCode) {
        return supportedLocale;
      }
    }
    
    // Check if just the language code is supported (ignore country)
    for (final supportedLocale in AppConfig.supportedLocales) {
      if (supportedLocale.languageCode == systemLocale.languageCode) {
        return supportedLocale;
      }
    }
    
    // Fallback to English if system language is not supported
    return const Locale('en', 'US');
  }
  
  /// Get system theme mode (light/dark)
  static ThemeMode getSystemThemeMode() {
    final brightness = ui.PlatformDispatcher.instance.platformBrightness;
    return brightness == Brightness.dark ? ThemeMode.dark : ThemeMode.light;
  }
  
  /// Get display name for a locale
  static String getLanguageDisplayName(Locale locale) {
    switch (locale.languageCode) {
      case 'en':
        return 'English';
      case 'de':
        return 'Deutsch';
      case 'hr':
        return 'Hrvatski';
      case 'fr':
        return 'Français';
      case 'ar':
        return 'العربية';
      case 'zh':
        return '中文';
      case 'ru':
        return 'Русский';
      case 'ms':
        return 'Bahasa Melayu';
      case 'id':
        return 'Bahasa Indonesia';
      case 'hi':
        return 'हिन्दी';
      case 'es':
        return 'Español';
      default:
        return 'English';
    }
  }
  
  /// Check if a locale is RTL (Right-to-Left)
  static bool isRTL(Locale locale) {
    return locale.languageCode == 'ar' || locale.languageCode == 'hi';
  }
}
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'tokens.dart';

/// Print theme for PDF exports with no opacity
/// Only used for Print/Export - ensures clear, readable documents
class PrintTheme {
  // Private constructor
  PrintTheme._();

  /// Print-specific colors (all solid, no opacity)
  static const Color background = ColorTokens.printBackground; // #FFFFFF
  static const Color text = ColorTokens.printText; // #2C2A27
  static const Color accent = ColorTokens.printAccent; // #C7A351 (Gold for headers/lines)
  static const Color tableLines = ColorTokens.printTableLines; // #D0D5DD
  static const Color zebraLight = ColorTokens.printZebraLight; // #F6F7F9 (alternating rows)

  /// Typography for print documents
  static const double bodyFontSize = 12.0;
  static const double captionFontSize = 13.0;
  static const double headerFontSize = 16.0;
  static const double titleFontSize = 18.0;

  /// Text styles for PDF documents
  static TextStyle get titleStyle => GoogleFonts.roboto(
    fontSize: titleFontSize,
    fontWeight: FontWeight.w700,
    color: text,
    letterSpacing: -0.5,
  );

  static TextStyle get headerStyle => GoogleFonts.roboto(
    fontSize: headerFontSize,
    fontWeight: FontWeight.w600,
    color: accent, // Gold for headers
    letterSpacing: -0.25,
  );

  static TextStyle get bodyStyle => GoogleFonts.roboto(
    fontSize: bodyFontSize,
    fontWeight: FontWeight.w400,
    color: text,
  );

  static TextStyle get captionStyle => GoogleFonts.roboto(
    fontSize: captionFontSize,
    fontWeight: FontWeight.w400,
    color: text,
  );

  static TextStyle get monoStyle => GoogleFonts.robotoMono(
    fontSize: bodyFontSize,
    fontWeight: FontWeight.w400,
    color: text,
  );

  static TextStyle get boldBodyStyle => GoogleFonts.roboto(
    fontSize: bodyFontSize,
    fontWeight: FontWeight.w600,
    color: text,
  );

  /// Table styling
  static BoxDecoration get tableHeaderDecoration => BoxDecoration(
    color: zebraLight,
    border: Border.all(color: tableLines, width: 1),
  );

  static BoxDecoration get tableCellDecoration => BoxDecoration(
    border: Border.all(color: tableLines, width: 0.5),
  );

  static BoxDecoration getTableRowDecoration(bool isZebra) => BoxDecoration(
    color: isZebra ? zebraLight : background,
    border: Border(
      bottom: BorderSide(color: tableLines, width: 0.5),
    ),
  );

  /// Page layout constants
  static const double pageMargin = 20.0;
  static const double sectionSpacing = 16.0;
  static const double itemSpacing = 8.0;
  static const double tableRowHeight = 32.0;
  static const double tableHeaderHeight = 40.0;

  /// A4 Portrait dimensions (in points)
  static const double a4PortraitWidth = 595.28;
  static const double a4PortraitHeight = 841.89;

  /// A4 Landscape dimensions (in points)
  static const double a4LandscapeWidth = 841.89;
  static const double a4LandscapeHeight = 595.28;

  /// Column widths for different layouts
  static const Map<String, double> portraitColumnWidths = {
    'date': 80.0,
    'gregorian': 60.0,
    'hijri': 60.0,
    'fajr': 50.0,
    'dhuhr': 50.0,
    'asr': 50.0,
    'maghrib': 50.0,
    'isha': 50.0,
  };

  static const Map<String, double> landscapeColumnWidths = {
    'date': 100.0,
    'gregorian': 80.0,
    'hijri': 80.0,
    'fajr': 70.0,
    'dhuhr': 70.0,
    'asr': 70.0,
    'maghrib': 70.0,
    'isha': 70.0,
  };

  /// Header information styling
  static Widget buildDocumentHeader({
    required String title,
    required String subtitle,
    String? locationInfo,
    String? dateRange,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: titleStyle),
        SizedBox(height: itemSpacing),
        Text(subtitle, style: headerStyle),
        if (locationInfo != null) ...[
          SizedBox(height: itemSpacing / 2),
          Text(locationInfo, style: captionStyle),
        ],
        if (dateRange != null) ...[
          SizedBox(height: itemSpacing / 2),
          Text(dateRange, style: captionStyle),
        ],
        SizedBox(height: sectionSpacing),
      ],
    );
  }

  /// Table header row
  static Widget buildTableHeader({
    required List<String> columns,
    required Map<String, double> columnWidths,
    bool isLandscape = false,
  }) {
    return Container(
      height: tableHeaderHeight,
      decoration: tableHeaderDecoration,
      child: Row(
        children: columns.map((column) {
          final width = columnWidths[column.toLowerCase()] ?? 60.0;
          return Container(
            width: width,
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(
              horizontal: itemSpacing / 2,
              vertical: itemSpacing / 2,
            ),
            child: Text(
              column,
              style: boldBodyStyle,
              textAlign: TextAlign.center,
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Table data row
  static Widget buildTableRow({
    required List<String> data,
    required Map<String, List<String>> columnMapping,
    required Map<String, double> columnWidths,
    bool isZebra = false,
    bool isToday = false,
  }) {
    return Container(
      height: tableRowHeight,
      decoration: getTableRowDecoration(isZebra),
      child: Row(
        children: columnMapping.keys.map((key) {
          final width = columnWidths[key] ?? 60.0;
          final index = columnMapping.keys.toList().indexOf(key);
          final cellData = index < data.length ? data[index] : '';
          
          return Container(
            width: width,
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(
              horizontal: itemSpacing / 2,
              vertical: itemSpacing / 2,
            ),
            decoration: tableCellDecoration,
            child: Text(
              cellData,
              style: isToday 
                  ? boldBodyStyle.copyWith(color: accent)
                  : key.contains('time') ? monoStyle : bodyStyle,
              textAlign: TextAlign.center,
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Footer with page info and generation timestamp
  static Widget buildDocumentFooter({
    required int pageNumber,
    required int totalPages,
    required DateTime generationTime,
  }) {
    final timestamp = '${generationTime.day}/${generationTime.month}/${generationTime.year} '
        '${generationTime.hour.toString().padLeft(2, '0')}:'
        '${generationTime.minute.toString().padLeft(2, '0')}';
    
    return Container(
      margin: EdgeInsets.only(top: sectionSpacing),
      padding: EdgeInsets.symmetric(vertical: itemSpacing),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: tableLines, width: 1)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Generated: $timestamp',
            style: captionStyle.copyWith(fontSize: 10),
          ),
          Text(
            'Page $pageNumber of $totalPages',
            style: captionStyle.copyWith(fontSize: 10),
          ),
        ],
      ),
    );
  }

  /// Column definitions for prayer times table
  static const List<String> prayerTableColumns = [
    'Date',
    'Gregorian',
    'Hijri', 
    'Fajr',
    'Dhuhr',
    'Asr',
    'Maghrib',
    'Isha',
  ];

  /// Column mapping for data extraction
  static const Map<String, List<String>> columnDataMapping = {
    'date': ['date'],
    'gregorian': ['gregorian'],
    'hijri': ['hijri'],
    'fajr': ['fajr'],
    'dhuhr': ['dhuhr'],
    'asr': ['asr'],
    'maghrib': ['maghrib'],
    'isha': ['isha'],
  };
}
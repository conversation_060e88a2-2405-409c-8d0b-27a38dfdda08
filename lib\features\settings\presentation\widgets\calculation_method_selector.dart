import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/app_config.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../bloc/settings_bloc.dart';
import '../../../prayer_times/presentation/bloc/prayer_times_bloc.dart' as prayer_bloc;

class CalculationMethodSelector extends StatelessWidget {
  const CalculationMethodSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return GlassmorphismContainer(
      forceTransparent: true,
      borderRadius: 20,
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'Calculation Method',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: AppConfig.smallPadding),
          Text(
            'Select the method used to calculate prayer times',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          BlocBuilder<SettingsBloc, SettingsState>(
            builder: (context, state) {
              return SizedBox(
                height: 400,
                child: ListView.builder(
                  itemCount: AppConfig.calculationMethods.length,
                  itemBuilder: (context, index) {
                    final methodId = AppConfig.calculationMethods.keys.elementAt(index);
                    final methodName = AppConfig.calculationMethods[methodId]!;
                    final isSelected = state.calculationMethod == methodId;
                    final isDefault = methodId == AppConfig.defaultCalculationMethod;

                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Colors.grey[300],
                        child: Text(
                          methodId.toString(),
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.grey[600],
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      title: Text(
                        methodName,
                        style: TextStyle(
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        ),
                      ),
                      subtitle: isDefault
                          ? Text(
                              'Default',
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            )
                          : null,
                      trailing: isSelected
                          ? Icon(
                              Icons.check,
                              color: Theme.of(context).colorScheme.primary,
                            )
                          : null,
                      onTap: () {
                        // Update settings
                        context.read<SettingsBloc>().add(
                          UpdateCalculationMethod(method: methodId),
                        );
                        
                        // Update prayer times with new method
                        context.read<prayer_bloc.PrayerTimesBloc>().add(
                          prayer_bloc.UpdateCalculationMethod(calculationMethod: methodId),
                        );
                        
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

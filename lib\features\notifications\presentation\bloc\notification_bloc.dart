import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../domain/usecases/schedule_prayer_notifications.dart';
import '../../../prayer_times/domain/entities/prayer_times.dart';

part 'notification_event.dart';
part 'notification_state.dart';

class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final SchedulePrayerNotifications schedulePrayerNotifications;

  NotificationBloc({
    required this.schedulePrayerNotifications,
  }) : super(NotificationInitial()) {
    on<ScheduleNotificationsEvent>(_onScheduleNotifications);
    on<ScheduleSelectiveNotificationsEvent>(_onScheduleSelectiveNotifications);
    on<CancelNotificationsEvent>(_onCancelNotifications);
  }

  Future<void> _onScheduleNotifications(
    ScheduleNotificationsEvent event,
    Emitter<NotificationState> emit,
  ) async {
    emit(NotificationLoading());

    final result = await schedulePrayerNotifications(
      SchedulePrayerNotificationsParams(
        prayerTimes: event.prayerTimes,
        adhanEnabled: event.adhanEnabled,
      ),
    );

    result.fold(
      (failure) => emit(NotificationError(message: failure.message)),
      (_) => emit(NotificationScheduled()),
    );
  }

  Future<void> _onScheduleSelectiveNotifications(
    ScheduleSelectiveNotificationsEvent event,
    Emitter<NotificationState> emit,
  ) async {
    emit(NotificationLoading());

    final result = await schedulePrayerNotifications.scheduleSelective(
      event.prayerTimes,
      event.adhanEnabled,
    );

    result.fold(
      (failure) => emit(NotificationError(message: failure.message)),
      (_) => emit(NotificationScheduled()),
    );
  }

  Future<void> _onCancelNotifications(
    CancelNotificationsEvent event,
    Emitter<NotificationState> emit,
  ) async {
    emit(NotificationLoading());
    // TODO: Implement cancel notifications
    emit(NotificationCancelled());
  }
}

import 'package:flutter_test/flutter_test.dart';
import 'package:gebet_app/features/prayer_times/domain/entities/prayer_times.dart';
import 'package:gebet_app/features/prayer_times/domain/services/export_service.dart';

void main() {
  group('Export Service Tests', () {
    late List<PrayerTimes> testPrayerTimes;

    setUp(() {
      testPrayerTimes = [
        PrayerTimes(
          date: DateTime(2024, 1, 1),
          fajr: '05:30',
          sunrise: '06:45',
          dhuhr: '12:15',
          asr: '15:30',
          sunset: '17:45',
          maghrib: '18:00',
          isha: '19:30',
          imsak: '05:20',
          midnight: '00:15',
          city: 'Test City',
          country: 'Test Country',
          latitude: 21.3891,
          longitude: 39.8579,
          calculationMethod: 4,
          timezone: 'Asia/Riyadh',
        ),
        PrayerTimes(
          date: DateTime(2024, 1, 2),
          fajr: '05:31',
          sunrise: '06:46',
          dhuhr: '12:16',
          asr: '15:31',
          sunset: '17:46',
          maghrib: '18:01',
          isha: '19:31',
          imsak: '05:21',
          midnight: '00:16',
          city: 'Test City',
          country: 'Test Country',
          latitude: 21.3891,
          longitude: 39.8579,
          calculationMethod: 4,
          timezone: 'Asia/Riyadh',
        ),
      ];
    });

    test('should generate PDF without errors', () async {
      expect(() async {
        await ExportService.generatePDF(
          prayerTimesList: testPrayerTimes,
          title: 'Test Prayer Times',
          languageCode: 'en',
          viewType: 'week',
          hijriOffset: 0,
        );
      }, returnsNormally);
    });

    test('should generate CSV with correct format', () {
      final csv = ExportService.generateCSV(
        prayerTimesList: testPrayerTimes,
        languageCode: 'en',
        hijriOffset: 0,
      );

      expect(csv, contains('Date,Hijri Date,Fajr,Dhuhr,Asr,Maghrib,Isha'));
      expect(csv, contains('2024-01-01'));
      expect(csv, contains('05:30'));
      expect(csv, contains('12:15'));
    });

    test('should handle hijri offset correctly', () {
      final csv = ExportService.generateCSV(
        prayerTimesList: testPrayerTimes,
        languageCode: 'en',
        hijriOffset: 1,
      );

      expect(csv, isNotEmpty);
      expect(csv, contains('Date,Hijri Date,Fajr,Dhuhr,Asr,Maghrib,Isha'));
    });
  });
}

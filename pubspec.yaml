name: <PERSON><PERSON><PERSON>_prayer_times
description: AlFalah - Modern Islamic prayer time app by Eddars Global with offline support, accurate calculations, and beautiful design.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  flutter_bloc: ^9.1.1
  equatable: ^2.0.5
  
  # HTTP & API
  http: ^1.1.0
  dio: ^5.3.2
  dartz: ^0.10.1
  
  # Location Services
  geolocator: ^14.0.2
  geocoding: ^4.0.0
  
  # Maps
  flutter_map: ^8.2.1
  latlong2: ^0.9.1
  
  # Database & Storage
  sqflite: ^2.3.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.1
  shared_preferences: ^2.2.2
  
  # Notifications
  flutter_local_notifications: ^19.4.0
  timezone: ^0.10.1
  hijri: ^3.0.0  # For Umm al-Qura calendar

  # Audio
  audioplayers: ^6.5.0
  
  # Internationalization
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  
  # UI & Design
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  image_picker: ^1.0.4
  cached_network_image: ^3.3.0
  
  # Utilities
  permission_handler: ^12.0.1
  url_launcher: ^6.2.1
  package_info_plus: ^8.3.1
  get_it: ^8.2.0
  connectivity_plus: ^6.0.5

  # Qibla Compass
  sensors_plus: ^4.0.2

  # Export and Share
  pdf: ^3.10.4
  printing: ^5.11.0
  share_plus: ^7.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.7
  flutter_launcher_icons: ^0.13.1

flutter_launcher_icons:
  android: true
  ios: false
  image_path: "assets/icons/Alfalah.png"




flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/audio/
    - assets/icons/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Medium.ttf
          weight: 500
        - asset: fonts/Roboto-Bold.ttf
          weight: 700

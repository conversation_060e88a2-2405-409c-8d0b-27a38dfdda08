import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../prayer_times/domain/entities/prayer_times.dart';

abstract class NotificationRepository {
  Future<Either<Failure, void>> schedulePrayerNotifications(PrayerTimes prayerTimes, bool adhanEnabled);
  Future<Either<Failure, void>> scheduleSelectivePrayerNotifications(PrayerTimes prayerTimes, bool adhanEnabled);
  Future<Either<Failure, void>> cancelAllNotifications();
  Future<Either<Failure, bool>> requestPermissions();
}

part of 'qibla_bloc.dart';

abstract class QiblaEvent extends Equatable {
  const QiblaEvent();

  @override
  List<Object?> get props => [];
}

/// Start Qibla compass
class StartQiblaCompass extends QiblaEvent {
  const StartQiblaCompass();
}

/// Stop Qibla compass
class StopQiblaCompass extends QiblaEvent {
  const StopQiblaCompass();
}

/// Update user location (from GPS or manual input)
class UpdateUserLocation extends QiblaEvent {
  final double latitude;
  final double longitude;
  final bool isManual;

  const UpdateUserLocation({
    required this.latitude,
    required this.longitude,
    this.isManual = false,
  });

  @override
  List<Object?> get props => [latitude, longitude, isManual];
}

/// Compass reading received from sensor
class CompassReadingReceived extends QiblaEvent {
  final double heading;
  final double accuracy;
  final CompassStatus status;

  const CompassReadingReceived({
    required this.heading,
    required this.accuracy,
    required this.status,
  });

  @override
  List<Object?> get props => [heading, accuracy, status];
}

/// Switch between compass and map mode
class SwitchQiblaMode extends QiblaEvent {
  final QiblaMode mode;

  const SwitchQiblaMode(this.mode);

  @override
  List<Object?> get props => [mode];
}

/// Calibrate compass
class CalibrateCompass extends QiblaEvent {
  const CalibrateCompass();
}

/// Reset Qibla compass
class ResetQiblaCompass extends QiblaEvent {
  const ResetQiblaCompass();
}

/// Request location permission
class RequestLocationPermission extends QiblaEvent {
  const RequestLocationPermission();
}

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/prayer_times.dart';
import '../entities/location.dart';

abstract class PrayerTimesRepository {
  /// Get prayer times for a specific date and location
  Future<Either<Failure, PrayerTimes>> getPrayerTimes({
    required DateTime date,
    required double latitude,
    required double longitude,
    required int calculationMethod,
  });

  /// Get prayer times by city name
  Future<Either<Failure, PrayerTimes>> getPrayerTimesByCity({
    required DateTime date,
    required String city,
    required String country,
    required int calculationMethod,
  });

  /// Get prayer times by address
  Future<Either<Failure, PrayerTimes>> getPrayerTimesByAddress({
    required DateTime date,
    required String address,
    required int calculationMethod,
  });

  /// Get prayer times for a month (for caching)
  Future<Either<Failure, List<PrayerTimes>>> getMonthlyPrayerTimes({
    required DateTime month,
    required double latitude,
    required double longitude,
    required int calculationMethod,
  });

  /// Get prayer times for a date range (Week/Month views)
  Future<Either<Failure, List<PrayerTimes>>> getPrayerTimesRange({
    required DateTime startDate,
    required DateTime endDate,
    required double latitude,
    required double longitude,
    required int calculationMethod,
  });

  /// Cache prayer times locally
  Future<Either<Failure, void>> cachePrayerTimes({
    required List<PrayerTimes> prayerTimes,
    required String cacheKey,
  });

  /// Get cached prayer times
  Future<Either<Failure, PrayerTimes?>> getCachedPrayerTimes({
    required DateTime date,
    required String cacheKey,
  });

  /// Get cached monthly prayer times
  Future<Either<Failure, List<PrayerTimes>?>> getCachedMonthlyPrayerTimes({
    required DateTime month,
    required String cacheKey,
  });

  /// Clear expired cache
  Future<Either<Failure, void>> clearExpiredCache();

  /// Check if cache is valid for a specific date
  Future<bool> isCacheValid({
    required DateTime date,
    required String cacheKey,
  });
}

import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive/hive.dart';

import '../features/prayer_times/data/datasources/prayer_times_remote_datasource.dart';
import '../features/prayer_times/data/datasources/prayer_times_local_datasource.dart';
import '../features/prayer_times/data/repositories/prayer_times_repository_impl.dart';
import '../features/prayer_times/domain/repositories/prayer_times_repository.dart';
import '../features/prayer_times/domain/usecases/get_prayer_times.dart';
import '../features/prayer_times/domain/usecases/cache_prayer_times.dart';
import '../features/prayer_times/presentation/bloc/prayer_times_bloc.dart';

import '../features/location/data/datasources/location_datasource.dart';
import '../features/location/data/repositories/location_repository_impl.dart';
import '../features/location/domain/repositories/location_repository.dart';
import '../features/location/domain/usecases/get_current_location.dart';
import '../features/location/domain/usecases/search_location.dart';
import '../features/location/presentation/bloc/location_bloc.dart';

import '../features/settings/data/datasources/settings_local_datasource.dart';
import '../features/settings/data/repositories/settings_repository_impl.dart';
import '../features/settings/domain/repositories/settings_repository.dart';
import '../features/settings/domain/usecases/get_settings.dart';
import '../features/settings/domain/usecases/save_settings.dart';
import '../features/settings/presentation/bloc/settings_bloc.dart';

import '../features/notifications/data/datasources/notification_datasource.dart';
import '../features/notifications/data/datasources/notification_settings_datasource.dart';
import '../features/notifications/data/repositories/notification_repository_impl.dart';
import '../features/notifications/domain/repositories/notification_repository.dart';
import '../features/notifications/domain/usecases/schedule_prayer_notifications.dart';
import '../features/notifications/presentation/bloc/notification_bloc.dart';

import '../features/qibla/data/services/compass_sensor_service.dart';
import '../features/qibla/presentation/bloc/qibla_bloc.dart';
import 'services/connectivity_service.dart';

class DependencyInjection {
  static final GetIt instance = GetIt.instance;

  static Future<void> init() async {
    // External dependencies
    final sharedPreferences = await SharedPreferences.getInstance();
    if (!instance.isRegistered<SharedPreferences>()) {
      instance.registerLazySingleton(() => sharedPreferences);
    }

    final dio = Dio()
      ..options.connectTimeout = const Duration(seconds: 30)
      ..options.receiveTimeout = const Duration(seconds: 30);
    if (!instance.isRegistered<Dio>()) {
      instance.registerLazySingleton(() => dio);
    }

    // Hive boxes (avec instanceName pour éviter les conflits)
    final prayerTimesBox = await Hive.openBox('prayer_times_cache');
    if (!instance.isRegistered<Box>(instanceName: 'prayer_times_cache')) {
      instance.registerLazySingleton<Box>(() => prayerTimesBox,
          instanceName: 'prayer_times_cache');
    }

    final settingsBox = await Hive.openBox('app_settings');
    if (!instance.isRegistered<Box>(instanceName: 'app_settings')) {
      instance.registerLazySingleton<Box>(() => settingsBox,
          instanceName: 'app_settings');
    }

    // Data sources
    if (!instance.isRegistered<PrayerTimesRemoteDataSource>()) {
      instance.registerLazySingleton<PrayerTimesRemoteDataSource>(
        () => PrayerTimesRemoteDataSourceImpl(dio: instance()),
      );
    }

    if (!instance.isRegistered<PrayerTimesLocalDataSource>()) {
      instance.registerLazySingleton<PrayerTimesLocalDataSource>(
        () => PrayerTimesLocalDataSourceImpl(
          box: instance<Box>(instanceName: 'prayer_times_cache'),
        ),
      );
    }

    if (!instance.isRegistered<LocationDataSource>()) {
      instance.registerLazySingleton<LocationDataSource>(
        () => LocationDataSourceImpl(),
      );
    }

    if (!instance.isRegistered<SettingsLocalDataSource>()) {
      instance.registerLazySingleton<SettingsLocalDataSource>(
        () => SettingsLocalDataSourceImpl(
          sharedPreferences: instance(),
          box: instance<Box>(instanceName: 'app_settings'),
        ),
      );
    }

    if (!instance.isRegistered<NotificationDataSource>()) {
      instance.registerLazySingleton<NotificationDataSource>(
        () => NotificationDataSourceImpl(),
      );
    }

    // Register NotificationSettingsDatasource with proper async initialization
    if (!instance.isRegistered<NotificationSettingsDatasource>()) {
      final notificationSettingsDatasource = NotificationSettingsDatasource();
      await notificationSettingsDatasource.init();
      instance.registerLazySingleton<NotificationSettingsDatasource>(
        () => notificationSettingsDatasource,
      );
    }

    // Repositories
    if (!instance.isRegistered<PrayerTimesRepository>()) {
      instance.registerLazySingleton<PrayerTimesRepository>(
        () => PrayerTimesRepositoryImpl(
          remoteDataSource: instance(),
          localDataSource: instance(),
        ),
      );
    }

    if (!instance.isRegistered<LocationRepository>()) {
      instance.registerLazySingleton<LocationRepository>(
        () => LocationRepositoryImpl(dataSource: instance()),
      );
    }

    if (!instance.isRegistered<SettingsRepository>()) {
      instance.registerLazySingleton<SettingsRepository>(
        () => SettingsRepositoryImpl(localDataSource: instance()),
      );
    }

    if (!instance.isRegistered<NotificationRepository>()) {
      instance.registerLazySingleton<NotificationRepository>(
        () => NotificationRepositoryImpl(dataSource: instance()),
      );
    }

    // Use cases
    if (!instance.isRegistered<GetPrayerTimes>()) {
      instance.registerLazySingleton(() => GetPrayerTimes(instance()));
    }
    if (!instance.isRegistered<CachePrayerTimes>()) {
      instance.registerLazySingleton(() => CachePrayerTimes(instance()));
    }
    if (!instance.isRegistered<GetCurrentLocation>()) {
      instance.registerLazySingleton(() => GetCurrentLocation(instance()));
    }
    if (!instance.isRegistered<SearchLocation>()) {
      instance.registerLazySingleton(() => SearchLocation(instance()));
    }
    if (!instance.isRegistered<GetSettings>()) {
      instance.registerLazySingleton(() => GetSettings(instance()));
    }
    if (!instance.isRegistered<SaveSettings>()) {
      instance.registerLazySingleton(() => SaveSettings(instance()));
    }
    if (!instance.isRegistered<SchedulePrayerNotifications>()) {
      instance.registerLazySingleton(
          () => SchedulePrayerNotifications(instance()));
    }

    // Qibla services
    if (!instance.isRegistered<CompassSensorService>()) {
      instance.registerLazySingleton(() => CompassSensorService());
    }

    // Connectivity service
    if (!instance.isRegistered<ConnectivityService>()) {
      instance.registerLazySingleton<ConnectivityService>(() => ConnectivityServiceImpl());
    }

    // Blocs (factories = toujours une nouvelle instance)
    instance.registerFactory(() => PrayerTimesBloc(
          getPrayerTimes: instance(),
          cachePrayerTimes: instance(),
        ));

    instance.registerFactory(() => LocationBloc(
          getCurrentLocation: instance(),
          searchLocation: instance(),
        ));

    instance.registerFactory(() => SettingsBloc(
          getSettings: instance(),
          saveSettings: instance(),
        ));

    instance.registerFactory(() => NotificationBloc(
          schedulePrayerNotifications: instance(),
        ));

    instance.registerFactory(() => QiblaBloc(
          compassService: instance(),
        ));
  }
}

import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../data/services/compass_sensor_service.dart';

/// Enhanced calibration guide widget with live feedback
class CalibrationGuideWidget extends StatefulWidget {
  final CompassReading compassReading;
  final VoidCallback onCalibrationComplete;
  
  const CalibrationGuideWidget({
    super.key,
    required this.compassReading,
    required this.onCalibrationComplete,
  });

  @override
  State<CalibrationGuideWidget> createState() => _CalibrationGuideWidgetState();
}

class _CalibrationGuideWidgetState extends State<CalibrationGuideWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  
  int _lastQuality = 0;

  @override
  void initState() {
    super.initState();
    
    // Pulse animation for quality feedback
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    
    // Rotation animation for figure-8 guide
    _rotationController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(begin: 0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );
    
    _rotationController.repeat();
  }

  @override
  void didUpdateWidget(CalibrationGuideWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Trigger haptic feedback on quality improvements
    if (widget.compassReading.calibrationQuality > _lastQuality + 10) {
      HapticFeedback.lightImpact();
      _pulseController.forward().then((_) => _pulseController.reverse());
    }
    
    // Complete calibration when quality is excellent
    if (widget.compassReading.calibrationQuality >= 90 && 
        widget.compassReading.calibrationPhase == CalibrationPhase.complete) {
      HapticFeedback.heavyImpact();
      widget.onCalibrationComplete();
    }
    
    _lastQuality = widget.compassReading.calibrationQuality;
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final reading = widget.compassReading;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            reading.qualityColor.withOpacity(0.1),
            reading.qualityColor.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: reading.qualityColor.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Column(
        children: [
          // Phase indicator
          _buildPhaseIndicator(reading),
          const SizedBox(height: 20),
          
          // Quality score with animated feedback
          _buildQualityScore(reading),
          const SizedBox(height: 20),
          
          // Movement guide animation
          _buildMovementGuide(reading),
          const SizedBox(height: 20),
          
          // Real-time feedback
          _buildRealtimeFeedback(reading),
        ],
      ),
    );
  }
  
  Widget _buildPhaseIndicator(CompassReading reading) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          reading.calibrationPhase.icon,
          color: reading.qualityColor,
          size: 28,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Phase ${reading.calibrationPhase.index + 1}/4',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey,
                ),
              ),
              Text(
                reading.calibrationPhase.description,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: reading.qualityColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  Widget _buildQualityScore(CompassReading reading) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  reading.qualityColor.withOpacity(0.8),
                  reading.qualityColor.withOpacity(0.3),
                ],
              ),
              border: Border.all(
                color: reading.qualityColor,
                width: 3,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${reading.calibrationQuality}%',
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  reading.qualityDescription,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildMovementGuide(CompassReading reading) {
    return SizedBox(
      height: 100,
      child: AnimatedBuilder(
        animation: _rotationAnimation,
        builder: (context, child) {
          return CustomPaint(
            painter: AnimatedFigure8Painter(
              animationValue: _rotationAnimation.value,
              phase: reading.calibrationPhase,
              quality: reading.calibrationQuality,
            ),
            size: const Size(double.infinity, 100),
          );
        },
      ),
    );
  }
  
  Widget _buildRealtimeFeedback(CompassReading reading) {
    String feedbackText;
    IconData feedbackIcon;
    
    switch (reading.calibrationPhase) {
      case CalibrationPhase.preparation:
        feedbackText = 'Hold device flat and steady...';
        feedbackIcon = Icons.phone_android;
        break;
      case CalibrationPhase.figure8Movement:
        if (reading.calibrationQuality < 40) {
          feedbackText = 'Move in figure-8 pattern - ${reading.calibrationQuality}% complete';
          feedbackIcon = Icons.all_out;
        } else {
          feedbackText = 'Good movement! Continue figure-8...';
          feedbackIcon = Icons.check_circle_outline;
        }
        break;
      case CalibrationPhase.tiltMovement:
        feedbackText = 'Tilt device in all directions - ${reading.calibrationQuality}% complete';
        feedbackIcon = Icons.screen_rotation;
        break;
      case CalibrationPhase.finalizing:
        feedbackText = 'Processing calibration data...';
        feedbackIcon = Icons.hourglass_empty;
        break;
      case CalibrationPhase.complete:
        feedbackText = 'Calibration complete! Accuracy: ${reading.qualityDescription}';
        feedbackIcon = Icons.check_circle;
        break;
    }
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: reading.qualityColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: reading.qualityColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            feedbackIcon,
            color: reading.qualityColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              feedbackText,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: reading.qualityColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Animated painter for figure-8 movement guide
class AnimatedFigure8Painter extends CustomPainter {
  final double animationValue;
  final CalibrationPhase phase;
  final int quality;
  
  AnimatedFigure8Painter({
    required this.animationValue,
    required this.phase,
    required this.quality,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 8;
    
    // Base path paint
    final pathPaint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    // Active path paint
    final activePaint = Paint()
      ..color = _getPhaseColor()
      ..strokeWidth = 4
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;
    
    // Draw figure-8 path
    final path = Path();
    
    // Left circle
    final leftCenter = Offset(center.dx - radius * 1.5, center.dy);
    path.addOval(Rect.fromCircle(center: leftCenter, radius: radius));
    
    // Right circle
    final rightCenter = Offset(center.dx + radius * 1.5, center.dy);
    path.addOval(Rect.fromCircle(center: rightCenter, radius: radius));
    
    canvas.drawPath(path, pathPaint);
    
    // Draw animated progress
    if (phase == CalibrationPhase.figure8Movement) {
      _drawAnimatedProgress(canvas, center, radius, activePaint);
    }
    
    // Draw device icon at current position
    _drawDeviceIcon(canvas, center, radius);
  }
  
  void _drawAnimatedProgress(Canvas canvas, Offset center, double radius, Paint paint) {
    final progress = (animationValue % 1.0);
    final leftCenter = Offset(center.dx - radius * 1.5, center.dy);
    final rightCenter = Offset(center.dx + radius * 1.5, center.dy);
    
    // Calculate position on figure-8
    Offset currentPos;
    if (progress < 0.5) {
      // Left circle
      final angle = progress * 4 * math.pi;
      currentPos = Offset(
        leftCenter.dx + radius * math.cos(angle),
        leftCenter.dy + radius * math.sin(angle),
      );
    } else {
      // Right circle
      final angle = (progress - 0.5) * 4 * math.pi;
      currentPos = Offset(
        rightCenter.dx + radius * math.cos(-angle),
        rightCenter.dy + radius * math.sin(-angle),
      );
    }
    
    // Draw progress indicator
    canvas.drawCircle(currentPos, 8, paint);
  }
  
  void _drawDeviceIcon(Canvas canvas, Offset center, double radius) {
    final devicePaint = Paint()
      ..color = _getPhaseColor()
      ..style = PaintingStyle.fill;
    
    // Simple device representation
    final deviceRect = RRect.fromRectAndRadius(
      Rect.fromCenter(center: center, width: 20, height: 30),
      const Radius.circular(4),
    );
    
    canvas.drawRRect(deviceRect, devicePaint);
  }
  
  Color _getPhaseColor() {
    switch (phase) {
      case CalibrationPhase.preparation:
        return Colors.blue;
      case CalibrationPhase.figure8Movement:
        return Colors.orange;
      case CalibrationPhase.tiltMovement:
        return Colors.purple;
      case CalibrationPhase.finalizing:
        return Colors.amber;
      case CalibrationPhase.complete:
        return Colors.green;
    }
  }

  @override
  bool shouldRepaint(AnimatedFigure8Painter oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
           oldDelegate.phase != phase ||
           oldDelegate.quality != quality;
  }
}

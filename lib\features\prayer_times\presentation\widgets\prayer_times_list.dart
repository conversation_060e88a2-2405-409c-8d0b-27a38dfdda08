import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:get_it/get_it.dart';
import '../../../../core/app_config.dart';
import '../../../../core/l10n/app_localizations.dart';
import '../../../../core/models/prayer_notification_settings.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../../../../core/theme/tokens.dart';
import '../../domain/entities/prayer_times.dart';
import '../../../settings/presentation/bloc/settings_bloc.dart';
import '../../../notifications/presentation/bloc/notification_bloc.dart';
import '../../../notifications/data/datasources/notification_settings_datasource.dart';

class PrayerTimesList extends StatelessWidget {
  final PrayerTimes prayerTimes;

  const PrayerTimesList({
    super.key,
    required this.prayerTimes,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final now = DateTime.now();
    final currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    
    return Column(
      children: prayerTimes.prayersList.map((prayer) {
        final isNext = _isNextPrayer(prayer, currentTime, prayerTimes.prayersList);
        final isPassed = prayer.time.compareTo(currentTime) < 0;
        
        return GlassmorphismCard(
          margin: const EdgeInsets.only(bottom: AppConfig.smallPadding),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppConfig.cardBorderRadius),
              border: isNext
                  ? Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 2,
                    )
                  : null,
            ),
            child: BlocBuilder<NotificationBloc, NotificationState>(
              builder: (context, notificationState) {
                // Get notification settings from the same datasource used for toggling
                bool isNotificationEnabled = true; // Default to enabled
                try {
                  if (GetIt.instance.isRegistered<NotificationSettingsDatasource>()) {
                    final notificationSettings = GetIt.instance<NotificationSettingsDatasource>();
                    final prayerId = _getPrayerId(prayer.name);
                    isNotificationEnabled = notificationSettings.isNotificationEnabled(
                      date: prayerTimes.date,
                      prayerId: prayerId,
                    );
                  }
                } catch (e) {
                  // If there's an error reading settings, default to enabled
                  isNotificationEnabled = true;
                }

                return ListTile(
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppConfig.defaultPadding,
                vertical: AppConfig.smallPadding,
              ),
              leading: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _getPrayerContainerColor(context, isNext, isPassed),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  _getPrayerIcon(prayer.name),
                  color: _getPrayerIconColor(context, isNext, isPassed),
                  size: 24,
                ),
              ),
              title: Text(
                _getPrayerDisplayName(prayer.name, l10n),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: isNext ? FontWeight.w600 : FontWeight.w500,
                  color: _getPrayerTextColor(context, isNext, isPassed),
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        prayer.time,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: isNext ? FontWeight.bold : FontWeight.w500,
                          color: _getPrayerTimeColor(context, isNext, isPassed),
                        ),
                      ),
                      if (isNext)
                        Text(
                          'Next',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      if (isPassed && !isNext)
                        Text(
                          _formatDateContext(prayerTimes.date),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: _getPrayerTextColor(context, false, true),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(width: 12),
                  // Notification toggle button
                  GestureDetector(
                    onTap: () => _togglePrayerNotification(context, prayer.name, !isNotificationEnabled),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: isNotificationEnabled
                            ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                            : Theme.of(context).colorScheme.surfaceVariant,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: isNotificationEnabled
                              ? Theme.of(context).colorScheme.primary.withOpacity(0.3)
                              : Theme.of(context).colorScheme.outline,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        isNotificationEnabled ? Icons.notifications_active : Icons.notifications_off,
                        color: isNotificationEnabled
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.onSurfaceVariant,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
              onTap: () {
                // TODO: Show prayer details or options
                _showPrayerOptions(context, prayer, l10n);
              },
                );
              },
            ),
          ),
        );
      }).toList(),
    );
  }

  void _togglePrayerNotification(BuildContext context, String prayerName, bool enabled) async {
    try {
      // Get the notification settings datasource with safety check
      if (!GetIt.instance.isRegistered<NotificationSettingsDatasource>()) {
        throw Exception('Notification settings service not initialized');
      }
      
      final notificationSettings = GetIt.instance<NotificationSettingsDatasource>();

      // Map prayer name to prayer ID
      final prayerId = _getPrayerId(prayerName);

      // Update the specific prayer notification setting for today
      await notificationSettings.setNotificationEnabled(
        date: prayerTimes.date,
        prayerId: prayerId,
        enabled: enabled,
      );

      // Show feedback
      final prayerDisplayName = _getPrayerDisplayName(prayerName, AppLocalizations.of(context)!);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              enabled
                  ? '$prayerDisplayName notifications enabled'
                  : '$prayerDisplayName notifications disabled'
            ),
            backgroundColor: enabled ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 2),
          ),
        );

        // Reschedule notifications for this date
        context.read<NotificationBloc>().add(
          ScheduleSelectiveNotificationsEvent(
            prayerTimes: prayerTimes,
            adhanEnabled: context.read<SettingsBloc>().state.adhanEnabled,
          ),
        );
        
        // Force a rebuild by dispatching a notification event
        // This ensures the UI updates immediately
        Future.delayed(const Duration(milliseconds: 100), () {
          if (context.mounted) {
            context.read<NotificationBloc>().add(
              ScheduleSelectiveNotificationsEvent(
                prayerTimes: prayerTimes,
                adhanEnabled: context.read<SettingsBloc>().state.adhanEnabled,
              ),
            );
          }
        });
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating notification: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  bool _isNextPrayer(PrayerTime prayer, String currentTime, List<PrayerTime> prayers) {
    final nextPrayer = prayers.firstWhere(
      (p) => p.time.compareTo(currentTime) > 0,
      orElse: () => prayers.first, // If no prayer left today, next is Fajr
    );
    return prayer.name == nextPrayer.name;
  }

  IconData _getPrayerIcon(String prayerName) {
    switch (prayerName.toLowerCase()) {
      case 'fajr':
        return Icons.wb_twilight;
      case 'dhuhr':
        return Icons.wb_sunny;
      case 'asr':
        return Icons.wb_sunny_outlined;
      case 'maghrib':
        return Icons.wb_twilight;
      case 'isha':
        return Icons.nightlight;
      default:
        return Icons.access_time;
    }
  }

  String _getPrayerDisplayName(String prayerName, AppLocalizations l10n) {
    switch (prayerName.toLowerCase()) {
      case 'fajr':
        return l10n.fajr;
      case 'dhuhr':
        return l10n.dhuhr;
      case 'asr':
        return l10n.asr;
      case 'maghrib':
        return l10n.maghrib;
      case 'isha':
        return l10n.isha;
      default:
        return prayerName;
    }
  }

  String _getPrayerId(String prayerName) {
    return prayerName.toLowerCase();
  }

  String _formatDateContext(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateToCheck = DateTime(date.year, date.month, date.day);
    
    if (dateToCheck.isAtSameMomentAs(today)) {
      return 'Passed';
    } else if (dateToCheck.isAtSameMomentAs(yesterday)) {
      return 'Yesterday';
    } else {
      // Use a simple format for other dates
      final months = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
      ];
      return '${months[date.month - 1]} ${date.day}';
    }
  }

  /// Get prayer container color with better dark mode support
  Color _getPrayerContainerColor(BuildContext context, bool isNext, bool isPassed) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    if (isNext) {
      return Theme.of(context).colorScheme.primary;
    } else if (isPassed) {
      return isDark 
          ? Theme.of(context).colorScheme.outline.withOpacity(0.3)
          : Theme.of(context).colorScheme.outline;
    } else {
      return isDark 
          ? Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.7)
          : Theme.of(context).colorScheme.surfaceVariant;
    }
  }

  /// Get prayer icon color with better dark mode support
  Color _getPrayerIconColor(BuildContext context, bool isNext, bool isPassed) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    if (isNext) {
      return Colors.white;
    } else if (isPassed) {
      return isDark 
          ? Theme.of(context).colorScheme.onSurface.withOpacity(0.5)
          : Theme.of(context).colorScheme.onSurfaceVariant;
    } else {
      return isDark 
          ? Theme.of(context).colorScheme.onSurface.withOpacity(0.8)
          : Theme.of(context).colorScheme.primary;
    }
  }

  /// Get prayer text color with better dark mode support
  Color? _getPrayerTextColor(BuildContext context, bool isNext, bool isPassed) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    if (isPassed) {
      return isDark 
          ? Theme.of(context).colorScheme.onSurface.withOpacity(0.5)
          : Theme.of(context).colorScheme.onSurfaceVariant;
    } else if (isNext) {
      return Theme.of(context).colorScheme.primary;
    } else {
      return isDark 
          ? Theme.of(context).colorScheme.onSurface.withOpacity(0.9)
          : null; // Use default color in light mode
    }
  }

  /// Get prayer time color with better dark mode support
  Color? _getPrayerTimeColor(BuildContext context, bool isNext, bool isPassed) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    if (isNext) {
      return Theme.of(context).colorScheme.primary;
    } else if (isPassed) {
      return isDark 
          ? Theme.of(context).colorScheme.onSurface.withOpacity(0.5)
          : Theme.of(context).colorScheme.onSurfaceVariant;
    } else {
      return isDark 
          ? Theme.of(context).colorScheme.onSurface.withOpacity(0.8)
          : null; // Use default color in light mode
    }
  }

  void _showPrayerOptions(BuildContext context, PrayerTime prayer, AppLocalizations l10n) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: AppConfig.defaultPadding),
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Icon(
                    _getPrayerIcon(prayer.name),
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppConfig.defaultPadding),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getPrayerDisplayName(prayer.name, l10n),
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    Text(
                      prayer.time,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: AppConfig.largePadding),
            ValueListenableBuilder<Box<PrayerNotificationSettings>>(
              valueListenable: Hive.box<PrayerNotificationSettings>('prayer_notifications').listenable(),
              builder: (context, box, _) {
                final settings = box.get('settings') ?? PrayerNotificationSettings();
                final isPrayerNotificationEnabled = settings.isPrayerEnabled(prayer.name);
                
                return BlocBuilder<SettingsBloc, SettingsState>(
                  builder: (context, settingsState) {
                    return Column(
                      children: [
                        // Individual Prayer Notification Toggle
                        SwitchListTile(
                          title: Text('${_getPrayerDisplayName(prayer.name, l10n)} Notification'),
                          subtitle: const Text('Enable notification for this prayer'),
                          value: isPrayerNotificationEnabled,
                          onChanged: (value) {
                            _togglePrayerNotification(context, prayer.name, value);
                          },
                        ),

                        // Adhan Toggle (only if notifications are enabled globally and for this prayer)
                        if (settingsState.notificationsEnabled && isPrayerNotificationEnabled)
                          SwitchListTile(
                            title: Text('${_getPrayerDisplayName(prayer.name, l10n)} Adhan'),
                            subtitle: Text('Play Adhan sound for ${_getPrayerDisplayName(prayer.name, l10n)}'),
                            value: settingsState.adhanEnabled,
                            onChanged: (value) {
                              context.read<SettingsBloc>().add(
                                UpdateAdhanEnabled(enabled: value),
                              );
                            },
                          ),

                    const SizedBox(height: AppConfig.defaultPadding),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.pop(context);
                              // Schedule notification for this specific prayer
                              context.read<NotificationBloc>().add(
                                ScheduleNotificationsEvent(
                                  prayerTimes: prayerTimes,
                                  adhanEnabled: settingsState.adhanEnabled,
                                ),
                              );
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('${prayer.name} notification scheduled'),
                                ),
                              );
                            },
                            icon: const Icon(Icons.schedule),
                            label: const Text('Schedule'),
                          ),
                        ),
                        const SizedBox(width: AppConfig.smallPadding),
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () {
                              Navigator.pop(context);
                              // TODO: Share prayer time
                            },
                            icon: const Icon(Icons.share),
                            label: const Text('Share'),
                          ),
                        ),
                      ],
                    ),
                      ],
                    );
                  },
                );
              },
            ),
            const SizedBox(height: AppConfig.smallPadding),
          ],
        ),
      ),
    );
  }
}

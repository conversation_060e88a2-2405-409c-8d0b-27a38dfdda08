import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import '../../../../core/app_config.dart';
import '../../../../core/l10n/app_localizations.dart';
import '../../domain/entities/location_entity.dart';
import '../bloc/location_bloc.dart';
import '../../../prayer_times/presentation/bloc/prayer_times_bloc.dart';
import '../../../settings/presentation/bloc/settings_bloc.dart';

class MapPage extends StatefulWidget {
  final LocationEntity? initialLocation;

  const MapPage({super.key, this.initialLocation});

  @override
  State<MapPage> createState() => _MapPageState();
}

class _MapPageState extends State<MapPage> {
  final MapController _mapController = MapController();
  final TextEditingController _searchController = TextEditingController();
  LatLng? _selectedLocation;
  LocationEntity? _selectedLocationEntity;

  @override
  void initState() {
    super.initState();
    _selectedLocation = widget.initialLocation != null
        ? LatLng(widget.initialLocation!.latitude, widget.initialLocation!.longitude)
        : const LatLng(AppConfig.defaultLatitude, AppConfig.defaultLongitude);
    _selectedLocationEntity = widget.initialLocation;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.location),
        actions: [
          IconButton(
            icon: const Icon(Icons.my_location),
            onPressed: () {
              context.read<LocationBloc>().add(GetCurrentLocationEvent());
            },
          ),
          IconButton(
            icon: const Icon(Icons.check),
            onPressed: _selectedLocationEntity != null
                ? () {
                    // Save location to settings
                    context.read<SettingsBloc>().add(UpdateCustomLocation(
                      latitude: _selectedLocationEntity!.latitude,
                      longitude: _selectedLocationEntity!.longitude,
                      locationName: _selectedLocationEntity!.city,
                    ));
                    
                    // Update prayer times with selected location
                    context.read<PrayerTimesBloc>().add(UpdateLocation(
                      latitude: _selectedLocationEntity!.latitude,
                      longitude: _selectedLocationEntity!.longitude,
                    ));
                    Navigator.pop(context, _selectedLocationEntity);
                  }
                : null,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(AppConfig.defaultPadding),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search for a location...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                  },
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConfig.buttonBorderRadius),
                ),
              ),
              onSubmitted: (query) {
                if (query.isNotEmpty) {
                  context.read<LocationBloc>().add(SearchLocationEvent(query: query));
                }
              },
            ),
          ),
          
          // Map
          Expanded(
            flex: 3,
            child: FlutterMap(
              mapController: _mapController,
              options: MapOptions(
                initialCenter: _selectedLocation ?? const LatLng(AppConfig.defaultLatitude, AppConfig.defaultLongitude),
                initialZoom: 10.0,
                onTap: (tapPosition, point) {
                  setState(() {
                    _selectedLocation = point;
                  });
                  // Get location details for the tapped point
                  _getLocationDetails(point.latitude, point.longitude);
                },
              ),
              children: [
                TileLayer(
                  urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                  userAgentPackageName: 'com.gebet.app',
                ),
                if (_selectedLocation != null)
                  MarkerLayer(
                    markers: [
                      Marker(
                        point: _selectedLocation!,
                        width: 40,
                        height: 40,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                          child: const Icon(
                            Icons.location_on,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
          
          // Location Info and Search Results
          Expanded(
            flex: 2,
            child: BlocListener<LocationBloc, LocationState>(
              listener: (context, state) {
                if (state is LocationLoaded) {
                  setState(() {
                    _selectedLocation = LatLng(state.location.latitude, state.location.longitude);
                    _selectedLocationEntity = state.location;
                  });
                  _mapController.move(_selectedLocation!, 12.0);
                }
              },
              child: BlocBuilder<LocationBloc, LocationState>(
                builder: (context, state) {
                  if (state is LocationLoading) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (state is LocationSearching) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (state is LocationSearchResults) {
                    return _buildSearchResults(state.locations);
                  } else if (state is LocationError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 48,
                            color: Theme.of(context).colorScheme.error,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            state.message,
                            style: Theme.of(context).textTheme.bodyMedium,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  } else {
                    return _buildLocationInfo();
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults(List<LocationEntity> locations) {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      itemCount: locations.length,
      itemBuilder: (context, index) {
        final location = locations[index];
        return Card(
          child: ListTile(
            leading: const Icon(Icons.location_on),
            title: Text(location.displayName),
            subtitle: Text(location.coordinates),
            onTap: () {
              context.read<LocationBloc>().add(SelectLocationEvent(location: location));
            },
          ),
        );
      },
    );
  }

  Widget _buildLocationInfo() {
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Location',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: AppConfig.smallPadding),
          if (_selectedLocationEntity != null) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConfig.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _selectedLocationEntity!.displayName,
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _selectedLocationEntity!.coordinates,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    if (_selectedLocationEntity!.address != null &&
                        _selectedLocationEntity!.address!.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        _selectedLocationEntity!.address!,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ] else if (_selectedLocation != null) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConfig.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Coordinates',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${_selectedLocation!.latitude.toStringAsFixed(4)}, ${_selectedLocation!.longitude.toStringAsFixed(4)}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ),
          ],
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'Tap on the map to select a location or search above',
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _getLocationDetails(double latitude, double longitude) {
    // This would typically call a reverse geocoding service
    // For now, we'll create a basic location entity
    setState(() {
      _selectedLocationEntity = LocationEntity(
        latitude: latitude,
        longitude: longitude,
        city: 'Selected Location',
        country: 'Unknown',
        timezone: 'UTC',
      );
    });
  }
}

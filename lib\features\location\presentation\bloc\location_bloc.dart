import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/location_entity.dart';
import '../../domain/usecases/get_current_location.dart';
import '../../domain/usecases/search_location.dart';

part 'location_event.dart';
part 'location_state.dart';

class LocationBloc extends Bloc<LocationEvent, LocationState> {
  final GetCurrentLocation getCurrentLocation;
  final SearchLocation searchLocation;

  LocationBloc({
    required this.getCurrentLocation,
    required this.searchLocation,
  }) : super(LocationInitial()) {
    on<GetCurrentLocationEvent>(_onGetCurrentLocation);
    on<SearchLocationEvent>(_onSearchLocation);
    on<SelectLocationEvent>(_onSelectLocation);
  }

  Future<void> _onGetCurrentLocation(
    GetCurrentLocationEvent event,
    Emitter<LocationState> emit,
  ) async {
    emit(LocationLoading());

    final result = await getCurrentLocation(const NoParams());

    result.fold(
      (failure) => emit(LocationError(message: failure.message)),
      (location) => emit(LocationLoaded(location: location)),
    );
  }

  Future<void> _onSearchLocation(
    SearchLocationEvent event,
    Emitter<LocationState> emit,
  ) async {
    emit(LocationSearching());

    final result = await searchLocation(SearchLocationParams(query: event.query));

    result.fold(
      (failure) => emit(LocationError(message: failure.message)),
      (locations) => emit(LocationSearchResults(locations: locations)),
    );
  }

  void _onSelectLocation(
    SelectLocationEvent event,
    Emitter<LocationState> emit,
  ) {
    emit(LocationLoaded(location: event.location));
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import '../../../../core/app_config.dart';
import '../../../../core/theme/app_themes.dart';
import '../../../../core/l10n/app_localizations.dart';
import '../../../../core/dependency_injection.dart';
import '../../../notifications/data/datasources/notification_datasource.dart';
import '../../../help/presentation/pages/help_support_page.dart';
import '../../../prayer_times/domain/usecases/prefetch_prayer_times.dart';
import '../bloc/settings_bloc.dart';
import '../widgets/settings_section.dart';
import '../widgets/language_selector.dart';
import '../widgets/theme_selector.dart';
import '../widgets/design_selector.dart';
import '../widgets/calculation_method_selector.dart';
import '../widgets/hijri_offset_selector.dart';
import 'hijri_validation_page.dart';
import '../widgets/adhan_sound_selector.dart';
import '../widgets/notification_testing_widget.dart';
import '../../../../core/utils/system_locale_utils.dart';


class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  @override
  void initState() {
    super.initState();
    context.read<SettingsBloc>().add(LoadSettings());
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.settings),
        actions: [
          IconButton(
            icon: const Icon(Icons.restore),
            onPressed: () {
              _showResetDialog(context, l10n);
            },
          ),
        ],
      ),
      body: BlocBuilder<SettingsBloc, SettingsState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading settings',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.error!,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      context.read<SettingsBloc>().add(LoadSettings());
                    },
                    child: Text(l10n.retry),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConfig.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // General Settings
                SettingsSection(
                  title: l10n.general,
                  children: [
                    ListTile(
                      leading: const Icon(Icons.language),
                      title: Text(l10n.language),
                      subtitle: Text(_getLanguageName(state.locale)),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () {
                        _showLanguageSelector(context);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.auto_awesome),
                      title: Text(l10n.designStyle),
                      subtitle: Text(_getDesignName(state.appDesign)),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () {
                        _showDesignSelector(context);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.palette),
                      title: Text(l10n.theme),
                      subtitle: Text(_getThemeName(state.themeMode)),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () {
                        _showThemeSelector(context);
                      },
                    ),

                  ],
                ),

                const SizedBox(height: AppConfig.defaultPadding),

                // Prayer Settings
                SettingsSection(
                  title: l10n.prayerSettings,
                  children: [
                    ListTile(
                      leading: const Icon(Icons.calculate),
                      title: Text(l10n.calculationMethod),
                      subtitle: Text(AppConfig.calculationMethods[state.calculationMethod] ?? 'Unknown'),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () {
                        _showCalculationMethodSelector(context);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.calendar_month),
                      title: Text(l10n.hijriDateAdjustment),
                      subtitle: Text(_getHijriOffsetText(state.hijriOffset)),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () {
                        _showHijriOffsetSelector(context);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.verified),
                      title: Text(l10n.hijriValidation),
                      subtitle: Text(l10n.hijriValidationSubtitle),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const HijriValidationPage(),
                          ),
                        );
                      },
                    ),
                    SwitchListTile(
                      secondary: const Icon(Icons.location_on),
                      title: Text(l10n.autoLocation),
                      subtitle: Text(l10n.autoLocationSubtitle),
                      value: state.autoLocation,
                      onChanged: (value) {
                        context.read<SettingsBloc>().add(UpdateAutoLocation(enabled: value));
                      },
                    ),
                  ],
                ),

                const SizedBox(height: AppConfig.defaultPadding),

                // Notification Settings
                SettingsSection(
                  title: l10n.notificationSettings,
                  children: [
                    SwitchListTile(
                      secondary: const Icon(Icons.notifications),
                      title: Text(l10n.notifications),
                      subtitle: Text(l10n.enablePrayerNotifications),
                      value: state.notificationsEnabled,
                      onChanged: (value) {
                        context.read<SettingsBloc>().add(UpdateNotificationsEnabled(enabled: value));
                      },
                    ),
                    SwitchListTile(
                      secondary: const Icon(Icons.volume_up),
                      title: Text(l10n.adhanSound),
                      subtitle: Text(l10n.playAdhanSound),
                      value: state.adhanEnabled,
                      onChanged: state.notificationsEnabled
                          ? (value) {
                              context.read<SettingsBloc>().add(UpdateAdhanEnabled(enabled: value));
                            }
                          : null,
                    ),
                    if (state.adhanEnabled && state.notificationsEnabled)
                      ListTile(
                        leading: const Icon(Icons.music_note),
                        title: Text(l10n.adhanSound),
                        subtitle: Text(_getAdhanSoundName(state.adhanSound)),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () {
                          _showAdhanSoundSelector(context);
                        },
                      ),
                    ListTile(
                      leading: const Icon(Icons.security),
                      title: const Text('Notification Permissions'),
                      subtitle: const Text('Request push notification permissions'),
                      onTap: () {
                        _requestNotificationPermissions(context);
                      },
                    ),
                  ],
                ),

                const SizedBox(height: AppConfig.defaultPadding),

                // Notification Testing (Debug)
                SettingsSection(
                  title: l10n.debug,
                  children: [
                    NotificationTestingWidget(),
                  ],
                ),

                const SizedBox(height: AppConfig.defaultPadding),

                // Data Section
                SettingsSection(
                  title: l10n.data,
                  children: [
                    ListTile(
                      leading: const Icon(Icons.download),
                      title: Text(l10n.prefetchPrayerTimes),
                      subtitle: Text(l10n.prefetchDescription),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _prefetchPrayerTimes(context),
                    ),
                  ],
                ),

                const SizedBox(height: AppConfig.defaultPadding),

                // About Section
                SettingsSection(
                  title: l10n.about,
                  children: [
                    ListTile(
                      leading: const Icon(Icons.info),
                      title: Text(l10n.version),
                      subtitle: Text(AppConfig.appVersion),
                    ),
                    ListTile(
                      leading: const Icon(Icons.help),
                      title: Text(l10n.helpSupport),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const HelpSupportPage(),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  String _getLanguageName(Locale locale) {
    return SystemLocaleUtils.getLanguageDisplayName(locale);
  }

  String _getDesignName(AppDesign appDesign) {
    switch (appDesign) {
      case AppDesign.emerald:
        return 'Emerald Light';
      case AppDesign.latte:
        return 'Latte Cream & Gold';
    }
  }

  String _getThemeName(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }



  String _getAdhanSoundName(String sound) {
    switch (sound) {
      case 'default_adhan.mp3':
        return 'Default Adhan';
      case 'mecca_adhan.mp3':
        return 'Mecca Adhan';
      case 'medina_adhan.mp3':
        return 'Medina Adhan';
      case 'silent':
        return 'Silent';
      default:
        return 'Default';
    }
  }

  void _showLanguageSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => const LanguageSelector(),
    );
  }

  void _showDesignSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => const DesignSelector(),
    );
  }

  void _showThemeSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => const ThemeSelector(),
    );
  }

  void _showCalculationMethodSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => const CalculationMethodSelector(),
    );
  }

  void _showHijriOffsetSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => const HijriOffsetSelector(),
    );
  }

  void _showAdhanSoundSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => const AdhanSoundSelector(),
    );
  }

  String _getHijriOffsetText(int offset) {
    if (offset == 0) {
      return 'Exact Umm al-Qura';
    } else if (offset > 0) {
      return '+$offset day${offset > 1 ? 's' : ''}';
    } else {
      return '$offset day${offset < -1 ? 's' : ''}';
    }
  }



  void _showResetDialog(BuildContext context, AppLocalizations l10n) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.resetSettings ?? 'Reset Settings'),
        content: Text(l10n.resetSettingsConfirmation ?? 'Are you sure you want to reset all settings to default values?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel ?? 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<SettingsBloc>().add(ResetSettings());
            },
            child: Text(l10n.reset ?? 'Reset'),
          ),
        ],
      ),
    );
  }

  Future<void> _requestNotificationPermissions(BuildContext context) async {
    try {
      final notificationDataSource = DependencyInjection.instance.isRegistered<NotificationDataSource>()
          ? DependencyInjection.instance<NotificationDataSource>()
          : null;
      
      if (notificationDataSource != null) {
        final granted = await notificationDataSource.requestPermissions();
        if (granted) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Notification permissions granted successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Notification permissions denied'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to request notification permissions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _prefetchPrayerTimes(BuildContext context) async {
    final l10n = AppLocalizations.of(context)!;
    final settingsState = context.read<SettingsBloc>().state;

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(l10n.prefetchDescription),
          ],
        ),
      ),
    );

    try {
      final prefetchUseCase = GetIt.instance<PrefetchPrayerTimes>();

      final result = await prefetchUseCase(PrefetchPrayerTimesParams(
        latitude: settingsState.customLatitude ?? 21.3891, // Default to Mecca
        longitude: settingsState.customLongitude ?? 39.8579, // Default to Mecca
        calculationMethod: settingsState.calculationMethod,
        daysAhead: 30,
      ));

      Navigator.of(context).pop(); // Close loading dialog

      result.fold(
        (failure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${l10n.prefetchError}: ${failure.message}'),
              backgroundColor: Colors.red,
            ),
          );
        },
        (_) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(l10n.prefetchSuccess),
              backgroundColor: Colors.green,
            ),
          );
        },
      );
    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${l10n.prefetchError}: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

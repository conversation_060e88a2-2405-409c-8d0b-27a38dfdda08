import 'dart:math' as math;

/// Utilities for sensor fusion and compass calculations
class SensorFusionUtils {
  /// Earth's magnetic field strength range (µT)
  static const double minMagneticStrength = 25.0;
  static const double maxMagneticStrength = 65.0;
  static const double typicalMagneticStrength = 45.0;
  
  /// Exponential Moving Average smoothing factor
  static const double defaultSmoothingFactor = 0.15;
  
  /// Calculate heading from magnetometer and accelerometer using tilt compensation
  /// Returns heading in degrees (0-360) relative to magnetic north
  static double calculateTiltCompensatedHeading({
    required double magneticX,
    required double magneticY,
    required double magneticZ,
    required double accelX,
    required double accelY,
    required double accelZ,
  }) {
    // Normalize accelerometer vector (gravity)
    final accelMagnitude = math.sqrt(accelX * accelX + accelY * accelY + accelZ * accelZ);
    if (accelMagnitude == 0) return 0.0;
    
    final ax = accelX / accelMagnitude;
    final ay = accelY / accelMagnitude;
    final az = accelZ / accelMagnitude;
    
    // Normalize magnetometer vector
    final magneticMagnitude = math.sqrt(magneticX * magneticX + magneticY * magneticY + magneticZ * magneticZ);
    if (magneticMagnitude == 0) return 0.0;
    
    final mx = magneticX / magneticMagnitude;
    final my = magneticY / magneticMagnitude;
    final mz = magneticZ / magneticMagnitude;
    
    // Calculate East vector: East = normalize(m × a)
    final eastX = my * az - mz * ay;
    final eastY = mz * ax - mx * az;
    final eastZ = mx * ay - my * ax;
    
    final eastMagnitude = math.sqrt(eastX * eastX + eastY * eastY + eastZ * eastZ);
    if (eastMagnitude == 0) return 0.0;
    
    final ex = eastX / eastMagnitude;
    final ey = eastY / eastMagnitude;
    final ez = eastZ / eastMagnitude;
    
    // Calculate North vector: North = normalize(a × East)
    final northX = ay * ez - az * ey;
    final northY = az * ex - ax * ez;
    final northZ = ax * ey - ay * ex;
    
    // Calculate heading: atan2(East.y, North.y)
    // Note: Adjust for device coordinate system (Android: Y points to magnetic north when flat)
    final heading = math.atan2(ey, northY) * 180.0 / math.pi;
    
    return normalizeAngle(heading);
  }
  
  /// Apply Exponential Moving Average smoothing to compass readings
  static double applySmoothingEMA({
    required double currentReading,
    required double previousReading,
    double smoothingFactor = defaultSmoothingFactor,
  }) {
    // Handle angle wrapping (359° -> 1°)
    double delta = currentReading - previousReading;
    if (delta > 180) {
      delta -= 360;
    } else if (delta < -180) {
      delta += 360;
    }
    
    final smoothedReading = previousReading + (smoothingFactor * delta);
    return normalizeAngle(smoothedReading);
  }
  
  /// Calculate magnetic declination for location (simplified approximation)
  /// For precise values, use platform-specific APIs (GeomagneticField on Android)
  static double calculateMagneticDeclination({
    required double latitude,
    required double longitude,
  }) {
    // Simplified magnetic declination calculation
    // For production, use GeomagneticField (Android) or CoreLocation (iOS)
    
    // Rough approximation based on location
    final lat = latitude * math.pi / 180;
    final lon = longitude * math.pi / 180;
    
    // Very simplified model - replace with proper geomagnetic model
    final declination = math.sin(lat) * math.cos(lon) * 15.0 + 
                       math.cos(lat) * math.sin(lon) * 5.0;
    
    return declination.clamp(-30.0, 30.0);
  }
  
  /// Convert magnetic heading to true heading
  static double magneticToTrueHeading({
    required double magneticHeading,
    required double magneticDeclination,
  }) {
    return normalizeAngle(magneticHeading + magneticDeclination);
  }
  
  /// Convert true heading to magnetic heading
  static double trueToMagneticHeading({
    required double trueHeading,
    required double magneticDeclination,
  }) {
    return normalizeAngle(trueHeading - magneticDeclination);
  }
  
  /// Normalize angle to 0-360 degrees
  static double normalizeAngle(double angle) {
    double normalized = angle % 360;
    if (normalized < 0) normalized += 360;
    return normalized;
  }
  
  /// Calculate angle difference handling wrap-around
  static double angleDifference(double angle1, double angle2) {
    double diff = angle1 - angle2;
    if (diff > 180) {
      diff -= 360;
    } else if (diff < -180) {
      diff += 360;
    }
    return diff.abs();
  }
  
  /// Estimate compass accuracy based on magnetic field strength and stability
  static double estimateCompassAccuracy({
    required double magneticStrength,
    required List<double> recentReadings,
  }) {
    // Base accuracy from magnetic field strength
    double strengthAccuracy = 45.0; // Default poor accuracy
    
    if (magneticStrength >= minMagneticStrength && magneticStrength <= maxMagneticStrength) {
      // Good magnetic field strength
      final strengthRatio = (magneticStrength - minMagneticStrength) / 
                           (maxMagneticStrength - minMagneticStrength);
      strengthAccuracy = 45.0 - (strengthRatio * 35.0); // 10-45 degrees
    }
    
    // Stability accuracy from reading variance
    double stabilityAccuracy = 45.0;
    if (recentReadings.length >= 5) {
      final variance = _calculateVariance(recentReadings);
      stabilityAccuracy = math.min(45.0, variance / 2.0); // Lower variance = better accuracy
    }
    
    // Return worst of both factors
    return math.max(strengthAccuracy, stabilityAccuracy);
  }
  
  /// Calculate calibration quality score (0-100)
  static int calculateCalibrationQuality({
    required List<CalibrationDataPoint> calibrationData,
    required double currentAccuracy,
  }) {
    if (calibrationData.isEmpty) return 0;
    
    int score = 0;
    
    // 1. Coverage score (0-40 points) - how much of 360° was covered
    final headings = calibrationData.map((p) => p.heading).toList();
    final coverageScore = _calculateCoverageScore(headings);
    score += (coverageScore * 40).round();
    
    // 2. Movement score (0-30 points) - device was moved in different orientations
    final movementScore = _calculateMovementScore(calibrationData);
    score += (movementScore * 30).round();
    
    // 3. Stability score (0-20 points) - readings are stable
    final stabilityScore = math.max(0.0, 1.0 - (currentAccuracy / 45.0));
    score += (stabilityScore * 20).round();
    
    // 4. Duration score (0-10 points) - sufficient calibration time
    if (calibrationData.length >= 50) score += 10; // ~5 seconds of data
    else if (calibrationData.length >= 30) score += 5;
    
    return math.min(100, score);
  }
  
  /// Calculate how much of 360° was covered during calibration
  static double _calculateCoverageScore(List<double> headings) {
    if (headings.length < 10) return 0.0;
    
    // Divide 360° into 36 sectors (10° each)
    final sectors = List<bool>.filled(36, false);
    
    for (final heading in headings) {
      final sector = (heading / 10).floor() % 36;
      sectors[sector] = true;
    }
    
    final coveredSectors = sectors.where((covered) => covered).length;
    return coveredSectors / 36.0; // 0.0 to 1.0
  }
  
  /// Calculate movement score based on tilt variations
  static double _calculateMovementScore(List<CalibrationDataPoint> data) {
    if (data.length < 10) return 0.0;
    
    final pitches = data.map((p) => p.pitch.abs()).toList();
    final rolls = data.map((p) => p.roll.abs()).toList();
    
    final maxPitch = pitches.reduce(math.max);
    final maxRoll = rolls.reduce(math.max);
    
    // Good movement should have at least 15° tilt in both directions
    final pitchScore = math.min(1.0, maxPitch / 15.0);
    final rollScore = math.min(1.0, maxRoll / 15.0);
    
    return (pitchScore + rollScore) / 2.0;
  }
  
  /// Calculate variance of a list of values
  static double _calculateVariance(List<double> values) {
    if (values.isEmpty) return 0.0;
    
    final mean = values.reduce((a, b) => a + b) / values.length;
    final squaredDifferences = values.map((x) => math.pow(x - mean, 2));
    return squaredDifferences.reduce((a, b) => a + b) / values.length;
  }
}

/// Data point for calibration analysis
class CalibrationDataPoint {
  final double heading;
  final double magneticStrength;
  final double pitch;
  final double roll;
  final DateTime timestamp;
  
  const CalibrationDataPoint({
    required this.heading,
    required this.magneticStrength,
    required this.pitch,
    required this.roll,
    required this.timestamp,
  });
}

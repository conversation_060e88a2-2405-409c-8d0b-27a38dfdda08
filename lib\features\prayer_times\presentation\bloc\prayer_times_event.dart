part of 'prayer_times_bloc.dart';

abstract class PrayerTimesEvent extends Equatable {
  const PrayerTimesEvent();

  @override
  List<Object?> get props => [];
}

class LoadPrayerTimes extends PrayerTimesEvent {
  final DateTime date;
  final double latitude;
  final double longitude;
  final int calculationMethod;

  const LoadPrayerTimes({
    required this.date,
    required this.latitude,
    required this.longitude,
    this.calculationMethod = AppConfig.defaultCalculationMethod,
  });

  @override
  List<Object> get props => [date, latitude, longitude, calculationMethod];
}

class RefreshPrayerTimes extends PrayerTimesEvent {
  final DateTime? date;

  const RefreshPrayerTimes({this.date});

  @override
  List<Object?> get props => [date];
}

class CacheMonthlyPrayerTimes extends PrayerTimesEvent {
  final DateTime month;

  const CacheMonthlyPrayerTimes({required this.month});

  @override
  List<Object> get props => [month];
}

class UpdateLocation extends PrayerTimesEvent {
  final double latitude;
  final double longitude;

  const UpdateLocation({
    required this.latitude,
    required this.longitude,
  });

  @override
  List<Object> get props => [latitude, longitude];
}

class UpdateCalculationMethod extends PrayerTimesEvent {
  final int calculationMethod;

  const UpdateCalculationMethod({required this.calculationMethod});

  @override
  List<Object> get props => [calculationMethod];
}

import 'package:equatable/equatable.dart';

/// Represents the Qibla direction and related information
class QiblaDirection extends Equatable {
  /// True bearing from user location to Kaaba (degrees from geographic north)
  final double bearingTrue;
  
  /// Current device heading from compass sensor (degrees)
  final double deviceHeading;
  
  /// Direction delta for arrow rotation (bearingTrue - deviceHeading, normalized 0-360)
  final double directionDelta;
  
  /// Distance to Kaaba in kilometers
  final double distanceKm;
  
  /// Current compass status
  final CompassStatus compassStatus;
  
  /// Current mode (compass or map fallback)
  final QiblaMode mode;
  
  /// User's current latitude
  final double userLatitude;
  
  /// User's current longitude
  final double userLongitude;
  
  /// Whether location is manually set
  final bool isManualLocation;

  const QiblaDirection({
    required this.bearingTrue,
    required this.deviceHeading,
    required this.directionDelta,
    required this.distanceKm,
    required this.compassStatus,
    required this.mode,
    required this.userLatitude,
    required this.userLongitude,
    this.isManualLocation = false,
  });

  /// Kaaba coordinates (Mecca, Saudi Arabia)
  static const double kaabaLatitude = 21.422487;
  static const double kaabaLongitude = 39.826206;

  @override
  List<Object?> get props => [
        bearingTrue,
        deviceHeading,
        directionDelta,
        distanceKm,
        compassStatus,
        mode,
        userLatitude,
        userLongitude,
        isManualLocation,
      ];

  QiblaDirection copyWith({
    double? bearingTrue,
    double? deviceHeading,
    double? directionDelta,
    double? distanceKm,
    CompassStatus? compassStatus,
    QiblaMode? mode,
    double? userLatitude,
    double? userLongitude,
    bool? isManualLocation,
  }) {
    return QiblaDirection(
      bearingTrue: bearingTrue ?? this.bearingTrue,
      deviceHeading: deviceHeading ?? this.deviceHeading,
      directionDelta: directionDelta ?? this.directionDelta,
      distanceKm: distanceKm ?? this.distanceKm,
      compassStatus: compassStatus ?? this.compassStatus,
      mode: mode ?? this.mode,
      userLatitude: userLatitude ?? this.userLatitude,
      userLongitude: userLongitude ?? this.userLongitude,
      isManualLocation: isManualLocation ?? this.isManualLocation,
    );
  }
}

/// Compass sensor status
enum CompassStatus {
  /// Compass is working correctly
  ok,
  
  /// Compass needs calibration
  calibrating,
  
  /// Compass is disturbed (interference)
  disturbed,
  
  /// Compass sensor not available
  notAvailable,
  
  /// Permission denied
  permissionDenied,
}

/// Qibla display mode
enum QiblaMode {
  /// Show compass with arrow
  compass,
  
  /// Show map fallback with direction
  mapFallback,
}

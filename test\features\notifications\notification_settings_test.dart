import 'package:flutter_test/flutter_test.dart';
import 'package:hive_flutter/hive_flutter.dart';

void main() {
  group('Notification Settings Tests', () {
    late Box<String> box;

    setUp(() async {
      Hive.init('test');
      box = await Hive.openBox<String>('test_notification_settings');
    });

    tearDown(() async {
      await box.clear();
      await box.close();
    });

    test('should set and get notification enabled status', () async {
      final date = DateTime(2024, 1, 1);
      const prayerId = 'fajr';
      final key = 'notify:20240101:fajr';

      // Initially should be null (false)
      expect(box.get(key), isNull);

      // Set to true
      await box.put(key, 'true');

      // Should now be true
      expect(box.get(key), 'true');

      // Set to false
      await box.put(key, 'false');

      // Should now be false
      expect(box.get(key), 'false');
    });

    test('should handle different prayer IDs correctly', () async {
      const prayerIds = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];

      // Set different values for each prayer
      for (int i = 0; i < prayerIds.length; i++) {
        final key = 'notify:20240101:${prayerIds[i]}';
        await box.put(key, (i % 2 == 0).toString()); // Even indices true, odd false
      }

      // Verify each prayer has correct value
      for (int i = 0; i < prayerIds.length; i++) {
        final key = 'notify:20240101:${prayerIds[i]}';
        expect(box.get(key), (i % 2 == 0).toString());
      }
    });

    test('should handle different dates correctly', () async {
      const prayerId = 'fajr';
      final key1 = 'notify:20240101:$prayerId';
      final key2 = 'notify:20240102:$prayerId';

      // Set different values for different dates
      await box.put(key1, 'true');
      await box.put(key2, 'false');

      // Verify each date has correct value
      expect(box.get(key1), 'true');
      expect(box.get(key2), 'false');
    });
  });
}

import 'package:flutter/material.dart';
import '../../../../core/theme/app_themes.dart';

part of 'settings_bloc.dart';

class SettingsState extends Equatable {
  final AppSettings settings;
  final bool isLoading;
  final String? error;

  const SettingsState({
    this.settings = const AppSettings(),
    this.isLoading = false,
    this.error,
  });

  // Convenience getters for easy access
  ThemeMode get themeMode => settings.themeMode;
  AppDesign get appDesign => settings.appDesign;
  String get backgroundType => settings.backgroundType;
  Locale get locale => settings.locale;
  String get language => settings.locale.toString();
  int get calculationMethod => settings.calculationMethod;
  bool get notificationsEnabled => settings.notificationsEnabled;
  bool get adhanEnabled => settings.adhanEnabled;
  String get adhanSound => settings.adhanSound;
  int get hijriOffset => settings.hijriOffset;

  bool get autoLocation => settings.autoLocation;
  double? get customLatitude => settings.customLatitude;
  double? get customLongitude => settings.customLongitude;
  String? get customLocationName => settings.customLocationName;

  SettingsState copyWith({
    AppSettings? settings,
    bool? isLoading,
    String? error,
  }) {
    return SettingsState(
      settings: settings ?? this.settings,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  List<Object?> get props => [settings, isLoading, error];
}

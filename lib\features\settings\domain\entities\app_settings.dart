import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/theme/app_themes.dart';
import '../../../../core/utils/system_locale_utils.dart';

class AppSettings extends Equatable {
  final ThemeMode themeMode;
  final AppDesign appDesign; // New field for design selection (emerald or latte)
  final String backgroundType; // New field for background control ('default' or 'photos')
  final Locale locale;
  final int calculationMethod;
  final bool notificationsEnabled;
  final bool adhanEnabled;
  final String adhanSound;
  final int hijriOffset; // -3 to +3 days for local sighting adjustments

  final bool autoLocation;
  final double? customLatitude;
  final double? customLongitude;
  final String? customLocationName;

  const AppSettings({
    this.themeMode = ThemeMode.system,
    this.appDesign = AppDesign.emerald, // Default to emerald as "classic light"
    this.backgroundType = 'default', // Default to no glassmorphism
    Locale? locale, // Made nullable to allow dynamic assignment
    this.calculationMethod = 4, // <PERSON><PERSON> al<PERSON>Qura default
    this.notificationsEnabled = true,
    this.adhanEnabled = true,
    this.adhanSound = 'default_adhan.mp3',
    this.hijriOffset = -1, // Corrected for local sighting (29 Safar)

    this.autoLocation = true,
    this.customLatitude,
    this.customLongitude,
    this.customLocationName,
  }) : locale = locale ?? const Locale('en', 'US'); // Will be replaced with system locale in factory

  /// Factory constructor with system defaults
  factory AppSettings.withSystemDefaults() {
    return AppSettings(
      themeMode: ThemeMode.system, // Always use system theme mode
      appDesign: AppDesign.emerald, // Default to emerald as "classic light"
      locale: SystemLocaleUtils.getSystemLocale(), // Use system locale
    );
  }

  AppSettings copyWith({
    ThemeMode? themeMode,
    AppDesign? appDesign,
    String? backgroundType,
    Locale? locale,
    int? calculationMethod,
    bool? notificationsEnabled,
    bool? adhanEnabled,
    String? adhanSound,
    int? hijriOffset,

    bool? autoLocation,
    double? customLatitude,
    double? customLongitude,
    String? customLocationName,
  }) {
    return AppSettings(
      themeMode: themeMode ?? this.themeMode,
      appDesign: appDesign ?? this.appDesign,
      backgroundType: backgroundType ?? this.backgroundType,
      locale: locale ?? this.locale,
      calculationMethod: calculationMethod ?? this.calculationMethod,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      adhanEnabled: adhanEnabled ?? this.adhanEnabled,
      adhanSound: adhanSound ?? this.adhanSound,
      hijriOffset: hijriOffset ?? this.hijriOffset,

      autoLocation: autoLocation ?? this.autoLocation,
      customLatitude: customLatitude ?? this.customLatitude,
      customLongitude: customLongitude ?? this.customLongitude,
      customLocationName: customLocationName ?? this.customLocationName,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'themeMode': themeMode.index,
      'appDesign': appDesign.index,
      'backgroundType': backgroundType,
      'locale': '${locale.languageCode}_${locale.countryCode}',
      'calculationMethod': calculationMethod,
      'notificationsEnabled': notificationsEnabled,
      'adhanEnabled': adhanEnabled,
      'adhanSound': adhanSound,
      'hijriOffset': hijriOffset,

      'autoLocation': autoLocation,
      'customLatitude': customLatitude,
      'customLongitude': customLongitude,
      'customLocationName': customLocationName,
    };
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    final localeString = json['locale'] as String?;
    Locale locale;
    
    if (localeString != null) {
      final localeParts = localeString.split('_');
      locale = Locale(
        localeParts.first,
        localeParts.length > 1 ? localeParts.last : null,
      );
    } else {
      // Use system locale if no saved locale
      locale = SystemLocaleUtils.getSystemLocale();
    }
    
    return AppSettings(
      themeMode: ThemeMode.values[json['themeMode'] as int? ?? 0],
      appDesign: AppDesign.values[json['appDesign'] as int? ?? 0],
      backgroundType: json['backgroundType'] as String? ?? 'default',
      locale: locale,
      calculationMethod: json['calculationMethod'] as int? ?? 4,
      notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
      adhanEnabled: json['adhanEnabled'] as bool? ?? true,
      adhanSound: json['adhanSound'] as String? ?? 'default_adhan.mp3',
      hijriOffset: json['hijriOffset'] as int? ?? -1, // Corrected for local sighting (29 Safar)

      autoLocation: json['autoLocation'] as bool? ?? true,
      customLatitude: json['customLatitude'] as double?,
      customLongitude: json['customLongitude'] as double?,
      customLocationName: json['customLocationName'] as String?,
    );
  }

  @override
  List<Object?> get props => [
        themeMode,
        appDesign,
        backgroundType,
        locale,
        calculationMethod,
        notificationsEnabled,
        adhanEnabled,
        adhanSound,
        hijriOffset,

        autoLocation,
        customLatitude,
        customLongitude,
        customLocationName,
      ];
}

import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../../../core/theme/design_tokens.dart';
import '../../domain/entities/qibla_direction.dart';

/// Widget that displays the Qibla compass with animated arrow
class Qi<PERSON>CompassWidget extends StatefulWidget {
  final QiblaDirection qiblaDirection;
  final double size;

  const QiblaCompassWidget({
    super.key,
    required this.qiblaDirection,
    this.size = 300.0,
  });

  @override
  State<QiblaCompassWidget> createState() => _QiblaCompassWidgetState();
}

class _QiblaCompassWidgetState extends State<QiblaCompassWidget>
    with TickerProviderStateMixin {
  late AnimationController _compassController;
  late AnimationController _arrowController;
  late Animation<double> _compassAnimation;
  late Animation<double> _arrowAnimation;

  double _previousCompassAngle = 0.0;
  double _previousArrowAngle = 0.0;

  @override
  void initState() {
    super.initState();
    
    _compassController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _arrowController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _compassAnimation = Tween<double>(
      begin: 0.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _compassController,
      curve: Curves.easeInOut,
    ));

    _arrowAnimation = Tween<double>(
      begin: 0.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _arrowController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(QiblaCompassWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.qiblaDirection != widget.qiblaDirection) {
      _updateAnimations();
    }
  }

  void _updateAnimations() {
    // Update compass rotation (device heading)
    final compassAngle = -widget.qiblaDirection.deviceHeading * math.pi / 180;
    final compassDelta = _calculateAngleDelta(_previousCompassAngle, compassAngle);
    
    _compassAnimation = Tween<double>(
      begin: _previousCompassAngle,
      end: _previousCompassAngle + compassDelta,
    ).animate(CurvedAnimation(
      parent: _compassController,
      curve: Curves.easeInOut,
    ));
    
    _compassController.forward(from: 0.0);
    _previousCompassAngle = _previousCompassAngle + compassDelta;

    // Update arrow rotation (direction to Qibla)
    final arrowAngle = widget.qiblaDirection.directionDelta * math.pi / 180;
    final arrowDelta = _calculateAngleDelta(_previousArrowAngle, arrowAngle);
    
    _arrowAnimation = Tween<double>(
      begin: _previousArrowAngle,
      end: _previousArrowAngle + arrowDelta,
    ).animate(CurvedAnimation(
      parent: _arrowController,
      curve: Curves.easeInOut,
    ));
    
    _arrowController.forward(from: 0.0);
    _previousArrowAngle = _previousArrowAngle + arrowDelta;
  }

  double _calculateAngleDelta(double from, double to) {
    double delta = to - from;
    
    // Handle angle wrapping
    if (delta > math.pi) {
      delta -= 2 * math.pi;
    } else if (delta < -math.pi) {
      delta += 2 * math.pi;
    }
    
    return delta;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Compass background
          AnimatedBuilder(
            animation: _compassAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _compassAnimation.value,
                child: _buildCompassBackground(),
              );
            },
          ),
          
          // Qibla arrow
          AnimatedBuilder(
            animation: _arrowAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _arrowAnimation.value,
                child: _buildQiblaArrow(),
              );
            },
          ),
          
          // Center dot
          _buildCenterDot(),
          
          // Status indicator
          _buildStatusIndicator(),
        ],
      ),
    );
  }

  Widget _buildCompassBackground() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            DesignTokens.getCardColor(context),
            DesignTokens.getSurfaceColor(context),
          ],
        ),
        border: Border.all(
          color: DesignTokens.lightPrimary.withOpacity(0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: CustomPaint(
        painter: CompassPainter(
          primaryColor: DesignTokens.lightPrimary,
          secondaryColor: DesignTokens.getTextColor(context, secondary: true),
          backgroundColor: DesignTokens.getCardColor(context),
        ),
      ),
    );
  }

  Widget _buildQiblaArrow() {
    final isAccurate = widget.qiblaDirection.compassStatus == CompassStatus.ok;
    
    return Container(
      width: widget.size * 0.8,
      height: widget.size * 0.8,
      child: CustomPaint(
        painter: QiblaArrowPainter(
          color: isAccurate ? DesignTokens.lightPrimary : Colors.orange,
          isAccurate: isAccurate,
        ),
      ),
    );
  }

  Widget _buildCenterDot() {
    return Container(
      width: 16,
      height: 16,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: DesignTokens.lightPrimary,
        border: Border.all(
          color: Colors.white,
          width: 2,
        ),
      ),
    );
  }

  Widget _buildStatusIndicator() {
    final status = widget.qiblaDirection.compassStatus;
    Color statusColor;
    IconData statusIcon;
    
    switch (status) {
      case CompassStatus.ok:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case CompassStatus.calibrating:
        statusColor = Colors.orange;
        statusIcon = Icons.sync;
        break;
      case CompassStatus.disturbed:
        statusColor = Colors.red;
        statusIcon = Icons.warning;
        break;
      case CompassStatus.notAvailable:
        statusColor = Colors.grey;
        statusIcon = Icons.error;
        break;
      case CompassStatus.permissionDenied:
        statusColor = Colors.red;
        statusIcon = Icons.location_disabled;
        break;
    }
    
    return Positioned(
      top: 10,
      right: 10,
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: statusColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: statusColor.withOpacity(0.3)),
        ),
        child: Icon(
          statusIcon,
          color: statusColor,
          size: 16,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _compassController.dispose();
    _arrowController.dispose();
    super.dispose();
  }
}

/// Custom painter for compass background with cardinal directions
class CompassPainter extends CustomPainter {
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;

  CompassPainter({
    required this.primaryColor,
    required this.secondaryColor,
    required this.backgroundColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    
    final paint = Paint()
      ..color = secondaryColor
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Draw cardinal direction marks
    for (int i = 0; i < 360; i += 30) {
      final angle = i * math.pi / 180;
      final isCardinal = i % 90 == 0;
      final markLength = isCardinal ? 20.0 : 10.0;
      
      final startRadius = radius - markLength;
      final endRadius = radius - 5;
      
      final start = Offset(
        center.dx + startRadius * math.sin(angle),
        center.dy - startRadius * math.cos(angle),
      );
      
      final end = Offset(
        center.dx + endRadius * math.sin(angle),
        center.dy - endRadius * math.cos(angle),
      );
      
      paint.strokeWidth = isCardinal ? 2 : 1;
      paint.color = isCardinal ? primaryColor : secondaryColor;
      
      canvas.drawLine(start, end, paint);
    }

    // Draw cardinal letters (N, E, S, W)
    final textPainter = TextPainter(
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    final cardinals = ['N', 'E', 'S', 'W'];
    for (int i = 0; i < cardinals.length; i++) {
      final angle = i * math.pi / 2;
      final letterRadius = radius - 35;
      
      final position = Offset(
        center.dx + letterRadius * math.sin(angle),
        center.dy - letterRadius * math.cos(angle),
      );
      
      textPainter.text = TextSpan(
        text: cardinals[i],
        style: TextStyle(
          color: primaryColor,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      );
      
      textPainter.layout();
      textPainter.paint(
        canvas,
        position - Offset(textPainter.width / 2, textPainter.height / 2),
      );
    }
  }

  @override
  bool shouldRepaint(CompassPainter oldDelegate) {
    return oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor ||
           oldDelegate.backgroundColor != backgroundColor;
  }
}

/// Custom painter for Qibla arrow
class QiblaArrowPainter extends CustomPainter {
  final Color color;
  final bool isAccurate;

  QiblaArrowPainter({
    required this.color,
    required this.isAccurate,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Draw arrow pointing north (will be rotated by Transform.rotate)
    final arrowPath = Path();
    final arrowLength = size.height * 0.35;
    final arrowWidth = 12.0;
    
    // Arrow tip
    arrowPath.moveTo(center.dx, center.dy - arrowLength);
    
    // Arrow sides
    arrowPath.lineTo(center.dx - arrowWidth, center.dy - arrowLength + 30);
    arrowPath.lineTo(center.dx - arrowWidth / 2, center.dy - arrowLength + 25);
    arrowPath.lineTo(center.dx - arrowWidth / 2, center.dy + arrowLength / 2);
    arrowPath.lineTo(center.dx + arrowWidth / 2, center.dy + arrowLength / 2);
    arrowPath.lineTo(center.dx + arrowWidth / 2, center.dy - arrowLength + 25);
    arrowPath.lineTo(center.dx + arrowWidth, center.dy - arrowLength + 30);
    
    arrowPath.close();
    
    canvas.drawPath(arrowPath, paint);
    
    // Draw accuracy indicator (pulsing effect if not accurate)
    if (!isAccurate) {
      paint.color = color.withOpacity(0.3);
      paint.style = PaintingStyle.stroke;
      paint.strokeWidth = 3;
      
      canvas.drawCircle(center, arrowLength + 10, paint);
    }
  }

  @override
  bool shouldRepaint(QiblaArrowPainter oldDelegate) {
    return oldDelegate.color != color || oldDelegate.isAccurate != isAccurate;
  }
}

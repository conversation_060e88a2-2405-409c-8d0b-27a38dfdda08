import 'package:equatable/equatable.dart';

class LocationEntity extends Equatable {
  final double latitude;
  final double longitude;
  final String city;
  final String country;
  final String? state;
  final String? address;
  final String timezone;

  const LocationEntity({
    required this.latitude,
    required this.longitude,
    required this.city,
    required this.country,
    this.state,
    this.address,
    required this.timezone,
  });

  String get displayName {
    if (state != null && state!.isNotEmpty) {
      return '$city, $state, $country';
    }
    return '$city, $country';
  }

  String get coordinates => '${latitude.toStringAsFixed(4)}, ${longitude.toStringAsFixed(4)}';

  @override
  List<Object?> get props => [
        latitude,
        longitude,
        city,
        country,
        state,
        address,
        timezone,
      ];
}

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive/hive.dart';
import '../../../../core/error/exceptions.dart';
import '../../domain/entities/app_settings.dart';

abstract class SettingsLocalDataSource {
  Future<AppSettings> getSettings();
  Future<void> saveSettings(AppSettings settings);
  Future<void> resetSettings();
}

class SettingsLocalDataSourceImpl implements SettingsLocalDataSource {
  final SharedPreferences sharedPreferences;
  final Box box;

  static const String _settingsKey = 'app_settings';

  SettingsLocalDataSourceImpl({
    required this.sharedPreferences,
    required this.box,
  });

  @override
  Future<AppSettings> getSettings() async {
    try {
      // Try to get from Hive first (newer storage)
      final hiveData = box.get(_settingsKey);
      if (hiveData != null) {
        if (hiveData is Map<String, dynamic>) {
          final settings = AppSettings.fromJson(hiveData);
          // Migration: Update hijriOffset to correct value for local sighting
          if (settings.hijriOffset == 0 || settings.hijriOffset == -3) {
            final updatedSettings = settings.copyWith(hijriOffset: -1);
            // Save the updated settings
            await saveSettings(updatedSettings);
            return updatedSettings;
          }
          return settings;
        }
      }

      // Fallback to SharedPreferences
      final jsonString = sharedPreferences.getString(_settingsKey);
      if (jsonString != null) {
        final jsonData = json.decode(jsonString) as Map<String, dynamic>;
        final settings = AppSettings.fromJson(jsonData);
        // Migration: Update hijriOffset to correct value for local sighting
        if (settings.hijriOffset == 0 || settings.hijriOffset == -3) {
          final updatedSettings = settings.copyWith(hijriOffset: -1);
          // Save the updated settings
          await saveSettings(updatedSettings);
          return updatedSettings;
        }
        return settings;
      }

      // Return system defaults if nothing found
      return AppSettings.withSystemDefaults();
    } catch (e) {
      throw CacheException(message: 'Failed to load settings: ${e.toString()}');
    }
  }

  @override
  Future<void> saveSettings(AppSettings settings) async {
    try {
      final jsonData = settings.toJson();

      // Save to both Hive and SharedPreferences for redundancy
      await box.put(_settingsKey, jsonData);
      await sharedPreferences.setString(_settingsKey, json.encode(jsonData));
    } catch (e) {
      throw CacheException(message: 'Failed to save settings: ${e.toString()}');
    }
  }

  @override
  Future<void> resetSettings() async {
    try {
      await box.delete(_settingsKey);
      await sharedPreferences.remove(_settingsKey);
    } catch (e) {
      throw CacheException(message: 'Failed to reset settings: ${e.toString()}');
    }
  }
}

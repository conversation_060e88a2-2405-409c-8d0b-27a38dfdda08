import 'package:hive/hive.dart';
import '../../../../core/app_config.dart';
import '../../../../core/error/exceptions.dart';
import '../models/prayer_times_model.dart';

abstract class PrayerTimesLocalDataSource {
  /// Cache prayer times
  Future<void> cachePrayerTimes({
    required List<PrayerTimesModel> prayerTimes,
    required String cacheKey,
  });

  /// Get cached prayer times for a specific date
  Future<PrayerTimesModel?> getCachedPrayerTimes({
    required DateTime date,
    required String cacheKey,
  });

  /// Get cached monthly prayer times
  Future<List<PrayerTimesModel>?> getCachedMonthlyPrayerTimes({
    required DateTime month,
    required String cacheKey,
  });

  /// Clear expired cache
  Future<void> clearExpiredCache();

  /// Check if cache is valid for a specific date
  Future<bool> isCacheValid({
    required DateTime date,
    required String cacheKey,
  });
}

class PrayerTimesLocalDataSourceImpl implements PrayerTimesLocalDataSource {
  final Box box;

  PrayerTimesLocalDataSourceImpl({required this.box});

  @override
  Future<void> cachePrayerTimes({
    required List<PrayerTimesModel> prayerTimes,
    required String cacheKey,
  }) async {
    try {
      for (final prayerTime in prayerTimes) {
        final key = _generateDailyKey(cacheKey, prayerTime.date);
        await box.put(key, prayerTime);
      }

      // Store cache metadata
      final metaKey = '${cacheKey}_meta';
      await box.put(metaKey, {
        'cached_at': DateTime.now().toIso8601String(),
        'cache_key': cacheKey,
        'count': prayerTimes.length,
      });
    } catch (e) {
      throw CacheException(message: 'Failed to cache prayer times: ${e.toString()}');
    }
  }

  @override
  Future<PrayerTimesModel?> getCachedPrayerTimes({
    required DateTime date,
    required String cacheKey,
  }) async {
    try {
      final key = _generateDailyKey(cacheKey, date);
      final cachedData = box.get(key);
      
      if (cachedData is PrayerTimesModel) {
        // Check if cache is still valid
        final isValid = await isCacheValid(date: date, cacheKey: cacheKey);
        if (isValid) {
          return cachedData;
        } else {
          // Remove expired cache
          await box.delete(key);
          return null;
        }
      }
      
      return null;
    } catch (e) {
      throw CacheException(message: 'Failed to get cached prayer times: ${e.toString()}');
    }
  }

  @override
  Future<List<PrayerTimesModel>?> getCachedMonthlyPrayerTimes({
    required DateTime month,
    required String cacheKey,
  }) async {
    try {
      final List<PrayerTimesModel> monthlyData = [];
      final daysInMonth = DateTime(month.year, month.month + 1, 0).day;

      for (int day = 1; day <= daysInMonth; day++) {
        final date = DateTime(month.year, month.month, day);
        final cachedDay = await getCachedPrayerTimes(
          date: date,
          cacheKey: cacheKey,
        );
        
        if (cachedDay != null) {
          monthlyData.add(cachedDay);
        } else {
          // If any day is missing, return null to fetch all data
          return null;
        }
      }

      return monthlyData.isNotEmpty ? monthlyData : null;
    } catch (e) {
      throw CacheException(message: 'Failed to get cached monthly prayer times: ${e.toString()}');
    }
  }

  @override
  Future<void> clearExpiredCache() async {
    try {
      final keys = box.keys.toList();
      final now = DateTime.now();
      
      for (final key in keys) {
        if (key.toString().contains('_meta')) {
          final metaData = box.get(key);
          if (metaData is Map) {
            final cachedAtString = metaData['cached_at'] as String?;
            if (cachedAtString != null) {
              final cachedAt = DateTime.parse(cachedAtString);
              final daysDifference = now.difference(cachedAt).inDays;
              
              if (daysDifference > AppConfig.cacheValidityDays) {
                // Remove metadata and related cache entries
                await box.delete(key);
                final cacheKey = metaData['cache_key'] as String?;
                if (cacheKey != null) {
                  await _clearCacheByKey(cacheKey);
                }
              }
            }
          }
        }
      }
    } catch (e) {
      throw CacheException(message: 'Failed to clear expired cache: ${e.toString()}');
    }
  }

  @override
  Future<bool> isCacheValid({
    required DateTime date,
    required String cacheKey,
  }) async {
    try {
      final metaKey = '${cacheKey}_meta';
      final metaData = box.get(metaKey);
      
      if (metaData is Map) {
        final cachedAtString = metaData['cached_at'] as String?;
        if (cachedAtString != null) {
          final cachedAt = DateTime.parse(cachedAtString);
          final daysDifference = DateTime.now().difference(cachedAt).inDays;
          return daysDifference <= AppConfig.cacheValidityDays;
        }
      }
      
      return false;
    } catch (e) {
      return false;
    }
  }

  String _generateDailyKey(String cacheKey, DateTime date) {
    return '${cacheKey}_${date.year}_${date.month}_${date.day}';
  }

  Future<void> _clearCacheByKey(String cacheKey) async {
    final keys = box.keys.where((key) => key.toString().startsWith(cacheKey)).toList();
    for (final key in keys) {
      await box.delete(key);
    }
  }
}

part of 'qibla_bloc.dart';

class QiblaState extends Equatable {
  final QiblaDirection? qiblaDirection;
  final bool isLoading;
  final String? error;
  final bool isCompassActive;
  final bool hasLocationPermission;

  const QiblaState({
    this.qiblaDirection,
    this.isLoading = false,
    this.error,
    this.isCompassActive = false,
    this.hasLocationPermission = false,
  });

  // Convenience getters
  double get bearingTrue => qiblaDirection?.bearingTrue ?? 0.0;
  double get deviceHeading => qiblaDirection?.deviceHeading ?? 0.0;
  double get directionDelta => qiblaDirection?.directionDelta ?? 0.0;
  double get distanceKm => qiblaDirection?.distanceKm ?? 0.0;
  CompassStatus get compassStatus => qiblaDirection?.compassStatus ?? CompassStatus.notAvailable;
  QiblaMode get mode => qiblaDirection?.mode ?? QiblaMode.compass;
  double get userLatitude => qiblaDirection?.userLatitude ?? 0.0;
  double get userLongitude => qiblaDirection?.userLongitude ?? 0.0;
  bool get isManualLocation => qiblaDirection?.isManualLocation ?? false;

  QiblaState copyWith({
    QiblaDirection? qiblaDirection,
    bool? isLoading,
    String? error,
    bool? isCompassActive,
    bool? hasLocationPermission,
  }) {
    return QiblaState(
      qiblaDirection: qiblaDirection ?? this.qiblaDirection,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isCompassActive: isCompassActive ?? this.isCompassActive,
      hasLocationPermission: hasLocationPermission ?? this.hasLocationPermission,
    );
  }

  @override
  List<Object?> get props => [
        qiblaDirection,
        isLoading,
        error,
        isCompassActive,
        hasLocationPermission,
      ];
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../../../core/l10n/app_localizations.dart';
import '../../../../core/theme/design_tokens.dart';
import '../../../../core/utils/hijri_date.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../../../settings/presentation/bloc/settings_bloc.dart';
import '../../domain/entities/prayer_times.dart';
import '../../export/prayer_times_exporter.dart';

class PrayerTimesTable extends StatelessWidget {
  final List<PrayerTimes> prayerTimesList;
  final String viewType; // 'week' or 'month'
  final String cityName;
  final bool showExportActions;

  const PrayerTimesTable({
    super.key,
    required this.prayerTimesList,
    required this.viewType,
    required this.cityName,
    this.showExportActions = true,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Column(
      children: [
        // Export actions
        if (showExportActions) ...[
          _buildExportActions(context, l10n),
          const SizedBox(height: DesignTokens.spacingM),
        ],
        
        // Table with sticky header
        Expanded(
          child: GlassmorphismCard(
            padding: EdgeInsets.zero,
            child: Column(
              children: [
                // Sticky Header
                _buildTableHeader(context, l10n),

                // Divider
                Divider(
                  height: 1,
                  color: DesignTokens.lightDivider,
                  thickness: 1,
                ),

                // Scrollable Rows
                Expanded(
                  child: ListView.builder(
                    itemCount: prayerTimesList.length,
                    itemBuilder: (context, index) {
                      final prayerTimes = prayerTimesList[index];
                      final isToday = _isToday(prayerTimes.date);
                      final isZebra = index % 2 == 1;

                      return _buildTableRow(
                        context,
                        prayerTimes,
                        isToday: isToday,
                        isZebra: isZebra,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildExportActions(BuildContext context, AppLocalizations l10n) {
    return BlocBuilder<SettingsBloc, SettingsState>(
      builder: (context, settingsState) {
        return Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _exportPdf(context, settingsState.hijriOffset, false),
                icon: const Icon(Icons.picture_as_pdf),
                label: Text(l10n.exportPdf),
              ),
            ),
            const SizedBox(width: DesignTokens.spacingS),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _exportCsv(context, settingsState.hijriOffset),
                icon: const Icon(Icons.table_chart),
                label: Text(l10n.exportCsv),
              ),
            ),
            const SizedBox(width: DesignTokens.spacingS),
            IconButton(
              onPressed: () => _showExportOptions(context, settingsState.hijriOffset),
              icon: const Icon(Icons.more_vert),
              tooltip: l10n.share,
            ),
          ],
        );
      },
    );
  }
  
  Widget _buildTableHeader(BuildContext context, AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingM,
        vertical: DesignTokens.spacingS,
      ),
      decoration: BoxDecoration(
        color: DesignTokens.isDarkMode(context) 
            ? DesignTokens.darkCard.withOpacity(0.3)
            : DesignTokens.printZebra,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(DesignTokens.radiusM),
          topRight: Radius.circular(DesignTokens.radiusM),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              l10n.date,
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                color: DesignTokens.getTextColor(context),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          _buildHeaderCell(context, l10n.fajr),
          _buildHeaderCell(context, l10n.dhuhr),
          _buildHeaderCell(context, l10n.asr),
          _buildHeaderCell(context, l10n.maghrib),
          _buildHeaderCell(context, l10n.isha),
        ],
      ),
    );
  }
  
  Widget _buildHeaderCell(BuildContext context, String text) {
    return Expanded(
      child: Text(
        text,
        style: Theme.of(context).textTheme.labelMedium?.copyWith(
          color: DesignTokens.getTextColor(context),
          fontWeight: FontWeight.w600,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
  
  Widget _buildTableRow(
    BuildContext context,
    PrayerTimes prayerTimes, {
    required bool isToday,
    required bool isZebra,
  }) {
    return BlocBuilder<SettingsBloc, SettingsState>(
      builder: (context, settingsState) {
        final hijriOffset = settingsState.hijriOffset;
        
        return Container(
          padding: const EdgeInsets.symmetric(
            horizontal: DesignTokens.spacingM,
            vertical: DesignTokens.spacingS,
          ),
          decoration: BoxDecoration(
            color: isToday
                ? DesignTokens.lightPrimary.withOpacity(0.1)
                : isZebra && !DesignTokens.isDarkMode(context)
                    ? DesignTokens.printZebra
                    : Colors.transparent,
            border: isToday
                ? Border.all(
                    color: DesignTokens.lightPrimary.withOpacity(0.3),
                    width: 1,
                  )
                : null,
          ),
          child: Row(
            children: [
              // Date column
              Expanded(
                flex: 2,
                child: _buildDateCell(context, prayerTimes.date, hijriOffset, isToday),
              ),
              
              // Prayer time columns
              _buildTimeCell(context, prayerTimes.fajr, isToday),
              _buildTimeCell(context, prayerTimes.dhuhr, isToday),
              _buildTimeCell(context, prayerTimes.asr, isToday),
              _buildTimeCell(context, prayerTimes.maghrib, isToday),
              _buildTimeCell(context, prayerTimes.isha, isToday),
            ],
          ),
        );
      },
    );
  }
  
  Widget _buildDateCell(BuildContext context, DateTime date, int hijriOffset, bool isToday) {
    final gregorianDate = DateFormat('MMM d').format(date);
    final hijriDate = HijriDateUtils.getHijriDateString(date, 'en', offset: hijriOffset)
        .split(' AH')[0]; // Remove "AH (Umm al-Qura)" suffix
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          gregorianDate,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: isToday 
                ? DesignTokens.lightPrimary
                : DesignTokens.getTextColor(context),
            fontWeight: isToday ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
        Text(
          hijriDate,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: isToday 
                ? DesignTokens.lightPrimary.withOpacity(0.8)
                : DesignTokens.getTextColor(context, secondary: true),
            fontSize: 11,
          ),
        ),
      ],
    );
  }
  
  Widget _buildTimeCell(BuildContext context, String time, bool isToday) {
    return Expanded(
      child: Text(
        time,
        style: DesignTokens.monospaceMedium.copyWith(
          color: isToday 
              ? DesignTokens.lightPrimary
              : DesignTokens.getTextColor(context),
          fontWeight: isToday ? FontWeight.w600 : FontWeight.w400,
          fontSize: 13,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
  
  bool _isToday(DateTime date) {
    final today = DateTime.now();
    return date.year == today.year &&
           date.month == today.month &&
           date.day == today.day;
  }
  
  void _exportPdf(BuildContext context, int hijriOffset, bool landscape) {
    PrayerTimesExporter.exportToPdf(
      context: context,
      prayerTimesList: prayerTimesList,
      viewType: viewType,
      cityName: cityName,
      hijriOffset: hijriOffset,
      landscape: landscape,
    );
  }
  
  void _exportCsv(BuildContext context, int hijriOffset) {
    PrayerTimesExporter.exportToCsv(
      context: context,
      prayerTimesList: prayerTimesList,
      viewType: viewType,
      cityName: cityName,
      hijriOffset: hijriOffset,
    );
  }
  
  void _showExportOptions(BuildContext context, int hijriOffset) {
    final l10n = AppLocalizations.of(context)!;
    
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(DesignTokens.spacingM),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.share),
              title: Text('${l10n.share} PDF'),
              onTap: () {
                Navigator.pop(context);
                PrayerTimesExporter.sharePdf(
                  context: context,
                  prayerTimesList: prayerTimesList,
                  viewType: viewType,
                  cityName: cityName,
                  hijriOffset: hijriOffset,
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.print),
              title: Text(l10n.print),
              onTap: () {
                Navigator.pop(context);
                _exportPdf(context, hijriOffset, false);
              },
            ),
            ListTile(
              leading: const Icon(Icons.crop_rotate),
              title: Text('${l10n.exportPdf} (${l10n.landscape})'),
              onTap: () {
                Navigator.pop(context);
                _exportPdf(context, hijriOffset, true);
              },
            ),
          ],
        ),
      ),
    );
  }
}

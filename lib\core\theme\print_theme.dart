import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'design_tokens.dart';

/// Print-specific theme for PDF generation
/// Uses solid colors, no transparency, optimized for B&W printing
class PrintTheme {
  // PDF Colors (converted to PdfColor)
  static const PdfColor backgroundColor = PdfColor.fromInt(0xFFFFFFFF);
  static const PdfColor textColor = PdfColor.fromInt(0xFF111827);
  static const PdfColor accentColor = PdfColor.fromInt(0xFF0B6B3B);
  static const PdfColor dividerColor = PdfColor.fromInt(0xFFD0D5DD);
  static const PdfColor zebraColor = PdfColor.fromInt(0xFFF6F7F9);
  static const PdfColor borderColor = PdfColor.fromInt(0xFFE5E7EB);
  
  // Page format and margins
  static const PdfPageFormat pageFormat = PdfPageFormat.a4;
  static const double marginTop = DesignTokens.printMarginTop;
  static const double marginBottom = DesignTokens.printMarginBottom;
  static const double marginLeft = DesignTokens.printMarginLeft;
  static const double marginRight = DesignTokens.printMarginRight;
  
  // Typography for PDF
  static pw.TextStyle get titleStyle => pw.TextStyle(
    fontSize: 18,
    fontWeight: pw.FontWeight.bold,
    color: textColor,
  );
  
  static pw.TextStyle get subtitleStyle => pw.TextStyle(
    fontSize: 14,
    fontWeight: pw.FontWeight.normal,
    color: textColor,
  );
  
  static pw.TextStyle get headerStyle => pw.TextStyle(
    fontSize: 12,
    fontWeight: pw.FontWeight.bold,
    color: textColor,
  );
  
  static pw.TextStyle get bodyStyle => pw.TextStyle(
    fontSize: 10,
    fontWeight: pw.FontWeight.normal,
    color: textColor,
  );
  
  static pw.TextStyle get monospaceStyle => pw.TextStyle(
    fontSize: 10,
    fontWeight: pw.FontWeight.normal,
    color: textColor,
    fontFallback: [pw.Font.courier()],
  );
  
  static pw.TextStyle get captionStyle => pw.TextStyle(
    fontSize: 8,
    fontWeight: pw.FontWeight.normal,
    color: textColor,
  );
  
  // Table styling
  static pw.TableBorder get tableBorder => pw.TableBorder.all(
    color: borderColor,
    width: 0.5,
  );
  
  static pw.EdgeInsets get cellPadding => const pw.EdgeInsets.all(6);
  
  // Header decoration
  static pw.BoxDecoration get headerDecoration => pw.BoxDecoration(
    color: zebraColor,
    border: pw.Border.all(color: borderColor, width: 0.5),
  );
  
  // Zebra row decoration
  static pw.BoxDecoration get zebraDecoration => pw.BoxDecoration(
    color: zebraColor,
  );
  
  // Standard row decoration
  static pw.BoxDecoration get standardDecoration => pw.BoxDecoration(
    color: backgroundColor,
  );
  
  // Page margins
  static pw.EdgeInsets get pageMargins => pw.EdgeInsets.only(
    top: marginTop,
    bottom: marginBottom,
    left: marginLeft,
    right: marginRight,
  );
  
  // Helper method to create table headers
  static pw.Widget createTableHeader(String text) {
    return pw.Container(
      padding: cellPadding,
      decoration: headerDecoration,
      child: pw.Text(
        text,
        style: headerStyle,
        textAlign: pw.TextAlign.center,
      ),
    );
  }
  
  // Helper method to create table cells
  static pw.Widget createTableCell(
    String text, {
    bool isMonospace = false,
    pw.TextAlign textAlign = pw.TextAlign.center,
    bool isZebra = false,
  }) {
    return pw.Container(
      padding: cellPadding,
      decoration: isZebra ? zebraDecoration : standardDecoration,
      child: pw.Text(
        text,
        style: isMonospace ? monospaceStyle : bodyStyle,
        textAlign: textAlign,
      ),
    );
  }
  
  // Helper method to create page header
  static pw.Widget createPageHeader({
    required String title,
    String? subtitle,
    String? dateRange,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(title, style: titleStyle),
        if (subtitle != null) ...[
          pw.SizedBox(height: 4),
          pw.Text(subtitle, style: subtitleStyle),
        ],
        if (dateRange != null) ...[
          pw.SizedBox(height: 4),
          pw.Text(dateRange, style: bodyStyle),
        ],
        pw.SizedBox(height: 16),
        pw.Divider(color: dividerColor, thickness: 1),
        pw.SizedBox(height: 16),
      ],
    );
  }
  
  // Helper method to create page footer
  static pw.Widget createPageFooter({
    required String appName,
    required String version,
    required String generatedAt,
  }) {
    return pw.Column(
      children: [
        pw.SizedBox(height: 16),
        pw.Divider(color: dividerColor, thickness: 0.5),
        pw.SizedBox(height: 8),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text('$appName v$version', style: captionStyle),
            pw.Text('Generated: $generatedAt', style: captionStyle),
          ],
        ),
      ],
    );
  }
  
  // Column widths for different table layouts
  static const List<double> todayColumnWidths = [2.5, 1.5, 1.5, 1.5, 1.5, 1.5]; // Date, Fajr, Dhuhr, Asr, Maghrib, Isha
  static const List<double> weekColumnWidths = [2.0, 1.2, 1.2, 1.2, 1.2, 1.2]; // Slightly narrower for week view
  static const List<double> monthColumnWidths = [1.8, 1.0, 1.0, 1.0, 1.0, 1.0]; // Even narrower for month view
  
  // Helper to get appropriate column widths
  static List<double> getColumnWidths(String viewType) {
    switch (viewType.toLowerCase()) {
      case 'today':
        return todayColumnWidths;
      case 'week':
        return weekColumnWidths;
      case 'month':
        return monthColumnWidths;
      default:
        return weekColumnWidths;
    }
  }
}

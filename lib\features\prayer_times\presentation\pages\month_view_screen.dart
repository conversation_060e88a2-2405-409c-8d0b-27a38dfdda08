import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/app_config.dart';
import '../../../../core/utils/hijri_date.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../../../settings/presentation/bloc/settings_bloc.dart';
import '../../domain/entities/prayer_times.dart';
import '../../export/services/export_service.dart';
import '../../export/prayer_times_exporter.dart';
import '../bloc/prayer_view_state.dart';
import '../widgets/export_dialog.dart';

class MonthViewScreen extends StatefulWidget {
  final List<PrayerTimes> monthPrayerTimes;
  final DateTime month;

  const MonthViewScreen({
    super.key,
    required this.monthPrayerTimes,
    required this.month,
  });

  @override
  State<MonthViewScreen> createState() => _MonthViewScreenState();
}

class _MonthViewScreenState extends State<MonthViewScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gebetszeiten – Monat'),
        actions: [
          PopupMenuButton<String>(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.more_horiz,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
            ),
            tooltip: 'Export Options',
            onSelected: (value) {
              switch (value) {
                case 'export_pdf':
                  _exportPDF();
                  break;
                case 'export_csv':
                  _exportCSV();
                  break;
                case 'share_pdf':
                  _sharePDF();
                  break;
                case 'print':
                  _printPrayerTimes();
                  break;
                case 'export_pdf_landscape':
                  _exportPDFLandscape();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export_pdf',
                child: Row(
                  children: [
                    Icon(Icons.picture_as_pdf_rounded, size: 20),
                    SizedBox(width: 12),
                    Text('Export PDF'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_csv',
                child: Row(
                  children: [
                    Icon(Icons.table_chart_rounded, size: 20),
                    SizedBox(width: 12),
                    Text('Export CSV'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'share_pdf',
                child: Row(
                  children: [
                    Icon(Icons.share_rounded, size: 20),
                    SizedBox(width: 12),
                    Text('Share PDF'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'print',
                child: Row(
                  children: [
                    Icon(Icons.print_rounded, size: 20),
                    SizedBox(width: 12),
                    Text('Print'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_pdf_landscape',
                child: Row(
                  children: [
                    Icon(Icons.crop_rotate_rounded, size: 20),
                    SizedBox(width: 12),
                    Text('PDF Landscape'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Container(
        decoration: _getBackgroundDecoration(context),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(AppConfig.defaultPadding),
            child: Column(
              children: [
                // Month header
                _buildMonthHeader(),
                const SizedBox(height: AppConfig.defaultPadding),
                
                // Prayer times list
                Expanded(
                  child: _buildPrayerTimesList(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMonthHeader() {
    return BlocBuilder<SettingsBloc, SettingsState>(
      builder: (context, state) {
        final languageCode = state.language.split('_')[0];
        final monthName = _getMonthName(widget.month.month, languageCode);
        final year = widget.month.year;
        
        return GlassmorphismCard(
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    Icons.calendar_month,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '$monthName $year',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        Text(
                          '${widget.monthPrayerTimes.length} days',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      'Month View',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPrayerTimesList() {
    if (widget.monthPrayerTimes.isEmpty) {
      return const Center(
        child: Text('No prayer times available'),
      );
    }

    return ListView.builder(
      itemCount: widget.monthPrayerTimes.length,
      itemBuilder: (context, index) {
        final prayerTimes = widget.monthPrayerTimes[index];
        final isToday = _isToday(prayerTimes.date);
        
        return _buildDayCard(prayerTimes, isToday);
      },
    );
  }

  Widget _buildDayCard(PrayerTimes prayerTimes, bool isToday) {
    return BlocBuilder<SettingsBloc, SettingsState>(
      builder: (context, state) {
        final languageCode = state.language.split('_')[0];
        final hijriOffset = state.hijriOffset;
        final hijriDateStr = HijriDateUtils.getHijriDateString(
          prayerTimes.date, 
          languageCode, 
          offset: hijriOffset,
        );
        
        return GlassmorphismCard(
          margin: const EdgeInsets.only(bottom: AppConfig.smallPadding),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppConfig.cardBorderRadius),
              border: isToday 
                  ? Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 2,
                    )
                  : null,
            ),
            child: Padding(
              padding: const EdgeInsets.all(AppConfig.defaultPadding),
              child: Column(
                children: [
                  // Date header
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: isToday 
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Center(
                          child: Text(
                            '${prayerTimes.date.day}',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: isToday 
                                  ? Colors.white
                                  : Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _getFullDateString(prayerTimes.date),
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: isToday ? FontWeight.w600 : FontWeight.w500,
                                color: isToday ? Theme.of(context).colorScheme.primary : null,
                              ),
                            ),
                            Text(
                              _getWeekdayName(prayerTimes.date),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                            // Hijri date
                            Text(
                              _getCompactHijriDate(hijriDateStr),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppConfig.primaryGold,
                                fontSize: 11,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (isToday)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'Today',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  
                  // Prayer times grid
                  Row(
                    children: [
                      Expanded(child: _buildPrayerTimeItem('Fajr', prayerTimes.fajr)),
                      Expanded(child: _buildPrayerTimeItem('Dhuhr', prayerTimes.dhuhr)),
                      Expanded(child: _buildPrayerTimeItem('Asr', prayerTimes.asr)),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(child: _buildPrayerTimeItem('Maghrib', prayerTimes.maghrib)),
                      Expanded(child: _buildPrayerTimeItem('Isha', prayerTimes.isha)),
                      const Expanded(child: SizedBox()), // Empty space for alignment
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPrayerTimeItem(String name, String time) {
    return Column(
      children: [
        Text(
          name,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          time,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  bool _isToday(DateTime date) {
    final today = HijriDateUtils.getTodayLocal();
    return date.year == today.year &&
           date.month == today.month &&
           date.day == today.day;
  }

  String _getWeekdayName(DateTime date) {
    const weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    return weekdays[date.weekday - 1];
  }

  String _getFullDateString(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getMonthName(int month, String languageCode) {
    const monthNames = {
      'en': ['January', 'February', 'March', 'April', 'May', 'June',
             'July', 'August', 'September', 'October', 'November', 'December'],
      'de': ['Januar', 'Februar', 'März', 'April', 'Mai', 'Juni',
             'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember'],
      'hr': ['Siječanj', 'Veljača', 'Ožujak', 'Travanj', 'Svibanj', 'Lipanj',
             'Srpanj', 'Kolovoz', 'Rujan', 'Listopad', 'Studeni', 'Prosinac'],
      'fr': ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
             'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'],
      'ar': ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
             'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
    };
    
    final names = monthNames[languageCode] ?? monthNames['en']!;
    return names[month - 1];
  }

  String _getCompactHijriDate(String fullHijriDate) {
    // Extract day and month from full Hijri date string
    final parts = fullHijriDate.split(' AH');
    if (parts.isNotEmpty) {
      return parts[0]; // Return everything before " AH"
    }
    return fullHijriDate;
  }

  BoxDecoration _getBackgroundDecoration(BuildContext context) {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          Theme.of(context).colorScheme.primary.withOpacity(0.05),
          Theme.of(context).scaffoldBackgroundColor,
        ],
      ),
    );
  }

  void _exportPDF() {
    final settingsState = context.read<SettingsBloc>().state;
    final cityName = widget.monthPrayerTimes.isNotEmpty 
        ? (widget.monthPrayerTimes.first.city.isNotEmpty 
            ? widget.monthPrayerTimes.first.city 
            : 'Current Location')
        : 'Current Location';
    
    PrayerTimesExporter.exportToPdf(
      context: context,
      prayerTimesList: widget.monthPrayerTimes,
      viewType: 'month',
      cityName: cityName,
      hijriOffset: settingsState.hijriOffset,
      landscape: false,
    );
  }

  void _exportCSV() {
    final settingsState = context.read<SettingsBloc>().state;
    final cityName = widget.monthPrayerTimes.isNotEmpty 
        ? (widget.monthPrayerTimes.first.city.isNotEmpty 
            ? widget.monthPrayerTimes.first.city 
            : 'Current Location')
        : 'Current Location';
    
    PrayerTimesExporter.exportToCsv(
      context: context,
      prayerTimesList: widget.monthPrayerTimes,
      viewType: 'month',
      cityName: cityName,
      hijriOffset: settingsState.hijriOffset,
    );
  }

  void _sharePDF() {
    final settingsState = context.read<SettingsBloc>().state;
    final cityName = widget.monthPrayerTimes.isNotEmpty 
        ? (widget.monthPrayerTimes.first.city.isNotEmpty 
            ? widget.monthPrayerTimes.first.city 
            : 'Current Location')
        : 'Current Location';
    
    PrayerTimesExporter.sharePdf(
      context: context,
      prayerTimesList: widget.monthPrayerTimes,
      viewType: 'month',
      cityName: cityName,
      hijriOffset: settingsState.hijriOffset,
    );
  }

  void _printPrayerTimes() {
    final settingsState = context.read<SettingsBloc>().state;
    final cityName = widget.monthPrayerTimes.isNotEmpty 
        ? (widget.monthPrayerTimes.first.city.isNotEmpty 
            ? widget.monthPrayerTimes.first.city 
            : 'Current Location')
        : 'Current Location';
    
    PrayerTimesExporter.printPrayerTimes(
      context: context,
      prayerTimesList: widget.monthPrayerTimes,
      viewType: 'month',
      cityName: cityName,
      hijriOffset: settingsState.hijriOffset,
      landscape: false,
    );
  }

  void _exportPDFLandscape() {
    final settingsState = context.read<SettingsBloc>().state;
    final cityName = widget.monthPrayerTimes.isNotEmpty 
        ? (widget.monthPrayerTimes.first.city.isNotEmpty 
            ? widget.monthPrayerTimes.first.city 
            : 'Current Location')
        : 'Current Location';
    
    PrayerTimesExporter.exportToPdf(
      context: context,
      prayerTimesList: widget.monthPrayerTimes,
      viewType: 'month',
      cityName: cityName,
      hijriOffset: settingsState.hijriOffset,
      landscape: true,
    );
  }
}

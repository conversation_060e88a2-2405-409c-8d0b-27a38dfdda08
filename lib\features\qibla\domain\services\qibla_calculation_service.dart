import 'dart:math' as math;
import '../entities/qibla_direction.dart';
import '../utils/sensor_fusion_utils.dart';

/// Service for Qibla direction calculations using great circle formulas
class QiblaCalculationService {
  /// Calculate initial bearing (great circle) from user location to Kaaba
  /// Returns TRUE bearing (relative to geographic north)
  ///
  /// Formula: θ = atan2(sin(Δλ)cos(φK), cos(φ)sin(φK) - sin(φ)cos(φK)cos(Δλ))
  /// Where φ = latitude, λ = longitude, K = Kaaba
  static double calculateTrueBearing({
    required double userLatitude,
    required double userLongitude,
  }) {
    // Convert degrees to radians
    final phi = _degreesToRadians(userLatitude);
    final lambda = _degreesToRadians(userLongitude);
    final phiK = _degreesToRadians(QiblaDirection.kaabaLatitude);
    final lambdaK = _degreesToRadians(QiblaDirection.kaabaLongitude);

    // Calculate delta longitude
    final deltaLambda = lambdaK - lambda;

    // Calculate initial bearing using great circle formula
    final y = math.sin(deltaLambda) * math.cos(phiK);
    final x = math.cos(phi) * math.sin(phiK) -
              math.sin(phi) * math.cos(phiK) * math.cos(deltaLambda);

    final theta = math.atan2(y, x);

    // Convert to degrees and normalize to 0-360
    final bearingDegrees = _radiansToDegrees(theta);
    return SensorFusionUtils.normalizeAngle(bearingDegrees);
  }

  /// Calculate magnetic bearing from user location to Kaaba
  /// Applies magnetic declination correction
  static double calculateMagneticBearing({
    required double userLatitude,
    required double userLongitude,
  }) {
    final trueBearing = calculateTrueBearing(
      userLatitude: userLatitude,
      userLongitude: userLongitude,
    );

    final magneticDeclination = SensorFusionUtils.calculateMagneticDeclination(
      latitude: userLatitude,
      longitude: userLongitude,
    );

    return SensorFusionUtils.trueToMagneticHeading(
      trueHeading: trueBearing,
      magneticDeclination: magneticDeclination,
    );
  }
  
  /// Calculate distance to Kaaba using Haversine formula
  /// 
  /// Formula: a = sin²(Δφ/2) + cos(φ)cos(φK)sin²(Δλ/2)
  ///          c = 2⋅atan2(√a, √(1-a))
  ///          d = R⋅c (where R = Earth radius)
  static double calculateDistance({
    required double userLatitude,
    required double userLongitude,
  }) {
    const earthRadiusKm = 6371.0; // Earth's radius in kilometers
    
    // Convert degrees to radians
    final phi1 = _degreesToRadians(userLatitude);
    final lambda1 = _degreesToRadians(userLongitude);
    final phi2 = _degreesToRadians(QiblaDirection.kaabaLatitude);
    final lambda2 = _degreesToRadians(QiblaDirection.kaabaLongitude);
    
    // Calculate differences
    final deltaPhi = phi2 - phi1;
    final deltaLambda = lambda2 - lambda1;
    
    // Haversine formula
    final a = math.sin(deltaPhi / 2) * math.sin(deltaPhi / 2) +
              math.cos(phi1) * math.cos(phi2) *
              math.sin(deltaLambda / 2) * math.sin(deltaLambda / 2);
    
    final c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    
    return earthRadiusKm * c;
  }
  
  /// Calculate direction delta for arrow rotation
  /// 
  /// This is the angle the arrow needs to rotate to point to Qibla
  /// Formula: directionDelta = (bearingTrue - deviceHeading + 360) % 360
  static double calculateDirectionDelta({
    required double bearingTrue,
    required double deviceHeading,
  }) {
    final delta = bearingTrue - deviceHeading;
    return _normalizeDegrees(delta);
  }
  
  /// Smooth compass readings to reduce jitter
  /// 
  /// Uses exponential moving average for smooth transitions
  static double smoothCompassReading({
    required double currentReading,
    required double previousReading,
    double smoothingFactor = 0.1,
  }) {
    // Handle angle wrapping (e.g., 359° to 1°)
    double delta = currentReading - previousReading;
    if (delta > 180) {
      delta -= 360;
    } else if (delta < -180) {
      delta += 360;
    }
    
    final smoothedReading = previousReading + (smoothingFactor * delta);
    return _normalizeDegrees(smoothedReading);
  }
  
  /// Check if compass reading indicates calibration is needed
  /// 
  /// Returns true if readings are erratic or accuracy is poor
  static bool needsCalibration({
    required double accuracy,
    required List<double> recentReadings,
  }) {
    // If accuracy is very poor (> 15 degrees), needs calibration
    if (accuracy > 15.0) return true;
    
    // If we don't have enough readings, assume OK
    if (recentReadings.length < 5) return false;
    
    // Check for erratic readings (high variance)
    final variance = _calculateVariance(recentReadings);
    return variance > 100.0; // Threshold for erratic readings
  }
  
  /// Compensate for device tilt using accelerometer data
  /// 
  /// This helps maintain accuracy when device is not held flat
  static double compensateForTilt({
    required double magneticHeading,
    required double pitch,
    required double roll,
  }) {
    // Simple tilt compensation - more complex algorithms can be implemented
    // For now, we apply a basic correction based on device orientation
    
    // Convert to radians
    final pitchRad = _degreesToRadians(pitch);
    final rollRad = _degreesToRadians(roll);
    final headingRad = _degreesToRadians(magneticHeading);
    
    // Apply tilt compensation (simplified)
    final compensatedHeading = headingRad + 
        (math.sin(rollRad) * math.sin(pitchRad) * 0.1);
    
    return _normalizeDegrees(_radiansToDegrees(compensatedHeading));
  }
  
  // Helper methods
  
  static double _degreesToRadians(double degrees) {
    return degrees * math.pi / 180.0;
  }
  
  static double _radiansToDegrees(double radians) {
    return radians * 180.0 / math.pi;
  }
  
  static double _normalizeDegrees(double degrees) {
    double normalized = degrees % 360;
    if (normalized < 0) normalized += 360;
    return normalized;
  }
  
  static double _calculateVariance(List<double> values) {
    if (values.isEmpty) return 0.0;
    
    final mean = values.reduce((a, b) => a + b) / values.length;
    final squaredDifferences = values.map((x) => math.pow(x - mean, 2));
    return squaredDifferences.reduce((a, b) => a + b) / values.length;
  }
}

@echo off
echo Setting up notification sounds for AlFalah Prayer Times...
echo.

REM Check if source files exist
if not exist "assets\audio\Adhan_2_short.mp3" (
    echo ERROR: assets\audio\Adhan_2_short.mp3 not found!
    pause
    exit /b 1
)

if not exist "assets\audio\Adhan_3_short.mp3" (
    echo ERROR: assets\audio\Adhan_3_short.mp3 not found!
    pause
    exit /b 1
)

REM Create raw directory if it doesn't exist
if not exist "android\app\src\main\res\raw" (
    echo Creating raw resources directory...
    mkdir "android\app\src\main\res\raw"
)

REM Copy sound files
echo Copying Adhan_2_short.mp3...
copy "assets\audio\Adhan_2_short.mp3" "android\app\src\main\res\raw\adhan_2_short.mp3" >nul
if %errorlevel% neq 0 (
    echo ERROR: Failed to copy Adhan_2_short.mp3
    pause
    exit /b 1
)

echo Copying Adhan_3_short.mp3...
copy "assets\audio\Adhan_3_short.mp3" "android\app\src\main\res\raw\adhan_3_short.mp3" >nul
if %errorlevel% neq 0 (
    echo ERROR: Failed to copy <PERSON>han_3_short.mp3
    pause
    exit /b 1
)

echo.
echo SUCCESS: All adhan sound files have been copied to Android raw resources!
echo.
echo The following files are now available for notifications:
echo - adhan_1_short.mp3 (already existed)
echo - adhan_2_short.mp3 (copied)
echo - adhan_3_short.mp3 (copied)
echo.
echo You can now build the APK with: flutter build apk
echo.
pause
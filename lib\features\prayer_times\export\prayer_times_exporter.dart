import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/print_theme.dart';
import '../../../core/utils/hijri_date.dart';
import '../domain/entities/prayer_times.dart';

class PrayerTimesExporter {
  static const String appName = 'AlFalah - Prayer Times';
  static const String appVersion = '1.0.0';
  
  /// Export prayer times to PDF
  static Future<void> exportToPdf({
    required BuildContext context,
    required List<PrayerTimes> prayerTimesList,
    required String viewType, // 'today', 'week', 'month'
    required String cityName,
    required int hijriOffset,
    bool landscape = false,
  }) async {
    try {
      final pdf = await _generatePdf(
        prayerTimesList: prayerTimesList,
        viewType: viewType,
        cityName: cityName,
        hijriOffset: hijriOffset,
        landscape: landscape,
      );
      
      final fileName = _generateFileName(
        cityName: cityName,
        viewType: viewType,
        startDate: prayerTimesList.first.date,
        endDate: prayerTimesList.last.date,
        extension: 'pdf',
      );
      
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: fileName,
        format: landscape ? PdfPageFormat.a4.landscape : PdfPageFormat.a4,
      );
    } catch (e) {
      _showErrorDialog(context, 'Failed to export PDF: $e');
    }
  }
  
  /// Share prayer times as PDF
  static Future<void> sharePdf({
    required BuildContext context,
    required List<PrayerTimes> prayerTimesList,
    required String viewType,
    required String cityName,
    required int hijriOffset,
    bool landscape = false,
  }) async {
    try {
      final pdf = await _generatePdf(
        prayerTimesList: prayerTimesList,
        viewType: viewType,
        cityName: cityName,
        hijriOffset: hijriOffset,
        landscape: landscape,
      );

      final fileName = _generateFileName(
        cityName: cityName,
        viewType: viewType,
        startDate: prayerTimesList.first.date,
        endDate: prayerTimesList.last.date,
        extension: 'pdf',
      );

      final bytes = await pdf.save();
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/$fileName');
      await file.writeAsBytes(bytes);

      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Prayer times for $cityName',
        subject: 'Prayer Times - $cityName',
      );
    } catch (e) {
      _showErrorDialog(context, 'Failed to share PDF: $e');
    }
  }

  /// Print prayer times (opens system print dialog)
  static Future<void> printPrayerTimes({
    required BuildContext context,
    required List<PrayerTimes> prayerTimesList,
    required String viewType,
    required String cityName,
    required int hijriOffset,
    bool landscape = false,
  }) async {
    try {
      final pdf = await _generatePdf(
        prayerTimesList: prayerTimesList,
        viewType: viewType,
        cityName: cityName,
        hijriOffset: hijriOffset,
        landscape: landscape,
      );

      final fileName = _generateFileName(
        cityName: cityName,
        viewType: viewType,
        startDate: prayerTimesList.first.date,
        endDate: prayerTimesList.last.date,
        extension: 'pdf',
      );

      // Open system print dialog
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: fileName,
        format: landscape ? PdfPageFormat.a4.landscape : PdfPageFormat.a4,
      );
    } catch (e) {
      _showErrorDialog(context, 'Failed to print: $e');
    }
  }
  
  /// Export prayer times to CSV
  static Future<void> exportToCsv({
    required BuildContext context,
    required List<PrayerTimes> prayerTimesList,
    required String viewType,
    required String cityName,
    required int hijriOffset,
  }) async {
    try {
      final csvContent = _generateCsv(
        prayerTimesList: prayerTimesList,
        hijriOffset: hijriOffset,
      );
      
      final fileName = _generateFileName(
        cityName: cityName,
        viewType: viewType,
        startDate: prayerTimesList.first.date,
        endDate: prayerTimesList.last.date,
        extension: 'csv',
      );
      
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/$fileName');
      await file.writeAsString(csvContent);
      
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Prayer times for $cityName (CSV)',
        subject: 'Prayer Times - $cityName',
      );
    } catch (e) {
      _showErrorDialog(context, 'Failed to export CSV: $e');
    }
  }
  
  /// Generate PDF document
  static Future<pw.Document> _generatePdf({
    required List<PrayerTimes> prayerTimesList,
    required String viewType,
    required String cityName,
    required int hijriOffset,
    bool landscape = false,
  }) async {
    final pdf = pw.Document();
    final pageFormat = landscape ? PdfPageFormat.a4.landscape : PdfPageFormat.a4;
    
    final startDate = prayerTimesList.first.date;
    final endDate = prayerTimesList.last.date;
    final dateRange = startDate == endDate
        ? DateFormat('MMMM d, yyyy').format(startDate)
        : '${DateFormat('MMM d').format(startDate)} - ${DateFormat('MMM d, yyyy').format(endDate)}';
    
    pdf.addPage(
      pw.Page(
        pageFormat: pageFormat,
        margin: PrintTheme.pageMargins,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Header
              PrintTheme.createPageHeader(
                title: 'Prayer Times - $cityName',
                subtitle: _getViewTypeTitle(viewType),
                dateRange: dateRange,
              ),
              
              // Table
              pw.Expanded(
                child: _buildPrayerTimesTable(
                  prayerTimesList: prayerTimesList,
                  viewType: viewType,
                  hijriOffset: hijriOffset,
                ),
              ),
              
              // Footer
              PrintTheme.createPageFooter(
                appName: appName,
                version: appVersion,
                generatedAt: DateFormat('MMM d, yyyy HH:mm').format(DateTime.now()),
              ),
            ],
          );
        },
      ),
    );
    
    return pdf;
  }
  
  /// Build prayer times table for PDF
  static pw.Widget _buildPrayerTimesTable({
    required List<PrayerTimes> prayerTimesList,
    required String viewType,
    required int hijriOffset,
  }) {
    final columnWidths = PrintTheme.getColumnWidths(viewType);
    
    return pw.Table(
      border: PrintTheme.tableBorder,
      columnWidths: {
        for (int i = 0; i < columnWidths.length; i++)
          i: pw.FlexColumnWidth(columnWidths[i]),
      },
      children: [
        // Header row
        pw.TableRow(
          children: [
            PrintTheme.createTableHeader('Date'),
            PrintTheme.createTableHeader('Fajr'),
            PrintTheme.createTableHeader('Dhuhr'),
            PrintTheme.createTableHeader('Asr'),
            PrintTheme.createTableHeader('Maghrib'),
            PrintTheme.createTableHeader('Isha'),
          ],
        ),
        
        // Data rows
        ...prayerTimesList.asMap().entries.map((entry) {
          final index = entry.key;
          final prayerTimes = entry.value;
          final isZebra = index % 2 == 1;
          
          final gregorianDate = DateFormat('MMM d').format(prayerTimes.date);
          final hijriDate = HijriDateUtils.getHijriDateString(
            prayerTimes.date,
            'en',
            offset: hijriOffset,
          ).split(' AH')[0]; // Remove "AH (Umm al-Qura)" suffix
          
          return pw.TableRow(
            children: [
              PrintTheme.createTableCell(
                '$gregorianDate\n$hijriDate',
                textAlign: pw.TextAlign.left,
                isZebra: isZebra,
              ),
              PrintTheme.createTableCell(
                prayerTimes.fajr,
                isMonospace: true,
                isZebra: isZebra,
              ),
              PrintTheme.createTableCell(
                prayerTimes.dhuhr,
                isMonospace: true,
                isZebra: isZebra,
              ),
              PrintTheme.createTableCell(
                prayerTimes.asr,
                isMonospace: true,
                isZebra: isZebra,
              ),
              PrintTheme.createTableCell(
                prayerTimes.maghrib,
                isMonospace: true,
                isZebra: isZebra,
              ),
              PrintTheme.createTableCell(
                prayerTimes.isha,
                isMonospace: true,
                isZebra: isZebra,
              ),
            ],
          );
        }).toList(),
      ],
    );
  }
  
  /// Generate CSV content
  static String _generateCsv({
    required List<PrayerTimes> prayerTimesList,
    required int hijriOffset,
  }) {
    final buffer = StringBuffer();
    
    // Header
    buffer.writeln('Date,Hijri Date,Fajr,Dhuhr,Asr,Maghrib,Isha');
    
    // Data rows
    for (final prayerTimes in prayerTimesList) {
      final gregorianDate = DateFormat('yyyy-MM-dd').format(prayerTimes.date);
      final hijriDate = HijriDateUtils.getHijriDateString(
        prayerTimes.date,
        'en',
        offset: hijriOffset,
      ).split(' AH')[0]; // Remove "AH (Umm al-Qura)" suffix
      
      buffer.writeln([
        gregorianDate,
        '"$hijriDate"',
        prayerTimes.fajr,
        prayerTimes.dhuhr,
        prayerTimes.asr,
        prayerTimes.maghrib,
        prayerTimes.isha,
      ].join(','));
    }
    
    return buffer.toString();
  }
  
  /// Generate filename
  static String _generateFileName({
    required String cityName,
    required String viewType,
    required DateTime startDate,
    required DateTime endDate,
    required String extension,
  }) {
    final cleanCityName = cityName.replaceAll(RegExp(r'[^\w\s-]'), '').replaceAll(' ', '_');
    final startDateStr = DateFormat('yyyy-MM-dd').format(startDate);
    final endDateStr = DateFormat('yyyy-MM-dd').format(endDate);
    
    if (startDate == endDate) {
      return 'PrayerTimes_${cleanCityName}_$startDateStr.$extension';
    } else {
      return 'PrayerTimes_${cleanCityName}_${startDateStr}_to_$endDateStr.$extension';
    }
  }
  
  /// Get view type title
  static String _getViewTypeTitle(String viewType) {
    switch (viewType.toLowerCase()) {
      case 'today':
        return 'Daily View';
      case 'week':
        return 'Weekly View';
      case 'month':
        return 'Monthly View';
      default:
        return 'Prayer Times';
    }
  }
  
  /// Show error dialog
  static void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

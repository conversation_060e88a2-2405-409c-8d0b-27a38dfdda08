import 'package:flutter/material.dart';

class AppConfig {
  static const String appName = 'AlFalah';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String aladhanApiBaseUrl = 'https://api.aladhan.com/v1';
  static const String timingsEndpoint = '/timings';
  static const String calendarEndpoint = '/calendar';
  static const String cityEndpoint = '/timingsByCity';
  static const String addressEndpoint = '/timingsByAddress';
  
  // Default Prayer Calculation Method (Umm al-Qura)
  static const int defaultCalculationMethod = 4;
  
  // Calculation Methods
  static const Map<int, String> calculationMethods = {
    1: 'University of Islamic Sciences, Karachi',
    2: 'Islamic Society of North America (ISNA)',
    3: 'Muslim World League (MWL)',
    4: 'Umm al-Qura, Makkah', // Default
    5: 'Egyptian General Authority of Survey',
    7: 'Institute of Geophysics, University of Tehran',
    8: 'Gulf Region',
    9: 'Kuwait',
    10: 'Qatar',
    11: '<PERSON><PERSON>gama Islam Singapura, Singapore',
    12: 'Union Organization islamic de France',
    13: '<PERSON><PERSON><PERSON>, Turkey',
    14: 'Spiritual Administration of Muslims of Russia',
  };
  
  // Supported Languages
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English (default)
    Locale('de', 'DE'), // German
    Locale('hr', 'HR'), // Croatian
    Locale('fr', 'FR'), // French
    Locale('ar', 'SA'), // Arabic
    Locale('zh', 'CN'), // Chinese (Simplified)
    Locale('ru', 'RU'), // Russian
    Locale('ms', 'MY'), // Malaysian (Malay)
    Locale('id', 'ID'), // Indonesian
    Locale('hi', 'IN'), // Hindi (Indian)
    Locale('es', 'ES'), // Spanish
  ];
  
  // Prayer Names
  static const List<String> prayerNames = [
    'Fajr',
    'Dhuhr',
    'Asr',
    'Maghrib',
    'Isha',
  ];
  
  // Default Notification Settings
  static const bool defaultNotificationsEnabled = true;
  static const bool defaultAdhanEnabled = true;
  static const String defaultAdhanSound = 'default_adhan.mp3';
  
  // Cache Settings
  static const int cacheValidityDays = 30;
  static const String cacheBoxName = 'prayer_times_cache';
  static const String settingsBoxName = 'app_settings';
  
  // Location Settings
  static const double defaultLatitude = 21.3891; // Mecca
  static const double defaultLongitude = 39.8579; // Mecca
  static const String defaultCity = 'Mecca';
  static const String defaultCountry = 'Saudi Arabia';
  
  // UI Constants
  static const double cardBorderRadius = 16.0;
  static const double buttonBorderRadius = 12.0;
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  
  // Colors
  static const Color primaryGreen = Color(0xFF1B4332); // Darker elegant green
  static const Color primaryGold = Color(0xFFFFD166);
  static const Color backgroundLight = Color(0xFFF8F9FA);
  static const Color backgroundDark = Color(0xFF121212);
  static const Color cardLight = Color(0xFFFFFFFF);
  static const Color cardDark = Color(0xFF1E1E1E);
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
}

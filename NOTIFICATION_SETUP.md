# Notification Setup Guide

## Issue #3: Fix Notification System - Adhan Sound Not Playing

### Current Problem
The notification system is configured but adhan sounds may not play properly due to missing raw resource files for Android notifications.

### Solution

#### 1. Copy Adhan Sound Files to Android Raw Resources

The notification system requires adhan sound files to be placed in the Android raw resources directory. Currently, only `adhan_1_short.mp3` is available.

**Required Actions:**

1. **Copy additional sound files to Android raw resources:**
```cmd
copy "assets\audio\Adhan_2_short.mp3" "android\app\src\main\res\raw\adhan_2_short.mp3"
copy "assets\audio\Adhan_3_short.mp3" "android\app\src\main\res\raw\adhan_3_short.mp3"
```

2. **Verify files are in place:**
Check that the following files exist in `android/app/src/main/res/raw/`:
- `adhan_1_short.mp3` ✓ (already exists)
- `adhan_2_short.mp3` (needs to be copied)
- `adhan_3_short.mp3` (needs to be copied)

#### 2. Update Notification Sound Mapping (COMPLETED)

✅ **Updated:** The notification system now properly reads the user's selected adhan sound from settings instead of using a hardcoded sound.

✅ **Enhanced:** Added proper sound mapping with fallbacks for missing raw resources.

#### 3. Test Notification System

After copying the sound files:

1. **Build the app:**
```cmd
flutter clean
flutter build apk
```

2. **Test notifications:**
   - Go to Settings → Debug → Notification Testing
   - Try "Test Immediate Notification" and "Test Scheduled (10s)"
   - Check notification permissions are granted
   - Verify adhan sound plays

#### 4. Notification Permission Issues

If notifications don't work:

1. **Check app permissions:**
   - Go to Android Settings → Apps → AlFalah → Permissions
   - Enable "Notifications"
   - Enable "Alarms & reminders" (for exact scheduling)

2. **Check battery optimization:**
   - Go to Android Settings → Battery → Battery optimization
   - Set AlFalah to "Not optimized"

3. **Use the debug tools:**
   - Settings → Debug → Notification Testing
   - "Check Notification Status" to see permission status
   - "Request Permissions" to request all required permissions

### Code Changes Made

1. **Enhanced notification datasource** (`notification_datasource.dart`):
   - Added proper settings integration to read user's selected adhan sound
   - Added sound mapping function to handle different adhan sounds
   - Added fallbacks for missing raw resources
   - Enhanced debug logging

2. **Improved error handling:**
   - Better error messages for notification failures
   - Graceful fallbacks when sound files are missing

### Testing Checklist

- [ ] Copy additional adhan sound files to raw resources
- [ ] Build and install APK
- [ ] Test immediate notifications
- [ ] Test scheduled notifications
- [ ] Test with different adhan sounds selected
- [ ] Verify notifications work when app is closed
- [ ] Check adhan sound plays correctly
- [ ] Test notification toggles per prayer

### Troubleshooting

**If notifications still don't work:**

1. Check Android notification channels are properly created
2. Verify exact alarm permissions are granted (Android 12+)
3. Check device-specific battery optimization settings
4. Try testing with default system notification sound first
5. Use the built-in notification testing tools

**If adhan sound doesn't play:**

1. Verify the selected sound file exists in raw resources
2. Check the sound mapping in `_mapToRawResourceName()` function
3. Try with "silent" notification to confirm the issue is sound-related
4. Check device volume settings and Do Not Disturb mode

### Next Steps

After implementing these fixes:
- Issue #4: Fix prayer time notification toggle buttons
- Issue #5: Implement push notification permissions
- Issue #6: Fix qibla compass calibration
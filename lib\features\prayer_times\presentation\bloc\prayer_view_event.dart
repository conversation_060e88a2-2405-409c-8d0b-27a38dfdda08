import 'package:equatable/equatable.dart';
import 'prayer_view_state.dart';

abstract class PrayerViewEvent extends Equatable {
  const PrayerViewEvent();

  @override
  List<Object> get props => [];
}

class ChangeViewType extends PrayerViewEvent {
  final PrayerViewType viewType;

  const ChangeViewType(this.viewType);

  @override
  List<Object> get props => [viewType];
}

class NavigateToWeekView extends PrayerViewEvent {
  final DateTime date;

  const NavigateToWeekView(this.date);

  @override
  List<Object> get props => [date];
}

class NavigateToMonthView extends PrayerViewEvent {
  final DateTime date;

  const NavigateToMonthView(this.date);

  @override
  List<Object> get props => [date];
}

class UpdateSelectedDate extends PrayerViewEvent {
  final DateTime date;

  const UpdateSelectedDate(this.date);

  @override
  List<Object> get props => [date];
}

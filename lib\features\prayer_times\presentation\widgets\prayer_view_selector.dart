import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/app_config.dart';
import '../../../../core/utils/hijri_date.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../../../settings/presentation/bloc/settings_bloc.dart';
import '../../domain/entities/prayer_times.dart';
import '../../domain/repositories/prayer_times_repository.dart';
import '../pages/week_view_page.dart';
import '../pages/month_view_page.dart';
import '../bloc/prayer_view_bloc.dart';
import '../bloc/prayer_view_state.dart';

class PrayerViewSelector extends StatelessWidget {
  final PrayerViewType selectedView;
  final Function(PrayerViewType) onViewChanged;

  const PrayerViewSelector({
    super.key,
    required this.selectedView,
    required this.onViewChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GlassmorphismCard(
      margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
      child: Row(
        children: [
          Expanded(
            child: _buildViewButton(
              context,
              PrayerViewType.today,
              'Today',
              Icons.today,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildViewButton(
              context,
              PrayerViewType.week,
              'Week',
              Icons.view_week,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildViewButton(
              context,
              PrayerViewType.month,
              'Month',
              Icons.calendar_month,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildViewButton(
    BuildContext context,
    PrayerViewType viewType,
    String label,
    IconData icon,
  ) {
    final isSelected = selectedView == viewType;

    return GestureDetector(
      onTap: () => _handleViewChange(context, viewType),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Colors.grey.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected
                  ? Colors.white
                  : Theme.of(context).colorScheme.primary,
              size: 18,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isSelected
                    ? Colors.white
                    : Theme.of(context).colorScheme.primary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleViewChange(BuildContext context, PrayerViewType viewType) {
    if (viewType == selectedView) return;

    onViewChanged(viewType);

    if (viewType == PrayerViewType.week) {
      _navigateToWeekView(context);
    } else if (viewType == PrayerViewType.month) {
      _navigateToMonthView(context);
    }
  }

  void _navigateToWeekView(BuildContext context) async {
    final today = HijriDateUtils.getTodayLocal();
    final endDate = today.add(const Duration(days: 6));

    // Show loading
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      final settingsState = context.read<SettingsBloc>().state;
      final repository = context.read<PrayerTimesRepository>();

      // Get coordinates from settings or use default (Mecca)
      final latitude = settingsState.customLatitude ?? AppConfig.defaultLatitude;
      final longitude = settingsState.customLongitude ?? AppConfig.defaultLongitude;
      final locationName = settingsState.customLocationName ?? 'Mecca';

      final result = await repository.getPrayerTimesRange(
        startDate: today,
        endDate: endDate,
        latitude: latitude,
        longitude: longitude,
        calculationMethod: settingsState.calculationMethod,
      );

      Navigator.pop(context); // Close loading dialog

      result.fold(
        (failure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to load week data: ${failure.message}')),
          );
          // Reset view to today on failure
          onViewChanged(PrayerViewType.today);
        },
        (weekData) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => WeekViewPage(
                weekPrayerTimes: weekData,
                cityName: locationName,
              ),
            ),
          ).then((_) {
            // Reset view to today when returning from week view
            onViewChanged(PrayerViewType.today);
          });
        },
      );
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
      // Reset view to today on error
      onViewChanged(PrayerViewType.today);
    }
  }

  void _navigateToMonthView(BuildContext context) async {
    final today = HijriDateUtils.getTodayLocal();
    final monthStart = DateTime(today.year, today.month, 1);
    final monthEnd = DateTime(today.year, today.month + 1, 0);

    // Show loading
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      final settingsState = context.read<SettingsBloc>().state;
      final repository = context.read<PrayerTimesRepository>();

      // Get coordinates from settings or use default (Mecca)
      final latitude = settingsState.customLatitude ?? AppConfig.defaultLatitude;
      final longitude = settingsState.customLongitude ?? AppConfig.defaultLongitude;
      final locationName = settingsState.customLocationName ?? 'Mecca';

      final result = await repository.getPrayerTimesRange(
        startDate: monthStart,
        endDate: monthEnd,
        latitude: latitude,
        longitude: longitude,
        calculationMethod: settingsState.calculationMethod,
      );

      Navigator.pop(context); // Close loading dialog

      result.fold(
        (failure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to load month data: ${failure.message}')),
          );
          // Reset view to today on failure
          onViewChanged(PrayerViewType.today);
        },
        (monthData) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => MonthViewPage(
                monthPrayerTimes: monthData,
                cityName: locationName,
              ),
            ),
          ).then((_) {
            // Reset view to today when returning from month view
            onViewChanged(PrayerViewType.today);
          });
        },
      );
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
      // Reset view to today on error
      onViewChanged(PrayerViewType.today);
    }
  }


}

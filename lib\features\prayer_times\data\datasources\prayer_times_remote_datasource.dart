import 'package:dio/dio.dart';
import '../../../../core/app_config.dart';
import '../../../../core/error/exceptions.dart';
import '../models/prayer_times_model.dart';

abstract class PrayerTimesRemoteDataSource {
  /// Get prayer times for a specific date and coordinates
  Future<PrayerTimesModel> getPrayerTimes({
    required DateTime date,
    required double latitude,
    required double longitude,
    required int calculationMethod,
  });

  /// Get prayer times by city name
  Future<PrayerTimesModel> getPrayerTimesByCity({
    required DateTime date,
    required String city,
    required String country,
    required int calculationMethod,
  });

  /// Get prayer times by address
  Future<PrayerTimesModel> getPrayerTimesByAddress({
    required DateTime date,
    required String address,
    required int calculationMethod,
  });

  /// Get monthly prayer times for caching
  Future<List<PrayerTimesModel>> getMonthlyPrayerTimes({
    required DateTime month,
    required double latitude,
    required double longitude,
    required int calculationMethod,
  });
}

class PrayerTimesRemoteDataSourceImpl implements PrayerTimesRemoteDataSource {
  final Dio dio;

  PrayerTimesRemoteDataSourceImpl({required this.dio});

  @override
  Future<PrayerTimesModel> getPrayerTimes({
    required DateTime date,
    required double latitude,
    required double longitude,
    required int calculationMethod,
  }) async {
    try {
      final timestamp = (date.millisecondsSinceEpoch / 1000).round();
      final response = await dio.get(
        '${AppConfig.aladhanApiBaseUrl}${AppConfig.timingsEndpoint}/$timestamp',
        queryParameters: {
          'latitude': latitude,
          'longitude': longitude,
          'method': calculationMethod,
        },
      );

      if (response.statusCode == 200) {
        print('API Response: ${response.data}'); // Debug print
        return PrayerTimesModel.fromJson(response.data);
      } else {
        throw ServerException(
          message: 'Failed to fetch prayer times',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.connectionError) {
        throw NetworkException(message: 'Network connection failed');
      } else {
        throw ServerException(
          message: e.message ?? 'Server error occurred',
          statusCode: e.response?.statusCode,
        );
      }
    } catch (e) {
      throw ServerException(message: 'Unexpected error: ${e.toString()}');
    }
  }

  @override
  Future<PrayerTimesModel> getPrayerTimesByCity({
    required DateTime date,
    required String city,
    required String country,
    required int calculationMethod,
  }) async {
    try {
      final timestamp = (date.millisecondsSinceEpoch / 1000).round();
      final response = await dio.get(
        '${AppConfig.aladhanApiBaseUrl}${AppConfig.cityEndpoint}/$timestamp',
        queryParameters: {
          'city': city,
          'country': country,
          'method': calculationMethod,
        },
      );

      if (response.statusCode == 200) {
        return PrayerTimesModel.fromJson(response.data);
      } else {
        throw ServerException(
          message: 'Failed to fetch prayer times by city',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.connectionError) {
        throw NetworkException(message: 'Network connection failed');
      } else {
        throw ServerException(
          message: e.message ?? 'Server error occurred',
          statusCode: e.response?.statusCode,
        );
      }
    } catch (e) {
      throw ServerException(message: 'Unexpected error: ${e.toString()}');
    }
  }

  @override
  Future<PrayerTimesModel> getPrayerTimesByAddress({
    required DateTime date,
    required String address,
    required int calculationMethod,
  }) async {
    try {
      final timestamp = (date.millisecondsSinceEpoch / 1000).round();
      final response = await dio.get(
        '${AppConfig.aladhanApiBaseUrl}${AppConfig.addressEndpoint}/$timestamp',
        queryParameters: {
          'address': address,
          'method': calculationMethod,
        },
      );

      if (response.statusCode == 200) {
        return PrayerTimesModel.fromJson(response.data);
      } else {
        throw ServerException(
          message: 'Failed to fetch prayer times by address',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.connectionError) {
        throw NetworkException(message: 'Network connection failed');
      } else {
        throw ServerException(
          message: e.message ?? 'Server error occurred',
          statusCode: e.response?.statusCode,
        );
      }
    } catch (e) {
      throw ServerException(message: 'Unexpected error: ${e.toString()}');
    }
  }

  @override
  Future<List<PrayerTimesModel>> getMonthlyPrayerTimes({
    required DateTime month,
    required double latitude,
    required double longitude,
    required int calculationMethod,
  }) async {
    try {
      final response = await dio.get(
        '${AppConfig.aladhanApiBaseUrl}${AppConfig.calendarEndpoint}/${month.year}/${month.month}',
        queryParameters: {
          'latitude': latitude,
          'longitude': longitude,
          'method': calculationMethod,
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['data'];
        return data.map((dayData) {
          final dayNumber = dayData['date']['gregorian']['day'];
          final date = DateTime(month.year, month.month, int.parse(dayNumber));
          return PrayerTimesModel.fromCalendarJson(dayData, date);
        }).toList();
      } else {
        throw ServerException(
          message: 'Failed to fetch monthly prayer times',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.connectionError) {
        throw NetworkException(message: 'Network connection failed');
      } else {
        throw ServerException(
          message: e.message ?? 'Server error occurred',
          statusCode: e.response?.statusCode,
        );
      }
    } catch (e) {
      throw ServerException(message: 'Unexpected error: ${e.toString()}');
    }
  }
}

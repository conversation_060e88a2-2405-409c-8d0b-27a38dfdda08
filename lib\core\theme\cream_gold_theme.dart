import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'tokens.dart';

/// Cream & Gold theme implementation using Material 3 ColorScheme
/// This is the sophisticated Design 2 (Elegant) theme variant
class CreamGoldTheme {
  // Private constructor
  CreamGoldTheme._();

  /// Light ColorScheme for Cream & Gold theme
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    // Primary colors
    primary: ColorTokens.lightPrimaryTaupe, // #8A7B68
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFD6CEC2),
    onPrimaryContainer: ColorTokens.lightText, // #2C2A27
    
    // Secondary colors
    secondary: ColorTokens.lightGold, // #C7A351
    onSecondary: Color(0xFF1A1405),
    secondaryContainer: Color(0xFFF1E6C6),
    onSecondaryContainer: Color(0xFF1A1405),
    
    // Tertiary colors
    tertiary: ColorTokens.lightGoldSoft, // #E3C972
    onTertiary: Color(0xFF1A1405),
    tertiaryContainer: Color(0xFFF8F3DC),
    onTertiaryContainer: Color(0xFF1A1405),
    
    // Error colors
    error: Color(0xFFBA1A1A),
    onError: Color(0xFFFFFFFF),
    errorContainer: Color(0xFFFFDAD6),
    onErrorContainer: Color(0xFF410002),
    
    // Background and surface colors
    background: ColorTokens.lightBG, // #FAF7F1
    onBackground: ColorTokens.lightText, // #2C2A27
    surface: ColorTokens.lightSurface, // #FFFCF6
    onSurface: ColorTokens.lightText, // #2C2A27
    surfaceVariant: ColorTokens.lightSurfaceVariant, // #F1ECE2
    onSurfaceVariant: ColorTokens.lightTextMuted, // #7A746B
    
    // Outline and other colors
    outline: ColorTokens.lightOutline, // #CFC7BA
    outlineVariant: Color(0xFFE0D8CC),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    
    // Inverse colors
    inverseSurface: Color(0xFF2A2622),
    onInverseSurface: Color(0xFFF4EFE8),
    inversePrimary: Color(0xFFBEB39E),
  );

  /// Dark ColorScheme for Cream & Gold theme
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    // Primary colors
    primary: ColorTokens.darkPrimaryGold, // #DCC79A
    onPrimary: Color(0xFF0E0D0B),
    primaryContainer: Color(0xFF3A3226),
    onPrimaryContainer: ColorTokens.darkText, // #EDE9E3
    
    // Secondary colors
    secondary: ColorTokens.darkGold, // #CFAF56
    onSecondary: Color(0xFF1A1405),
    secondaryContainer: Color(0xFF3F3317),
    onSecondaryContainer: ColorTokens.darkText, // #EDE9E3
    
    // Tertiary colors
    tertiary: ColorTokens.darkGoldDeep, // #B5922F
    onTertiary: Color(0xFF0E0D0B),
    tertiaryContainer: Color(0xFF3F3317),
    onTertiaryContainer: ColorTokens.darkText, // #EDE9E3
    
    // Error colors
    error: Color(0xFFFFB4AB),
    onError: Color(0xFF690005),
    errorContainer: Color(0xFF93000A),
    onErrorContainer: Color(0xFFFFDAD6),
    
    // Background and surface colors
    background: ColorTokens.darkBG, // #151311
    onBackground: ColorTokens.darkText, // #EDE9E3
    surface: ColorTokens.darkSurface, // #1C1916
    onSurface: ColorTokens.darkText, // #EDE9E3
    surfaceVariant: ColorTokens.darkSurfaceVariant, // #2B2620
    onSurfaceVariant: ColorTokens.darkTextMuted, // #A59D90
    
    // Outline and other colors
    outline: ColorTokens.darkOutline, // #5A5044
    outlineVariant: Color(0xFF4A453A),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    
    // Inverse colors
    inverseSurface: ColorTokens.darkText, // #EDE9E3
    onInverseSurface: ColorTokens.darkSurface, // #1C1916
    inversePrimary: Color(0xFF8A7B68),
  );

  /// Create light theme with Material 3 design
  static ThemeData createLightTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: lightColorScheme,
      textTheme: _createTextTheme(false),
      appBarTheme: _createAppBarTheme(false),
      cardTheme: _createCardTheme(false),
      elevatedButtonTheme: _createElevatedButtonTheme(false),
      outlinedButtonTheme: _createOutlinedButtonTheme(false),
      chipTheme: _createChipTheme(false),
      snackBarTheme: _createSnackBarTheme(false),
      popupMenuTheme: _createPopupMenuTheme(false),
      dividerTheme: _createDividerTheme(false),
      listTileTheme: _createListTileTheme(false),
    );
  }

  /// Create dark theme with Material 3 design
  static ThemeData createDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: darkColorScheme,
      textTheme: _createTextTheme(true),
      appBarTheme: _createAppBarTheme(true),
      cardTheme: _createCardTheme(true),
      elevatedButtonTheme: _createElevatedButtonTheme(true),
      outlinedButtonTheme: _createOutlinedButtonTheme(true),
      chipTheme: _createChipTheme(true),
      snackBarTheme: _createSnackBarTheme(true),
      popupMenuTheme: _createPopupMenuTheme(true),
      dividerTheme: _createDividerTheme(true),
      listTileTheme: _createListTileTheme(true),
    );
  }

  /// Typography with Inter font for elegant design
  static TextTheme _createTextTheme(bool isDark) {
    final baseTheme = isDark ? ThemeData.dark().textTheme : ThemeData.light().textTheme;
    final textColor = isDark ? ColorTokens.darkText : ColorTokens.lightText;
    final mutedColor = isDark ? ColorTokens.darkTextMuted : ColorTokens.lightTextMuted;
    
    return GoogleFonts.interTextTheme(baseTheme).copyWith(
      // Display styles
      displayLarge: GoogleFonts.inter(
        fontSize: 28,
        fontWeight: FontWeight.w700,
        color: textColor,
        letterSpacing: -0.5,
      ),
      displayMedium: GoogleFonts.inter(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: textColor,
        letterSpacing: -0.25,
      ),
      displaySmall: GoogleFonts.inter(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: textColor,
        letterSpacing: -0.15,
      ),
      
      // Headline styles
      headlineLarge: GoogleFonts.inter(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: textColor,
        letterSpacing: -0.15,
      ),
      headlineMedium: GoogleFonts.inter(
        fontSize: 18,
        fontWeight: FontWeight.w500,
        color: textColor,
        letterSpacing: -0.1,
      ),
      headlineSmall: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: textColor,
        letterSpacing: -0.1,
      ),
      
      // Title styles
      titleLarge: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: textColor,
        letterSpacing: -0.1,
      ),
      titleMedium: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: textColor,
        letterSpacing: -0.05,
      ),
      titleSmall: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: mutedColor,
        letterSpacing: 0,
      ),
      
      // Body styles
      bodyLarge: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: textColor,
        letterSpacing: 0,
        height: 1.4,
      ),
      bodyMedium: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: textColor,
        letterSpacing: 0,
        height: 1.33,
      ),
      bodySmall: GoogleFonts.inter(
        fontSize: 11,
        fontWeight: FontWeight.w400,
        color: mutedColor,
        letterSpacing: 0,
        height: 1.33,
      ),
      
      // Label styles
      labelLarge: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: mutedColor,
        letterSpacing: 0.1,
      ),
      labelMedium: GoogleFonts.inter(
        fontSize: 11,
        fontWeight: FontWeight.w500,
        color: mutedColor,
        letterSpacing: 0.1,
      ),
      labelSmall: GoogleFonts.inter(
        fontSize: 10,
        fontWeight: FontWeight.w500,
        color: mutedColor,
        letterSpacing: 0.1,
      ),
    );
  }

  /// AppBar theme following design rules
  static AppBarTheme _createAppBarTheme(bool isDark) {
    return AppBarTheme(
      backgroundColor: isDark ? ColorTokens.darkSurface : ColorTokens.lightPrimaryTaupe,
      foregroundColor: isDark ? ColorTokens.darkText : Colors.white,
      elevation: 0,
      centerTitle: false,
      titleTextStyle: GoogleFonts.inter(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: isDark ? ColorTokens.darkText : Colors.white,
        letterSpacing: -0.15,
      ),
      surfaceTintColor: Colors.transparent,
    );
  }

  /// Card theme with full coverage, no transparency
  static CardThemeData _createCardTheme(bool isDark) {
    return CardThemeData(
      elevation: 0,
      shadowColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      color: isDark ? ColorTokens.darkSurface : ColorTokens.lightSurface,
      surfaceTintColor: Colors.transparent,
      margin: EdgeInsets.zero,
    );
  }

  /// Elevated button theme
  static ElevatedButtonThemeData _createElevatedButtonTheme(bool isDark) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: isDark ? ColorTokens.darkPrimaryGold : ColorTokens.lightPrimaryTaupe,
        foregroundColor: isDark ? Color(0xFF0A0A0A) : Colors.white,
        elevation: 0,
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        textStyle: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          letterSpacing: -0.1,
        ),
      ),
    );
  }

  /// Outlined button theme for secondary actions
  static OutlinedButtonThemeData _createOutlinedButtonTheme(bool isDark) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: isDark ? ColorTokens.darkPrimaryGold : ColorTokens.lightPrimaryTaupe,
        side: BorderSide(
          color: isDark ? ColorTokens.darkOutline : ColorTokens.lightOutline,
          width: 1,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        textStyle: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          letterSpacing: -0.1,
        ),
      ),
    );
  }

  /// Chip theme for badges like Ramadan
  static ChipThemeData _createChipTheme(bool isDark) {
    return ChipThemeData(
      backgroundColor: ColorTokens.ramadanBadgeLight, // Gold for Ramadan
      labelStyle: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: ColorTokens.ramadanBadgeText, // Dark text
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    );
  }

  /// SnackBar theme with inverse colors
  static SnackBarThemeData _createSnackBarTheme(bool isDark) {
    return SnackBarThemeData(
      backgroundColor: isDark ? ColorTokens.snackBarDark : ColorTokens.snackBarLight,
      contentTextStyle: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: isDark ? ColorTokens.darkSurface : ColorTokens.lightSurface,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }

  /// Modern popup menu theme
  static PopupMenuThemeData _createPopupMenuTheme(bool isDark) {
    return PopupMenuThemeData(
      color: isDark ? ColorTokens.darkSurface : ColorTokens.lightSurface,
      elevation: isDark ? 8 : 2,
      shadowColor: isDark ? Colors.black.withOpacity(0.3) : Colors.grey.shade300,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isDark ? ColorTokens.darkDivider : ColorTokens.lightDivider,
          width: 1,
        ),
      ),
      textStyle: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: isDark ? ColorTokens.darkText : ColorTokens.lightText,
        letterSpacing: -0.1,
      ),
    );
  }

  /// Divider theme
  static DividerThemeData _createDividerTheme(bool isDark) {
    return DividerThemeData(
      color: isDark ? ColorTokens.darkDivider : ColorTokens.lightDivider,
      thickness: 0.5,
      space: 1,
    );
  }

  /// List tile theme
  static ListTileThemeData _createListTileTheme(bool isDark) {
    return ListTileThemeData(
      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      titleTextStyle: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: isDark ? ColorTokens.darkText : ColorTokens.lightText,
        letterSpacing: -0.1,
      ),
      subtitleTextStyle: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: isDark ? ColorTokens.darkTextMuted : ColorTokens.lightTextMuted,
        height: 1.4,
      ),
    );
  }
}
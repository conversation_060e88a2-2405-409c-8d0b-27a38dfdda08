import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/app_config.dart';
import '../../../../core/theme/theme_variants.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../bloc/settings_bloc.dart';

class ThemeVariantSelector extends StatelessWidget {
  const ThemeVariantSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return GlassmorphismContainer(
      forceTransparent: true,
      borderRadius: 20,
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'Select Design Style',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: AppConfig.smallPadding),
          Text(
            'Choose your preferred design aesthetic',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          BlocBuilder<SettingsBloc, SettingsState>(
            builder: (context, state) {
              return Column(
                children: ThemeVariant.values.map((variant) {
                  final isSelected = state.themeVariant == variant;
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () {
                          context.read<SettingsBloc>().add(UpdateThemeVariant(themeVariant: variant));
                          Navigator.pop(context);
                        },
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: isSelected 
                                  ? Theme.of(context).colorScheme.primary
                                  : Colors.grey.withOpacity(0.3),
                              width: isSelected ? 2 : 1,
                            ),
                            borderRadius: BorderRadius.circular(12),
                            color: isSelected 
                                ? Theme.of(context).colorScheme.primary.withOpacity(0.05)
                                : null,
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 50,
                                height: 50,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: isSelected 
                                      ? Theme.of(context).colorScheme.primary
                                      : Colors.grey[100],
                                ),
                                child: Icon(
                                  _getVariantIcon(variant),
                                  color: isSelected 
                                      ? Colors.white 
                                      : Colors.grey[600],
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _getVariantName(variant),
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                        color: isSelected 
                                            ? Theme.of(context).colorScheme.primary
                                            : null,
                                      ),
                                    ),
                                    const SizedBox(height: 2),
                                    Text(
                                      _getVariantDescription(variant),
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (isSelected)
                                Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).colorScheme.primary,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: const Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              );
            },
          ),
          const SizedBox(height: AppConfig.smallPadding),
          // Preview cards showing the design styles
          _buildPreviewSection(context),
        ],
      ),
    );
  }

  Widget _buildPreviewSection(BuildContext context) {
    return BlocBuilder<SettingsBloc, SettingsState>(
      builder: (context, state) {
        return Column(
          children: [
            const Divider(),
            const SizedBox(height: 16),
            Text(
              'Preview',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: ThemeVariant.values.map((variant) {
                final isSelected = state.themeVariant == variant;
                return Expanded(
                  child: Container(
                    margin: EdgeInsets.only(
                      right: variant == ThemeVariant.classic ? 8 : 0,
                      left: variant == ThemeVariant.elegant ? 8 : 0,
                    ),
                    child: Column(
                      children: [
                        Container(
                          height: 60,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isSelected 
                                  ? Theme.of(context).colorScheme.primary
                                  : Colors.grey.withOpacity(0.3),
                              width: isSelected ? 2 : 1,
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(7),
                            child: _buildMiniPreview(context, variant),
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          _getVariantName(variant),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            color: isSelected 
                                ? Theme.of(context).colorScheme.primary
                                : null,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMiniPreview(BuildContext context, ThemeVariant variant) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final theme = ThemeVariants.getTheme(variant, isDark);
    
    return Container(
      color: theme.colorScheme.surface,
      child: Column(
        children: [
          // App bar mini
          Container(
            height: 20,
            color: theme.appBarTheme.backgroundColor,
            child: Center(
              child: Container(
                width: 40,
                height: 2,
                color: theme.appBarTheme.foregroundColor,
              ),
            ),
          ),
          // Content area
          Expanded(
            child: Container(
              color: theme.colorScheme.surface,
              padding: const EdgeInsets.all(4),
              child: Column(
                children: [
                  Container(
                    height: 12,
                    margin: const EdgeInsets.only(bottom: 2),
                    decoration: BoxDecoration(
                      color: theme.cardTheme.color,
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: Center(
                      child: Container(
                        width: 20,
                        height: 1,
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ),
                  Container(
                    height: 12,
                    decoration: BoxDecoration(
                      color: theme.cardTheme.color,
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: Center(
                      child: Container(
                        width: 24,
                        height: 1,
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getVariantName(ThemeVariant variant) {
    switch (variant) {
      case ThemeVariant.classic:
        return 'Classic';
      case ThemeVariant.elegant:
        return 'Elegant';
    }
  }

  String _getVariantDescription(ThemeVariant variant) {
    switch (variant) {
      case ThemeVariant.classic:
        return 'Traditional design with familiar styling';
      case ThemeVariant.elegant:
        return 'Modern, sophisticated design with Inter font';
    }
  }

  IconData _getVariantIcon(ThemeVariant variant) {
    switch (variant) {
      case ThemeVariant.classic:
        return Icons.apps;
      case ThemeVariant.elegant:
        return Icons.auto_awesome;
    }
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:share_plus/share_plus.dart';
import 'package:geolocator/geolocator.dart';
import '../../../../core/app_config.dart';
import '../../../../core/l10n/app_localizations.dart';
import '../bloc/prayer_times_bloc.dart';
import '../widgets/prayer_times_card.dart';
import '../widgets/next_prayer_card.dart';
import '../widgets/prayer_times_list.dart';
import '../widgets/date_header.dart';
import '../widgets/prayer_view_selector.dart';
import '../widgets/export_dialog.dart';
import '../../export/prayer_times_exporter.dart';
import '../bloc/prayer_view_state.dart';
import '../../../settings/presentation/pages/settings_page.dart';
import '../../../settings/presentation/widgets/calculation_method_selector.dart';
import '../../../settings/presentation/bloc/settings_bloc.dart';
import '../../../location/presentation/pages/map_page.dart';
import '../../../location/presentation/bloc/location_bloc.dart';
import '../../../location/domain/entities/location_entity.dart';
import '../../domain/entities/prayer_times.dart';
import '../../../../core/widgets/background_container.dart';
import '../../../notifications/presentation/bloc/notification_bloc.dart';
import '../../../qibla/presentation/pages/qibla_compass_page.dart';
import '../../../qibla/presentation/bloc/qibla_bloc.dart';
import '../../../../core/dependency_injection.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  PrayerViewType _selectedView = PrayerViewType.today;

  @override
  void initState() {
    super.initState();
    // Load prayer times for current location (default to Mecca for now)
    // This is wrapped in a post-frame callback to avoid potential issues
    // if the app starts offline
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialPrayerTimes();
    });
  }

  Future<void> _loadInitialPrayerTimes() async {
    try {
      // Get location from settings or use default
      final settingsState = context.read<SettingsBloc>().state;
      double latitude = settingsState.customLatitude ?? AppConfig.defaultLatitude;
      double longitude = settingsState.customLongitude ?? AppConfig.defaultLongitude;
      
      // If no custom location is set and we have location permission, try to get current location
      if (settingsState.customLatitude == null || settingsState.customLongitude == null) {
        try {
          final permission = await Geolocator.checkPermission();
          if (permission == LocationPermission.always || permission == LocationPermission.whileInUse) {
            final position = await Geolocator.getCurrentPosition(
              desiredAccuracy: LocationAccuracy.high,
              timeLimit: const Duration(seconds: 10),
            );
            
            latitude = position.latitude;
            longitude = position.longitude;
            
            // Save the detected location to settings for future use
            if (mounted) {
              context.read<SettingsBloc>().add(
                UpdateCustomLocation(
                  latitude: latitude,
                  longitude: longitude,
                  locationName: 'Current Location',
                ),
              );
            }
          }
        } catch (e) {
          // If location detection fails, continue with default/saved location
          debugPrint('Location detection failed: $e');
        }
      }
      
      context.read<PrayerTimesBloc>().add(LoadPrayerTimes(
        date: DateTime.now(),
        latitude: latitude,
        longitude: longitude,
        calculationMethod: settingsState.calculationMethod,
      ));
    } catch (e) {
      // If loading fails (e.g., offline), the app should still function
      // Users can manually refresh or set their location
      debugPrint('Initial prayer times loading failed: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return BlocListener<SettingsBloc, SettingsState>(
      listener: (context, settingsState) {
        if (settingsState.customLatitude != null && settingsState.customLongitude != null) {
          context.read<PrayerTimesBloc>().add(LoadPrayerTimes(
            date: DateTime.now(),
            latitude: settingsState.customLatitude!,
            longitude: settingsState.customLongitude!,
            calculationMethod: settingsState.calculationMethod,
          ));
        }
      },
      child: Scaffold(
        appBar: AppBar(
        title: Text(l10n.appTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.explore),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => BlocProvider(
                    create: (context) => DependencyInjection.instance<QiblaBloc>(),
                    child: const QiblaCompassPage(),
                  ),
                ),
              );
            },
            tooltip: l10n.qiblaCompass,
          ),
          // Theme mode toggle switch
          BlocBuilder<SettingsBloc, SettingsState>(
            builder: (context, settingsState) {
              final isDark = settingsState.themeMode == ThemeMode.dark ||
                  (settingsState.themeMode == ThemeMode.system &&
                      MediaQuery.of(context).platformBrightness == Brightness.dark);
              
              return IconButton(
                icon: Icon(
                  isDark ? Icons.light_mode : Icons.dark_mode,
                ),
                onPressed: () {
                  final newThemeMode = isDark ? ThemeMode.light : ThemeMode.dark;
                  context.read<SettingsBloc>().add(UpdateThemeMode(themeMode: newThemeMode));
                },
                tooltip: isDark ? 'Switch to light mode' : 'Switch to dark mode',
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              final settingsState = context.read<SettingsBloc>().state;
              final latitude = settingsState.customLatitude ?? AppConfig.defaultLatitude;
              final longitude = settingsState.customLongitude ?? AppConfig.defaultLongitude;
              
              context.read<PrayerTimesBloc>().add(LoadPrayerTimes(
                date: DateTime.now(),
                latitude: latitude,
                longitude: longitude,
                calculationMethod: settingsState.calculationMethod,
              ));
            },
          ),
          BlocBuilder<PrayerTimesBloc, PrayerTimesState>(
            builder: (context, state) {
              return PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert),
                onSelected: (value) {
                  switch (value) {
                    case 'export_pdf':
                      _exportPDF(context, state);
                      break;
                    case 'export_csv':
                      _exportCSV(context, state);
                      break;
                    case 'share_pdf':
                      _sharePDF(context, state);
                      break;
                    case 'print':
                      _printPrayerTimes(context, state);
                      break;
                    case 'export_pdf_landscape':
                      _exportPDFLandscape(context, state);
                      break;
                    case 'settings':
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SettingsPage(),
                        ),
                      );
                      break;
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'export_pdf',
                    child: Row(
                      children: [
                        Icon(
                          Icons.picture_as_pdf,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Export PDF',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'export_csv',
                    child: Row(
                      children: [
                        Icon(
                          Icons.table_chart,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Export CSV',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'share_pdf',
                    child: Row(
                      children: [
                        Icon(
                          Icons.share,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Share PDF',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'print',
                    child: Row(
                      children: [
                        Icon(
                          Icons.print,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Print',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'export_pdf_landscape',
                    child: Row(
                      children: [
                        Icon(
                          Icons.crop_rotate,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'PDF Landscape',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const PopupMenuDivider(),
                  PopupMenuItem(
                    value: 'settings',
                    child: Row(
                      children: [
                        Icon(
                          Icons.settings,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Settings',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: BackgroundContainer(
        child: BlocBuilder<PrayerTimesBloc, PrayerTimesState>(
          builder: (context, state) {
          if (state is PrayerTimesLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          } else if (state is PrayerTimesError) {
            // Determine if this is likely an offline error
            final isLikelyOffline = state.message.toLowerCase().contains('network') ||
                                  state.message.toLowerCase().contains('connection') ||
                                  state.message.toLowerCase().contains('internet') ||
                                  state.message.toLowerCase().contains('failed');
            
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(AppConfig.defaultPadding),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      isLikelyOffline ? Icons.wifi_off : Icons.error_outline,
                      size: 64,
                      color: isLikelyOffline 
                          ? Theme.of(context).colorScheme.onSurfaceVariant
                          : Theme.of(context).colorScheme.error,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      isLikelyOffline 
                          ? l10n.offlineMode ?? 'Offline Mode'
                          : 'Error loading prayer times',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      isLikelyOffline 
                          ? 'Prayer times will be available once you\'re connected to the internet or have cached data.'
                          : state.message,
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    if (isLikelyOffline) ...[
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const SettingsPage(),
                            ),
                          );
                        },
                        icon: const Icon(Icons.settings),
                        label: const Text('Open Settings'),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'You can set your location and preferences in settings.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ] else ...[
                      ElevatedButton(
                        onPressed: () {
                          final settingsState = context.read<SettingsBloc>().state;
                          final latitude = settingsState.customLatitude ?? AppConfig.defaultLatitude;
                          final longitude = settingsState.customLongitude ?? AppConfig.defaultLongitude;
                          
                          context.read<PrayerTimesBloc>().add(LoadPrayerTimes(
                            date: DateTime.now(),
                            latitude: latitude,
                            longitude: longitude,
                            calculationMethod: settingsState.calculationMethod,
                          ));
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  ],
                ),
              ),
            );
          } else if (state is PrayerTimesLoaded || state is PrayerTimesRefreshing) {
            final prayerTimes = state is PrayerTimesLoaded 
                ? state.prayerTimes 
                : (state as PrayerTimesRefreshing).prayerTimes;
            
            return RefreshIndicator(
              onRefresh: () async {
                final settingsState = context.read<SettingsBloc>().state;
                final latitude = settingsState.customLatitude ?? AppConfig.defaultLatitude;
                final longitude = settingsState.customLongitude ?? AppConfig.defaultLongitude;
                
                context.read<PrayerTimesBloc>().add(LoadPrayerTimes(
                  date: DateTime.now(),
                  latitude: latitude,
                  longitude: longitude,
                  calculationMethod: settingsState.calculationMethod,
                ));
                // Wait a bit for the request to complete
                await Future.delayed(const Duration(milliseconds: 500));
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(AppConfig.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Date Header with Hijri date
                    DateHeader(date: DateTime.now()),

                    // View Selector
                    PrayerViewSelector(
                      selectedView: _selectedView,
                      onViewChanged: (viewType) {
                        setState(() {
                          _selectedView = viewType;
                        });
                      },
                    ),

                    // Next Prayer Card
                    NextPrayerCard(prayerTimes: prayerTimes),
                    const SizedBox(height: AppConfig.defaultPadding),
                    
                    // Location Info
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(AppConfig.defaultPadding),
                        child: Row(
                          children: [
                            Icon(
                              Icons.location_on,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    prayerTimes.city.isNotEmpty 
                                        ? prayerTimes.city 
                                        : 'Current Location',
                                    style: Theme.of(context).textTheme.titleMedium,
                                  ),
                                  Text(
                                    '${prayerTimes.latitude.toStringAsFixed(4)}, ${prayerTimes.longitude.toStringAsFixed(4)}',
                                    style: Theme.of(context).textTheme.bodySmall,
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.map),
                              onPressed: () async {
                                final result = await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => BlocProvider.value(
                                      value: context.read<LocationBloc>(),
                                      child: MapPage(
                                        initialLocation: state is PrayerTimesLoaded
                                            ? LocationEntity(
                                                latitude: state.latitude,
                                                longitude: state.longitude,
                                                city: prayerTimes.city,
                                                country: prayerTimes.country,
                                                timezone: prayerTimes.timezone,
                                              )
                                            : null,
                                      ),
                                    ),
                                  ),
                                );

                                // Handle the result if needed
                                if (result != null) {
                                  // Location was selected, prayer times will be updated automatically
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: AppConfig.defaultPadding),
                    
                    // Prayer Times List
                    Text(
                      'Today\'s Prayer Times',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: AppConfig.smallPadding),
                    PrayerTimesList(prayerTimes: prayerTimes),
                    
                    // Calculation Method Info
                    const SizedBox(height: AppConfig.defaultPadding),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(AppConfig.defaultPadding),
                        child: Row(
                          children: [
                            Icon(
                              Icons.calculate,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    l10n.calculationMethod,
                                    style: Theme.of(context).textTheme.titleMedium,
                                  ),
                                  Text(
                                    AppConfig.calculationMethods[prayerTimes.calculationMethod] ?? 
                                        'Unknown Method',
                                    style: Theme.of(context).textTheme.bodySmall,
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.edit),
                              onPressed: () {
                                showModalBottomSheet(
                                  context: context,
                                  builder: (context) => const CalculationMethodSelector(),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
          
          // Show initial welcome state when no prayer times are loaded
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(AppConfig.defaultPadding),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.access_time,
                    size: 64,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Welcome to AlFalah',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Your prayer times companion',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            final settingsState = context.read<SettingsBloc>().state;
                            final latitude = settingsState.customLatitude ?? AppConfig.defaultLatitude;
                            final longitude = settingsState.customLongitude ?? AppConfig.defaultLongitude;
                            
                            context.read<PrayerTimesBloc>().add(LoadPrayerTimes(
                              date: DateTime.now(),
                              latitude: latitude,
                              longitude: longitude,
                              calculationMethod: settingsState.calculationMethod,
                            ));
                          },
                          icon: const Icon(Icons.refresh),
                          label: const Text('Load Prayer Times'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const SettingsPage(),
                              ),
                            );
                          },
                          icon: const Icon(Icons.location_on),
                          label: const Text('Set Location'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Tip: Set your location for accurate prayer times',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _exportPDF(BuildContext context, PrayerTimesState state) {
    if (state is PrayerTimesLoaded) {
      final settingsState = context.read<SettingsBloc>().state;
      PrayerTimesExporter.exportToPdf(
        context: context,
        prayerTimesList: [state.prayerTimes],
        viewType: 'today',
        cityName: state.prayerTimes.city.isNotEmpty ? state.prayerTimes.city : 'Current Location',
        hijriOffset: settingsState.hijriOffset,
        landscape: false,
      );
    } else {
      _showLoadingError(context);
    }
  }

  void _exportCSV(BuildContext context, PrayerTimesState state) {
    if (state is PrayerTimesLoaded) {
      final settingsState = context.read<SettingsBloc>().state;
      PrayerTimesExporter.exportToCsv(
        context: context,
        prayerTimesList: [state.prayerTimes],
        viewType: 'today',
        cityName: state.prayerTimes.city.isNotEmpty ? state.prayerTimes.city : 'Current Location',
        hijriOffset: settingsState.hijriOffset,
      );
    } else {
      _showLoadingError(context);
    }
  }

  void _sharePDF(BuildContext context, PrayerTimesState state) {
    if (state is PrayerTimesLoaded) {
      final settingsState = context.read<SettingsBloc>().state;
      PrayerTimesExporter.sharePdf(
        context: context,
        prayerTimesList: [state.prayerTimes],
        viewType: 'today',
        cityName: state.prayerTimes.city.isNotEmpty ? state.prayerTimes.city : 'Current Location',
        hijriOffset: settingsState.hijriOffset,
      );
    } else {
      _showLoadingError(context);
    }
  }

  void _printPrayerTimes(BuildContext context, PrayerTimesState state) {
    if (state is PrayerTimesLoaded) {
      final settingsState = context.read<SettingsBloc>().state;
      PrayerTimesExporter.printPrayerTimes(
        context: context,
        prayerTimesList: [state.prayerTimes],
        viewType: 'today',
        cityName: state.prayerTimes.city.isNotEmpty ? state.prayerTimes.city : 'Current Location',
        hijriOffset: settingsState.hijriOffset,
        landscape: false,
      );
    } else {
      _showLoadingError(context);
    }
  }

  void _exportPDFLandscape(BuildContext context, PrayerTimesState state) {
    if (state is PrayerTimesLoaded) {
      final settingsState = context.read<SettingsBloc>().state;
      PrayerTimesExporter.exportToPdf(
        context: context,
        prayerTimesList: [state.prayerTimes],
        viewType: 'today',
        cityName: state.prayerTimes.city.isNotEmpty ? state.prayerTimes.city : 'Current Location',
        hijriOffset: settingsState.hijriOffset,
        landscape: true,
      );
    } else {
      _showLoadingError(context);
    }
  }

  void _shareApp(BuildContext context) {
    const appName = 'AlFalah';
    const appDescription = 'Accurate Islamic prayer times with offline support';
    const playStoreUrl = 'https://play.google.com/store/apps/details?id=com.eddarsglobal.alfalah';
    const appStoreUrl = 'https://apps.apple.com/app/alfalah-prayer-times/id123456789';
    
    final shareText = '''Check out $appName - $appDescription

🕌 Features:
• Accurate prayer times with multiple calculation methods
• Offline support for uninterrupted use
• Beautiful themes and dark mode
• Hijri calendar with local adjustments
• Export and share prayer schedules
• Multi-language support

Download now:
Android: $playStoreUrl
iOS: $appStoreUrl

#PrayerTimes #Islam #AlFalah''';
    
    Share.share(
      shareText,
      subject: 'AlFalah - Islamic Prayer Times App',
    );
  }

  void _showLoadingError(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Please wait for prayer times to load'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}

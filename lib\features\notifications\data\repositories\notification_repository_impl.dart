import 'package:dartz/dartz.dart';
import 'package:get_it/get_it.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';
import '../../domain/repositories/notification_repository.dart';
import '../../../prayer_times/domain/entities/prayer_times.dart';
import '../datasources/notification_datasource.dart';
import '../datasources/notification_settings_datasource.dart';

class NotificationRepositoryImpl implements NotificationRepository {
  final NotificationDataSource dataSource;

  NotificationRepositoryImpl({required this.dataSource});

  @override
  Future<Either<Failure, void>> schedulePrayerNotifications(PrayerTimes prayerTimes, bool adhanEnabled) async {
    try {
      await dataSource.schedulePrayerNotifications(prayerTimes, adhanEnabled);
      return const Right(null);
    } on NotificationException catch (e) {
      return Left(NotificationFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> scheduleSelectivePrayerNotifications(PrayerTimes prayerTimes, bool adhanEnabled) async {
    try {
      final settingsDataSource = GetIt.instance<NotificationSettingsDatasource>();
      await dataSource.scheduleSelectivePrayerNotifications(prayerTimes, adhanEnabled, settingsDataSource);
      return const Right(null);
    } on NotificationException catch (e) {
      return Left(NotificationFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> cancelAllNotifications() async {
    try {
      await dataSource.cancelAllNotifications();
      return const Right(null);
    } on NotificationException catch (e) {
      return Left(NotificationFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> requestPermissions() async {
    try {
      final granted = await dataSource.requestPermissions();
      return Right(granted);
    } on NotificationException catch (e) {
      return Left(NotificationFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }
}

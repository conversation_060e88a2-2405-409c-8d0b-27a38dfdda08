import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../prayer_times/domain/entities/prayer_times.dart';
import '../repositories/notification_repository.dart';

class SchedulePrayerNotifications implements UseCase<void, SchedulePrayerNotificationsParams> {
  final NotificationRepository repository;

  SchedulePrayerNotifications(this.repository);

  @override
  Future<Either<Failure, void>> call(SchedulePrayerNotificationsParams params) async {
    return await repository.schedulePrayerNotifications(params.prayerTimes, params.adhanEnabled);
  }

  Future<Either<Failure, void>> scheduleSelective(PrayerTimes prayerTimes, bool adhanEnabled) async {
    return await repository.scheduleSelectivePrayerNotifications(prayerTimes, adhanEnabled);
  }

  Future<Either<Failure, void>> cancelAll() async {
    return await repository.cancelAllNotifications();
  }
}

class SchedulePrayerNotificationsParams extends Equatable {
  final PrayerTimes prayerTimes;
  final bool adhanEnabled;

  const SchedulePrayerNotificationsParams({
    required this.prayerTimes,
    required this.adhanEnabled,
  });

  @override
  List<Object> get props => [prayerTimes, adhanEnabled];
}

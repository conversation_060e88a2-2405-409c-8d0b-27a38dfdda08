import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  // Prayer names
  String get fajr {
    switch (locale.languageCode) {
      case 'de':
        return 'Fajr';
      case 'hr':
        return 'Fajr';
      case 'fr':
        return 'Fajr';
      case 'ar':
        return 'الفجر';
      case 'es':
        return 'Fajr';
      default:
        return 'Fajr';
    }
  }

  String get dhuhr {
    switch (locale.languageCode) {
      case 'de':
        return 'Dhuhr';
      case 'hr':
        return 'Dhuhr';
      case 'fr':
        return 'Dhuhr';
      case 'ar':
        return 'الظهر';
      case 'es':
        return 'Dhuhr';
      default:
        return 'Dhuhr';
    }
  }

  String get asr {
    switch (locale.languageCode) {
      case 'de':
        return 'Asr';
      case 'hr':
        return 'Asr';
      case 'fr':
        return 'Asr';
      case 'ar':
        return 'العصر';
      case 'es':
        return 'Asr';
      default:
        return 'Asr';
    }
  }

  String get maghrib {
    switch (locale.languageCode) {
      case 'de':
        return 'Maghrib';
      case 'hr':
        return 'Maghrib';
      case 'fr':
        return 'Maghrib';
      case 'ar':
        return 'المغرب';
      case 'es':
        return 'Maghrib';
      default:
        return 'Maghrib';
    }
  }

  String get isha {
    switch (locale.languageCode) {
      case 'de':
        return 'Isha';
      case 'hr':
        return 'Isha';
      case 'fr':
        return 'Isha';
      case 'ar':
        return 'العشاء';
      case 'es':
        return 'Isha';
      default:
        return 'Isha';
    }
  }

  // Common UI strings
  String get appTitle {
    switch (locale.languageCode) {
      case 'de':
        return 'AlFalah - Gebetszeiten';
      case 'hr':
        return 'AlFalah - Vremena molitve';
      case 'fr':
        return 'AlFalah - Heures de prière';
      case 'ar':
        return 'جبت - أوقات الصلاة';
      case 'es':
        return 'AlFalah - Horarios de Oración';
      case 'zh':
        return 'AlFalah - 祈祷时间';
      case 'ru':
        return 'AlFalah - Время молитвы';
      case 'ms':
        return 'AlFalah - Waktu Solat';
      case 'id':
        return 'AlFalah - Waktu Shalat';
      case 'hi':
        return 'AlFalah - नमाज़ का समय';
      default:
        return 'AlFalah - Prayer Times';
    }
  }

  String get nextPrayer {
    switch (locale.languageCode) {
      case 'de':
        return 'Nächstes Gebet';
      case 'hr':
        return 'Sljedeća molitva';
      case 'fr':
        return 'Prochaine prière';
      case 'ar':
        return 'الصلاة التالية';
      case 'es':
        return 'Próxima Oración';
      case 'zh':
        return '下一个祈祷';
      case 'ru':
        return 'Следующая молитва';
      case 'ms':
        return 'Solat Seterusnya';
      case 'id':
        return 'Shalat Berikutnya';
      case 'hi':
        return 'अगली नमाज़';
      default:
        return 'Next Prayer';
    }
  }

  String get timeRemaining {
    switch (locale.languageCode) {
      case 'de':
        return 'Verbleibende Zeit';
      case 'hr':
        return 'Preostalo vrijeme';
      case 'fr':
        return 'Temps restant';
      case 'ar':
        return 'الوقت المتبقي';
      case 'es':
        return 'Tiempo Restante';
      default:
        return 'Time Remaining';
    }
  }

  String get settings {
    switch (locale.languageCode) {
      case 'de':
        return 'Einstellungen';
      case 'hr':
        return 'Postavke';
      case 'fr':
        return 'Paramètres';
      case 'ar':
        return 'الإعدادات';
      case 'es':
        return 'Configuración';
      case 'zh':
        return '设置';
      case 'ru':
        return 'Настройки';
      case 'ms':
        return 'Tetapan';
      case 'id':
        return 'Pengaturan';
      case 'hi':
        return 'सेटिंग्स';
      default:
        return 'Settings';
    }
  }

  // Help & Support
  String get helpSupport {
    switch (locale.languageCode) {
      case 'de':
        return 'Hilfe & Support';
      case 'hr':
        return 'Pomoć i podrška';
      case 'fr':
        return 'Aide et support';
      case 'ar':
        return 'المساعدة والدعم';
      case 'es':
        return 'Ayuda y Soporte';
      default:
        return 'Help & Support';
    }
  }

  String get faq {
    switch (locale.languageCode) {
      case 'de':
        return 'Häufig gestellte Fragen';
      case 'hr':
        return 'Često postavljana pitanja';
      case 'fr':
        return 'Questions fréquentes';
      case 'ar':
        return 'الأسئلة الشائعة';
      case 'es':
        return 'Preguntas Frecuentes';
      default:
        return 'Frequently Asked Questions';
    }
  }

  String get contact {
    switch (locale.languageCode) {
      case 'de':
        return 'Kontakt';
      case 'hr':
        return 'Kontakt';
      case 'fr':
        return 'Contact';
      case 'ar':
        return 'اتصل بنا';
      case 'es':
        return 'Contacto';
      default:
        return 'Contact';
    }
  }

  String get reportIssue {
    switch (locale.languageCode) {
      case 'de':
        return 'Problem melden';
      case 'hr':
        return 'Prijavi problem';
      case 'fr':
        return 'Signaler un problème';
      case 'ar':
        return 'الإبلاغ عن مشكلة';
      case 'es':
        return 'Reportar Problema';
      default:
        return 'Report Issue';
    }
  }

  String get legal {
    switch (locale.languageCode) {
      case 'de':
        return 'Rechtliches';
      case 'hr':
        return 'Pravno';
      case 'fr':
        return 'Légal';
      case 'ar':
        return 'قانوني';
      case 'es':
        return 'Legal';
      default:
        return 'Legal';
    }
  }

  String get appInfo {
    switch (locale.languageCode) {
      case 'de':
        return 'App-Informationen';
      case 'hr':
        return 'Informacije o aplikaciji';
      case 'fr':
        return 'Informations sur l\'app';
      case 'ar':
        return 'معلومات التطبيق';
      case 'es':
        return 'Información de la App';
      default:
        return 'App Information';
    }
  }

  // Export & Print
  String get exportPdf {
    switch (locale.languageCode) {
      case 'de':
        return 'Als PDF exportieren';
      case 'hr':
        return 'Izvezi kao PDF';
      case 'fr':
        return 'Exporter en PDF';
      case 'ar':
        return 'تصدير كـ PDF';
      case 'es':
        return 'Exportar como PDF';
      default:
        return 'Export as PDF';
    }
  }

  String get exportCsv {
    switch (locale.languageCode) {
      case 'de':
        return 'Als CSV exportieren';
      case 'hr':
        return 'Izvezi kao CSV';
      case 'fr':
        return 'Exporter en CSV';
      case 'ar':
        return 'تصدير كـ CSV';
      case 'es':
        return 'Exportar como CSV';
      default:
        return 'Export as CSV';
    }
  }

  String get print {
    switch (locale.languageCode) {
      case 'de':
        return 'Drucken';
      case 'hr':
        return 'Ispiši';
      case 'fr':
        return 'Imprimer';
      case 'ar':
        return 'طباعة';
      case 'es':
        return 'Imprimir';
      default:
        return 'Print';
    }
  }

  String get share {
    switch (locale.languageCode) {
      case 'de':
        return 'Teilen';
      case 'hr':
        return 'Podijeli';
      case 'fr':
        return 'Partager';
      case 'ar':
        return 'مشاركة';
      case 'es':
        return 'Compartir';
      default:
        return 'Share';
    }
  }

  String get landscape {
    switch (locale.languageCode) {
      case 'de':
        return 'Querformat';
      case 'hr':
        return 'Vodoravno';
      case 'fr':
        return 'Paysage';
      case 'ar':
        return 'أفقي';
      case 'es':
        return 'Horizontal';
      default:
        return 'Landscape';
    }
  }

  String get portrait {
    switch (locale.languageCode) {
      case 'de':
        return 'Hochformat';
      case 'hr':
        return 'Okomito';
      case 'fr':
        return 'Portrait';
      case 'ar':
        return 'عمودي';
      case 'es':
        return 'Vertical';
      default:
        return 'Portrait';
    }
  }

  // FAQ Questions and Answers
  String get faqPrayerTimesAccuracy {
    switch (locale.languageCode) {
      case 'de':
        return 'Wie genau sind die Gebetszeiten?';
      case 'hr':
        return 'Koliko su točna vremena molitve?';
      case 'fr':
        return 'Quelle est la précision des heures de prière?';
      case 'ar':
        return 'ما مدى دقة أوقات الصلاة؟';
      case 'es':
        return '¿Qué tan precisos son los horarios de oración?';
      default:
        return 'How accurate are the prayer times?';
    }
  }

  String get faqPrayerTimesAccuracyAnswer {
    switch (locale.languageCode) {
      case 'de':
        return 'Die Gebetszeiten werden mit der Umm al-Qura Methode berechnet, die von Saudi-Arabien offiziell verwendet wird. Die Genauigkeit liegt bei ±1-2 Minuten.';
      case 'hr':
        return 'Vremena molitve se izračunavaju pomoću Umm al-Qura metode koju službeno koristi Saudijska Arabija. Točnost je ±1-2 minute.';
      case 'fr':
        return 'Les heures de prière sont calculées avec la méthode Umm al-Qura officiellement utilisée par l\'Arabie Saoudite. La précision est de ±1-2 minutes.';
      case 'ar':
        return 'يتم حساب أوقات الصلاة باستخدام طريقة أم القرى المستخدمة رسمياً في المملكة العربية السعودية. الدقة ±1-2 دقيقة.';
      case 'es':
        return 'Los horarios de oración se calculan utilizando el método Umm al-Qura oficialmente usado por Arabia Saudí. La precisión es de ±1-2 minutos.';
      default:
        return 'Prayer times are calculated using the Umm al-Qura method officially used by Saudi Arabia. Accuracy is ±1-2 minutes.';
    }
  }

  String get faqHijriDateOffset {
    switch (locale.languageCode) {
      case 'de':
        return 'Warum ist das Hijri-Datum anders als erwartet?';
      case 'hr':
        return 'Zašto se hidžrijski datum razlikuje od očekivanog?';
      case 'fr':
        return 'Pourquoi la date hijri est-elle différente de celle attendue?';
      case 'ar':
        return 'لماذا يختلف التاريخ الهجري عن المتوقع؟';
      case 'es':
        return '¿Por qué la fecha Hijri es diferente a la esperada?';
      default:
        return 'Why is the Hijri date different than expected?';
    }
  }

  String get faqHijriDateOffsetAnswer {
    switch (locale.languageCode) {
      case 'de':
        return 'Das Hijri-Datum basiert auf der Umm al-Qura Tabelle. Lokale Mondsichtungen können abweichen. Sie können den Offset in den Einstellungen anpassen (-3 bis +3 Tage).';
      case 'hr':
        return 'Hidžrijski datum se temelji na Umm al-Qura tablici. Lokalna promatranja mjeseca mogu se razlikovati. Možete prilagoditi pomak u postavkama (-3 do +3 dana).';
      case 'fr':
        return 'La date hijri est basée sur le tableau Umm al-Qura. Les observations locales de la lune peuvent différer. Vous pouvez ajuster le décalage dans les paramètres (-3 à +3 jours).';
      case 'ar':
        return 'يعتمد التاريخ الهجري على جدول أم القرى. قد تختلف الرؤية المحلية للهلال. يمكنك تعديل الإزاحة في الإعدادات (-3 إلى +3 أيام).';
      case 'es':
        return 'La fecha Hijri se basa en la tabla Umm al-Qura. Las observaciones locales de la luna pueden diferir. Puedes ajustar el desplazamiento en configuración (-3 a +3 días).';
      default:
        return 'The Hijri date is based on the Umm al-Qura table. Local moon sightings may differ. You can adjust the offset in settings (-3 to +3 days).';
    }
  }

  String get faqNotificationsNotWorking {
    switch (locale.languageCode) {
      case 'de':
        return 'Warum funktionieren die Benachrichtigungen nicht?';
      case 'hr':
        return 'Zašto obavijesti ne rade?';
      case 'fr':
        return 'Pourquoi les notifications ne fonctionnent-elles pas?';
      case 'ar':
        return 'لماذا لا تعمل الإشعارات؟';
      case 'es':
        return '¿Por qué no funcionan las notificaciones?';
      default:
        return 'Why are notifications not working?';
    }
  }

  String get faqNotificationsNotWorkingAnswer {
    switch (locale.languageCode) {
      case 'de':
        return 'Stellen Sie sicher, dass Benachrichtigungen in den App-Einstellungen und Systemeinstellungen aktiviert sind. Überprüfen Sie auch die Batterie-Optimierung.';
      case 'hr':
        return 'Provjerite jesu li obavijesti omogućene u postavkama aplikacije i sustava. Također provjerite optimizaciju baterije.';
      case 'fr':
        return 'Assurez-vous que les notifications sont activées dans les paramètres de l\'app et du système. Vérifiez aussi l\'optimisation de la batterie.';
      case 'ar':
        return 'تأكد من تفعيل الإشعارات في إعدادات التطبيق والنظام. تحقق أيضاً من تحسين البطارية.';
      case 'es':
        return 'Asegúrate de que las notificaciones estén habilitadas en la configuración de la app y del sistema. También verifica la optimización de la batería.';
      default:
        return 'Make sure notifications are enabled in app settings and system settings. Also check battery optimization.';
    }
  }

  String get faqOfflineMode {
    switch (locale.languageCode) {
      case 'de':
        return 'Funktioniert die App offline?';
      case 'hr':
        return 'Radi li aplikacija offline?';
      case 'fr':
        return 'L\'app fonctionne-t-elle hors ligne?';
      case 'ar':
        return 'هل يعمل التطبيق بدون إنترنت؟';
      case 'es':
        return '¿Funciona la app sin conexión?';
      default:
        return 'Does the app work offline?';
    }
  }

  String get faqOfflineModeAnswer {
    switch (locale.languageCode) {
      case 'de':
        return 'Ja, nach dem ersten Laden werden die Gebetszeiten lokal gespeichert. Sie können auch Zeiten im Voraus laden.';
      case 'hr':
        return 'Da, nakon prvog učitavanja vremena molitve se lokalno pohranjuju. Također možete unaprijed učitati vremena.';
      case 'fr':
        return 'Oui, après le premier chargement, les heures de prière sont stockées localement. Vous pouvez aussi précharger les heures.';
      case 'ar':
        return 'نعم، بعد التحميل الأول يتم حفظ أوقات الصلاة محلياً. يمكنك أيضاً تحميل الأوقات مسبقاً.';
      case 'es':
        return 'Sí, después de la primera carga, los horarios de oración se almacenan localmente. También puedes precargar horarios con anticipación.';
      default:
        return 'Yes, after initial loading, prayer times are stored locally. You can also preload times in advance.';
    }
  }

  String get faqLocationAccuracy {
    switch (locale.languageCode) {
      case 'de':
        return 'Wie wichtig ist die genaue Standortangabe?';
      case 'hr':
        return 'Koliko je važna točna lokacija?';
      case 'fr':
        return 'Quelle est l\'importance de la localisation précise?';
      case 'ar':
        return 'ما أهمية دقة الموقع؟';
      case 'es':
        return '¿Qué tan importante es la ubicación precisa?';
      default:
        return 'How important is accurate location?';
    }
  }

  String get faqLocationAccuracyAnswer {
    switch (locale.languageCode) {
      case 'de':
        return 'Die Gebetszeiten variieren je nach geografischer Lage. Eine Abweichung von 50km kann die Zeiten um 2-4 Minuten ändern.';
      case 'hr':
        return 'Vremena molitve variraju ovisno o geografskom položaju. Odstupanje od 50km može promijeniti vremena za 2-4 minute.';
      case 'fr':
        return 'Les heures de prière varient selon la position géographique. Un écart de 50km peut changer les heures de 2-4 minutes.';
      case 'ar':
        return 'تختلف أوقات الصلاة حسب الموقع الجغرافي. انحراف 50 كم قد يغير الأوقات بـ 2-4 دقائق.';
      case 'es':
        return 'Los horarios de oración varían según la ubicación geográfica. Una desviación de 50km puede cambiar los horarios en 2-4 دقائق.';
      default:
        return 'Prayer times vary by geographic location. A 50km deviation can change times by 2-4 minutes.';
    }
  }

  // Contact & Support
  String get contactEmail {
    switch (locale.languageCode) {
      case 'de':
        return 'E-Mail senden';
      case 'hr':
        return 'Pošalji e-mail';
      case 'fr':
        return 'Envoyer un e-mail';
      case 'ar':
        return 'إرسال بريد إلكتروني';
      case 'es':
        return 'Enviar Email';
      default:
        return 'Send Email';
    }
  }

  String get contactEmailDescription {
    switch (locale.languageCode) {
      case 'de':
        return 'Kontaktieren Sie unser Support-Team';
      case 'hr':
        return 'Kontaktirajte naš tim za podršku';
      case 'fr':
        return 'Contactez notre équipe de support';
      case 'ar':
        return 'اتصل بفريق الدعم';
      default:
        return 'Contact our support team';
    }
  }

  String get website {
    switch (locale.languageCode) {
      case 'de':
        return 'Website besuchen';
      case 'hr':
        return 'Posjeti web stranicu';
      case 'fr':
        return 'Visiter le site web';
      case 'ar':
        return 'زيارة الموقع';
      case 'es':
        return 'Visitar Sitio Web';
      default:
        return 'Visit Website';
    }
  }

  String get reportBug {
    switch (locale.languageCode) {
      case 'de':
        return 'Fehler melden';
      case 'hr':
        return 'Prijavi grešku';
      case 'fr':
        return 'Signaler un bug';
      case 'ar':
        return 'الإبلاغ عن خطأ';
      case 'es':
        return 'Reportar Error';
      default:
        return 'Report Bug';
    }
  }

  String get reportBugDescription {
    switch (locale.languageCode) {
      case 'de':
        return 'Teilen Sie technische Probleme mit uns';
      case 'hr':
        return 'Podijelite tehničke probleme s nama';
      case 'fr':
        return 'Partagez les problèmes techniques avec nous';
      case 'ar':
        return 'شارك المشاكل التقنية معنا';
      case 'es':
        return 'Comparte problemas técnicos con nosotros';
      default:
        return 'Share technical issues with us';
    }
  }

  String get sendFeedback {
    switch (locale.languageCode) {
      case 'de':
        return 'Feedback senden';
      case 'hr':
        return 'Pošalji povratnu informaciju';
      case 'fr':
        return 'Envoyer des commentaires';
      case 'ar':
        return 'إرسال ملاحظات';
      case 'es':
        return 'Enviar Comentarios';
      default:
        return 'Send Feedback';
    }
  }

  String get sendFeedbackDescription {
    switch (locale.languageCode) {
      case 'de':
        return 'Teilen Sie Ihre Ideen und Vorschläge';
      case 'hr':
        return 'Podijelite svoje ideje i prijedloge';
      case 'fr':
        return 'Partagez vos idées et suggestions';
      case 'ar':
        return 'شارك أفكارك واقتراحاتك';
      case 'es':
        return 'Comparte tus ideas y sugerencias';
      default:
        return 'Share your ideas and suggestions';
    }
  }

  String get privacyPolicy {
    switch (locale.languageCode) {
      case 'de':
        return 'Datenschutzerklärung';
      case 'hr':
        return 'Pravila privatnosti';
      case 'fr':
        return 'Politique de confidentialité';
      case 'ar':
        return 'سياسة الخصوصية';
      case 'es':
        return 'Política de Privacidad';
      default:
        return 'Privacy Policy';
    }
  }

  String get termsOfService {
    switch (locale.languageCode) {
      case 'de':
        return 'Nutzungsbedingungen';
      case 'hr':
        return 'Uvjeti korištenja';
      case 'fr':
        return 'Conditions d\'utilisation';
      case 'ar':
        return 'شروط الخدمة';
      case 'es':
        return 'Términos de Servicio';
      default:
        return 'Terms of Service';
    }
  }

  String get licenses {
    switch (locale.languageCode) {
      case 'de':
        return 'Lizenzen';
      case 'hr':
        return 'Licence';
      case 'fr':
        return 'Licences';
      case 'ar':
        return 'التراخيص';
      case 'es':
        return 'Licencias';
      default:
        return 'Licenses';
    }
  }

  String get appName {
    switch (locale.languageCode) {
      case 'de':
        return 'AlFalah';
      case 'hr':
        return 'AlFalah';
      case 'fr':
        return 'AlFalah';
      case 'ar':
        return 'الفلاح';
      case 'es':
        return 'AlFalah';
      default:
        return 'AlFalah';
    }
  }

  String get packageName {
    switch (locale.languageCode) {
      case 'de':
        return 'Paket-Name';
      case 'hr':
        return 'Ime paketa';
      case 'fr':
        return 'Nom du package';
      case 'ar':
        return 'اسم الحزمة';
      case 'es':
        return 'Nombre del Paquete';
      default:
        return 'Package Name';
    }
  }

  String get platform {
    switch (locale.languageCode) {
      case 'de':
        return 'Plattform';
      case 'hr':
        return 'Platforma';
      case 'fr':
        return 'Plateforme';
      case 'ar':
        return 'المنصة';
      case 'es':
        return 'Plataforma';
      default:
        return 'Platform';
    }
  }

  String get buildDate {
    switch (locale.languageCode) {
      case 'de':
        return 'Build-Datum';
      case 'hr':
        return 'Datum izgradnje';
      case 'fr':
        return 'Date de build';
      case 'ar':
        return 'تاريخ البناء';
      case 'es':
        return 'Fecha de Compilación';
      default:
        return 'Build Date';
    }
  }

  String get noEmailClient {
    switch (locale.languageCode) {
      case 'de':
        return 'Kein E-Mail-Client';
      case 'hr':
        return 'Nema e-mail klijenta';
      case 'fr':
        return 'Pas de client e-mail';
      case 'ar':
        return 'لا يوجد عميل بريد إلكتروني';
      case 'es':
        return 'Sin Cliente de Email';
      default:
        return 'No Email Client';
    }
  }

  String get noEmailClientDescription {
    switch (locale.languageCode) {
      case 'de':
        return 'Kein E-Mail-Client gefunden. Bitte kopieren Sie die E-Mail-Adresse:';
      case 'hr':
        return 'Nije pronađen e-mail klijent. Molimo kopirajte e-mail adresu:';
      case 'fr':
        return 'Aucun client e-mail trouvé. Veuillez copier l\'adresse e-mail:';
      case 'ar':
        return 'لم يتم العثور على عميل بريد إلكتروني. يرجى نسخ عنوان البريد الإلكتروني:';
      case 'es':
        return 'No se encontró cliente de email. Por favor copia la dirección de email:';
      default:
        return 'No email client found. Please copy the email address:';
    }
  }

  String get copyEmail {
    switch (locale.languageCode) {
      case 'de':
        return 'E-Mail kopieren';
      case 'hr':
        return 'Kopiraj e-mail';
      case 'fr':
        return 'Copier l\'e-mail';
      case 'ar':
        return 'نسخ البريد الإلكتروني';
      case 'es':
        return 'Copiar Email';
      default:
        return 'Copy Email';
    }
  }

  String get emailCopied {
    switch (locale.languageCode) {
      case 'de':
        return 'E-Mail-Adresse kopiert';
      case 'hr':
        return 'E-mail adresa kopirana';
      case 'fr':
        return 'Adresse e-mail copiée';
      case 'ar':
        return 'تم نسخ عنوان البريد الإلكتروني';
      case 'es':
        return 'Dirección de email copiada';
      default:
        return 'Email address copied';
    }
  }

  String get close {
    switch (locale.languageCode) {
      case 'de':
        return 'Schließen';
      case 'hr':
        return 'Zatvori';
      case 'fr':
        return 'Fermer';
      case 'ar':
        return 'إغلاق';
      case 'es':
        return 'Cerrar';
      default:
        return 'Close';
    }
  }

  // Export & Share strings
  String get exportShare {
    switch (locale.languageCode) {
      case 'de':
        return 'Exportieren & Teilen';
      case 'hr':
        return 'Izvoz i dijeljenje';
      case 'fr':
        return 'Exporter et partager';
      case 'ar':
        return 'تصدير ومشاركة';
      case 'es':
        return 'Exportar y Compartir';
      default:
        return 'Export & Share';
    }
  }

  String exportSubtitle(int days) {
    switch (locale.languageCode) {
      case 'de':
        return '$days Tage Gebetszeiten';
      case 'hr':
        return '$days dana vremena molitve';
      case 'fr':
        return '$days jours d\'heures de prière';
      case 'ar':
        return '$days أيام من أوقات الصلاة';
      case 'es':
        return '$days أيام de horarios de oración';
      default:
        return '$days days of prayer times';
    }
  }

  String get preparingExport {
    switch (locale.languageCode) {
      case 'de':
        return 'Export wird vorbereitet...';
      case 'hr':
        return 'Priprema izvoza...';
      case 'fr':
        return 'Préparation de l\'export...';
      case 'ar':
        return 'جاري تحضير التصدير...';
      case 'es':
        return 'Preparando exportación...';
      default:
        return 'Preparing export...';
    }
  }

  String get exportPdfSubtitle {
    switch (locale.languageCode) {
      case 'de':
        return 'Formatierte Tabelle mit Daten';
      case 'hr':
        return 'Formatirana tablica s datumima';
      case 'fr':
        return 'Tableau formaté avec dates';
      case 'ar':
        return 'جدول منسق مع التواريخ';
      case 'es':
        return 'Tabla formateada con fechas';
      default:
        return 'Formatted table with dates';
    }
  }

  String get exportCsvSubtitle {
    switch (locale.languageCode) {
      case 'de':
        return 'Tabellenkalkulationskompatibles Format';
      case 'hr':
        return 'Format kompatibilan s proračunskim tablicama';
      case 'fr':
        return 'Format compatible tableur';
      case 'ar':
        return 'تنسيق متوافق مع جداول البيانات';
      case 'es':
        return 'Formato compatible con hojas de cálculo';
      default:
        return 'Spreadsheet compatible format';
    }
  }

  String get sharePdf {
    switch (locale.languageCode) {
      case 'de':
        return 'PDF teilen';
      case 'hr':
        return 'Dijeli PDF';
      case 'fr':
        return 'Partager PDF';
      case 'ar':
        return 'مشاركة PDF';
      case 'es':
        return 'Compartir PDF';
      default:
        return 'Share PDF';
    }
  }

  String get sharePdfSubtitle {
    switch (locale.languageCode) {
      case 'de':
        return 'Über Apps teilen';
      case 'hr':
        return 'Dijeli putem aplikacija';
      case 'fr':
        return 'Partager via les apps';
      case 'ar':
        return 'مشاركة عبر التطبيقات';
      case 'es':
        return 'Compartir vía apps';
      default:
        return 'Share via apps';
    }
  }

  String get printSubtitle {
    switch (locale.languageCode) {
      case 'de':
        return 'Gebetszeiten drucken';
      case 'hr':
        return 'Ispiši vremena molitve';
      case 'fr':
        return 'Imprimer les heures de prière';
      case 'ar':
        return 'طباعة أوقات الصلاة';
      case 'es':
        return 'Imprimir horarios de oración';
      default:
        return 'Print prayer times';
    }
  }

  String get exportPdfLandscape {
    switch (locale.languageCode) {
      case 'de':
        return 'PDF exportieren (Querformat)';
      case 'hr':
        return 'Izvezi PDF (vodoravno)';
      case 'fr':
        return 'Exporter PDF (paysage)';
      case 'ar':
        return 'تصدير PDF (أفقي)';
      case 'es':
        return 'Exportar PDF (Horizontal)';
      default:
        return 'Export PDF (Landscape)';
    }
  }

  String get exportPdfLandscapeSubtitle {
    switch (locale.languageCode) {
      case 'de':
        return 'Querformat-Ausrichtung';
      case 'hr':
        return 'Vodoravna orijentacija';
      case 'fr':
        return 'Orientation paysage';
      case 'ar':
        return 'اتجاه أفقي';
      case 'es':
        return 'Orientación horizontal';
      default:
        return 'Landscape orientation';
    }
  }

  String get date {
    switch (locale.languageCode) {
      case 'de':
        return 'Datum';
      case 'hr':
        return 'Datum';
      case 'fr':
        return 'Date';
      case 'ar':
        return 'التاريخ';
      case 'es':
        return 'Fecha';
      default:
        return 'Date';
    }
  }

  // Offline functionality strings
  String get offlineMode {
    switch (locale.languageCode) {
      case 'de':
        return 'Offline-Modus - Zeiten aus dem Cache';
      case 'hr':
        return 'Offline način - vremena iz predmemorije';
      case 'fr':
        return 'Mode hors ligne - heures du cache';
      case 'ar':
        return 'وضع عدم الاتصال - الأوقات من التخزين المؤقت';
      case 'es':
        return 'Modo sin conexión - horarios desde caché';
      default:
        return 'Offline mode - times from cache';
    }
  }

  String get prefetchPrayerTimes {
    switch (locale.languageCode) {
      case 'de':
        return 'Gebetszeiten vorab laden';
      case 'hr':
        return 'Unaprijed učitaj vremena molitve';
      case 'fr':
        return 'Précharger les heures de prière';
      case 'ar':
        return 'تحميل أوقات الصلاة مسبقاً';
      case 'es':
        return 'Precargar horarios de oración';
      default:
        return 'Prefetch prayer times';
    }
  }

  String get prefetchDescription {
    switch (locale.languageCode) {
      case 'de':
        return 'Lade Gebetszeiten für die nächsten 30 Tage für Offline-Nutzung';
      case 'hr':
        return 'Učitaj vremena molitve za sljedećih 30 dana za offline korištenje';
      case 'fr':
        return 'Charger les heures de prière pour les 30 prochains jours pour utilisation hors ligne';
      case 'ar':
        return 'تحميل أوقات الصلاة للـ 30 يوماً القادمة للاستخدام دون اتصال';
      default:
        return 'Load prayer times for the next 30 days for offline use';
    }
  }

  String get prefetchSuccess {
    switch (locale.languageCode) {
      case 'de':
        return 'Gebetszeiten erfolgreich vorab geladen';
      case 'hr':
        return 'Vremena molitve uspješno unaprijed učitana';
      case 'fr':
        return 'Heures de prière préchargées avec succès';
      case 'ar':
        return 'تم تحميل أوقات الصلاة مسبقاً بنجاح';
      case 'es':
        return 'Horarios de oración precargados exitosamente';
      default:
        return 'Prayer times prefetched successfully';
    }
  }

  String get prefetchError {
    switch (locale.languageCode) {
      case 'de':
        return 'Fehler beim Vorladen der Gebetszeiten';
      case 'hr':
        return 'Greška pri unaprijed učitavanju vremena molitve';
      case 'fr':
        return 'Erreur lors du préchargement des heures de prière';
      case 'ar':
        return 'خطأ في تحميل أوقات الصلاة مسبقاً';
      case 'es':
        return 'Error al precargar horarios de oración';
      default:
        return 'Error prefetching prayer times';
    }
  }

  String get emailTemplate {
    switch (locale.languageCode) {
      case 'de':
        return 'Hallo Support-Team,\n\nIch benötige Hilfe bei:\n\n[Beschreiben Sie Ihr Problem hier]\n\nVielen Dank!';
      case 'hr':
        return 'Pozdrav timu za podršku,\n\nTrebam pomoć s:\n\n[Opišite svoj problem ovdje]\n\nHvala!';
      case 'fr':
        return 'Bonjour équipe de support,\n\nJ\'ai besoin d\'aide avec:\n\n[Décrivez votre problème ici]\n\nMerci!';
      case 'ar':
        return 'مرحباً فريق الدعم،\n\nأحتاج مساعدة في:\n\n[اوصف مشكلتك هنا]\n\nشكراً!';
      default:
        return 'Hello Support Team,\n\nI need help with:\n\n[Describe your issue here]\n\nThank you!';
    }
  }

  String get feedbackTemplate {
    switch (locale.languageCode) {
      case 'de':
        return 'Hallo,\n\nIch möchte folgendes Feedback teilen:\n\n[Ihr Feedback hier]\n\nVielen Dank!';
      case 'hr':
        return 'Pozdrav,\n\nŽelim podijeliti sljedeću povratnu informaciju:\n\n[Vaša povratna informacija ovdje]\n\nHvala!';
      case 'fr':
        return 'Bonjour,\n\nJe souhaite partager les commentaires suivants:\n\n[Vos commentaires ici]\n\nMerci!';
      case 'ar':
        return 'مرحباً،\n\nأود مشاركة الملاحظات التالية:\n\n[ملاحظاتك هنا]\n\nشكراً!';
      default:
        return 'Hello,\n\nI would like to share the following feedback:\n\n[Your feedback here]\n\nThank you!';
    }
  }

  String get monthlyPrayerTimes {
    switch (locale.languageCode) {
      case 'de':
        return 'Monatliche Gebetszeiten';
      case 'hr':
        return 'Mjesečna vremena molitve';
      case 'fr':
        return 'Heures de prière mensuelles';
      case 'ar':
        return 'أوقات الصلاة الشهرية';
      default:
        return 'Monthly Prayer Times';
    }
  }

  String get weeklyPrayerTimes {
    switch (locale.languageCode) {
      case 'de':
        return 'Wöchentliche Gebetszeiten';
      case 'hr':
        return 'Tjedna vremena molitve';
      case 'fr':
        return 'Heures de prière hebdomadaires';
      case 'ar':
        return 'أوقات الصلاة الأسبوعية';
      case 'es':
        return 'Horarios de Oración Semanales';
      default:
        return 'Weekly Prayer Times';
    }
  }

  String get location {
    switch (locale.languageCode) {
      case 'de':
        return 'Standort';
      case 'hr':
        return 'Lokacija';
      case 'fr':
        return 'Localisation';
      case 'ar':
        return 'الموقع';
      case 'es':
        return 'Ubicación';
      default:
        return 'Location';
    }
  }

  String get notifications {
    switch (locale.languageCode) {
      case 'de':
        return 'Benachrichtigungen';
      case 'hr':
        return 'Obavijesti';
      case 'fr':
        return 'Notifications';
      case 'ar':
        return 'الإشعارات';
      case 'es':
        return 'Notificaciones';
      default:
        return 'Notifications';
    }
  }

  String get calculationMethod {
    switch (locale.languageCode) {
      case 'de':
        return 'Berechnungsmethode';
      case 'hr':
        return 'Metoda izračuna';
      case 'fr':
        return 'Méthode de calcul';
      case 'ar':
        return 'طريقة الحساب';
      case 'es':
        return 'Método de Cálculo';
      default:
        return 'Calculation Method';
    }
  }

  String get language {
    switch (locale.languageCode) {
      case 'de':
        return 'Sprache';
      case 'hr':
        return 'Jezik';
      case 'fr':
        return 'Langue';
      case 'ar':
        return 'اللغة';
      case 'es':
        return 'Idioma';
      default:
        return 'Language';
    }
  }

  String get theme {
    switch (locale.languageCode) {
      case 'de':
        return 'Design';
      case 'hr':
        return 'Tema';
      case 'fr':
        return 'Thème';
      case 'ar':
        return 'المظهر';
      case 'es':
        return 'Tema';
      default:
        return 'Theme';
    }
  }

  String get background {
    switch (locale.languageCode) {
      case 'de':
        return 'Hintergrund';
      case 'hr':
        return 'Pozadina';
      case 'fr':
        return 'Arrière-plan';
      case 'ar':
        return 'الخلفية';
      case 'es':
        return 'Fondo';
      default:
        return 'Background';
    }
  }

  // Qibla Compass
  String get qiblaCompass {
    switch (locale.languageCode) {
      case 'de':
        return 'Qibla-Kompass';
      case 'hr':
        return 'Qibla kompas';
      case 'fr':
        return 'Boussole Qibla';
      case 'ar':
        return 'بوصلة القبلة';
      case 'es':
        return 'Brújula Qibla';
      default:
        return 'Qibla Compass';
    }
  }

  String get qiblaCompassDescription {
    switch (locale.languageCode) {
      case 'de':
        return 'Finde die Richtung zur Kaaba in Mekka mit deinem Gerätekompass';
      case 'hr':
        return 'Pronađite smjer prema Kabi u Meki pomoću kompasa uređaja';
      case 'fr':
        return 'Trouvez la direction vers la Kaaba à La Mecque avec votre boussole';
      case 'ar':
        return 'اعثر على اتجاه الكعبة في مكة باستخدام بوصلة جهازك';
      case 'es':
        return 'Encuentra la dirección hacia la Kaaba en La Meca usando tu brújula del dispositivo';
      default:
        return 'Find the direction to Kaaba in Mecca using your device compass';
    }
  }

  String get startCompass {
    switch (locale.languageCode) {
      case 'de':
        return 'Kompass starten';
      case 'hr':
        return 'Pokretanje kompasa';
      case 'fr':
        return 'Démarrer la boussole';
      case 'ar':
        return 'تشغيل البوصلة';
      case 'es':
        return 'Iniciar Brújula';
      default:
        return 'Start Compass';
    }
  }

  String get calibrateCompass {
    switch (locale.languageCode) {
      case 'de':
        return 'Kompass kalibrieren';
      case 'hr':
        return 'Kalibriraj kompas';
      case 'fr':
        return 'Calibrer la boussole';
      case 'ar':
        return 'معايرة البوصلة';
      case 'es':
        return 'Calibrar Brújula';
      default:
        return 'Calibrate Compass';
    }
  }

  String get compassMode {
    switch (locale.languageCode) {
      case 'de':
        return 'Kompass-Modus';
      case 'hr':
        return 'Način kompasa';
      case 'fr':
        return 'Mode boussole';
      case 'ar':
        return 'وضع البوصلة';
      case 'es':
        return 'Modo Brújula';
      default:
        return 'Compass Mode';
    }
  }

  String get mapMode {
    switch (locale.languageCode) {
      case 'de':
        return 'Karten-Modus';
      case 'hr':
        return 'Način karte';
      case 'fr':
        return 'Mode carte';
      case 'ar':
        return 'وضع الخريطة';
      case 'es':
        return 'Modo Mapa';
      default:
        return 'Map Mode';
    }
  }

  String get setManualLocation {
    switch (locale.languageCode) {
      case 'de':
        return 'Standort manuell setzen';
      case 'hr':
        return 'Postavite lokaciju ručno';
      case 'fr':
        return 'Définir la localisation manuellement';
      case 'ar':
        return 'تعيين الموقع يدوياً';
      default:
        return 'Set Manual Location';
    }
  }

  String get initializingCompass {
    switch (locale.languageCode) {
      case 'de':
        return 'Kompass wird initialisiert...';
      case 'hr':
        return 'Inicijalizacija kompasa...';
      case 'fr':
        return 'Initialisation de la boussole...';
      case 'ar':
        return 'تهيئة البوصلة...';
      default:
        return 'Initializing compass...';
    }
  }

  String get compassError {
    switch (locale.languageCode) {
      case 'de':
        return 'Kompass-Fehler';
      case 'hr':
        return 'Greška kompasa';
      case 'fr':
        return 'Erreur de boussole';
      case 'ar':
        return 'خطأ في البوصلة';
      default:
        return 'Compass Error';
    }
  }

  String get tryAgain {
    switch (locale.languageCode) {
      case 'de':
        return 'Erneut versuchen';
      case 'hr':
        return 'Pokušaj ponovo';
      case 'fr':
        return 'Réessayer';
      case 'ar':
        return 'حاول مرة أخرى';
      case 'es':
        return 'Intentar de Nuevo';
      default:
        return 'Try Again';
    }
  }

  String get requestPermission {
    switch (locale.languageCode) {
      case 'de':
        return 'Berechtigung anfordern';
      case 'hr':
        return 'Zatraži dozvolu';
      case 'fr':
        return 'Demander la permission';
      case 'ar':
        return 'طلب الإذن';
      default:
        return 'Request Permission';
    }
  }

  String get compassReady {
    switch (locale.languageCode) {
      case 'de':
        return 'Kompass ist bereit und zeigt zur Qibla';
      case 'hr':
        return 'Kompas je spreman i pokazuje prema Qibli';
      case 'fr':
        return 'La boussole est prête et pointe vers la Qibla';
      case 'ar':
        return 'البوصلة جاهزة وتشير إلى القبلة';
      default:
        return 'Compass is ready and pointing to Qibla';
    }
  }

  String get compassCalibrating {
    switch (locale.languageCode) {
      case 'de':
        return 'Kompass benötigt Kalibrierung';
      case 'hr':
        return 'Kompas treba kalibraciju';
      case 'fr':
        return 'La boussole nécessite un étalonnage';
      case 'ar':
        return 'البوصلة تحتاج إلى معايرة';
      default:
        return 'Compass needs calibration';
    }
  }

  String get compassDisturbed {
    switch (locale.languageCode) {
      case 'de':
        return 'Kompass wird durch Störungen beeinträchtigt';
      case 'hr':
        return 'Kompas je poremećen interferencijom';
      case 'fr':
        return 'La boussole est perturbée par des interférences';
      case 'ar':
        return 'البوصلة متأثرة بالتداخل';
      default:
        return 'Compass is disturbed by interference';
    }
  }

  String get compassNotAvailable {
    switch (locale.languageCode) {
      case 'de':
        return 'Kompass-Sensor nicht verfügbar';
      case 'hr':
        return 'Senzor kompasa nije dostupan';
      case 'fr':
        return 'Capteur de boussole non disponible';
      case 'ar':
        return 'مستشعر البوصلة غير متوفر';
      default:
        return 'Compass sensor not available';
    }
  }

  String get compassPermissionDenied {
    switch (locale.languageCode) {
      case 'de':
        return 'Standortberechtigung für Kompass erforderlich';
      case 'hr':
        return 'Potrebna je dozvola za lokaciju za kompas';
      case 'fr':
        return 'Permission de localisation requise pour la boussole';
      case 'ar':
        return 'إذن الموقع مطلوب للبوصلة';
      default:
        return 'Location permission required for compass';
    }
  }

  String get distance {
    switch (locale.languageCode) {
      case 'de':
        return 'Entfernung';
      case 'hr':
        return 'Udaljenost';
      case 'fr':
        return 'Distance';
      case 'ar':
        return 'المسافة';
      case 'es':
        return 'Distancia';
      default:
        return 'Distance';
    }
  }

  String get bearing {
    switch (locale.languageCode) {
      case 'de':
        return 'Peilung';
      case 'hr':
        return 'Smjer';
      case 'fr':
        return 'Relèvement';
      case 'ar':
        return 'الاتجاه';
      case 'es':
        return 'Rumbo';
      default:
        return 'Bearing';
    }
  }

  String get calibrationNeeded {
    switch (locale.languageCode) {
      case 'de':
        return 'Kalibrierung erforderlich';
      case 'hr':
        return 'Potrebna kalibracija';
      case 'fr':
        return 'Étalonnage nécessaire';
      case 'ar':
        return 'المعايرة مطلوبة';
      default:
        return 'Calibration Needed';
    }
  }

  String get calibrationInstructions {
    switch (locale.languageCode) {
      case 'de':
        return 'Bewegen Sie Ihr Gerät in einer 8er-Form, um den Kompass-Sensor zu kalibrieren';
      case 'hr':
        return 'Pomičite uređaj u obliku broja 8 da biste kalibrirali senzor kompasa';
      case 'fr':
        return 'Déplacez votre appareil en forme de 8 pour calibrer le capteur de boussole';
      case 'ar':
        return 'حرك جهازك في شكل رقم 8 لمعايرة مستشعر البوصلة';
      default:
        return 'Move your device in a figure-8 pattern to calibrate the compass sensor';
    }
  }

  String get calibrateNow {
    switch (locale.languageCode) {
      case 'de':
        return 'Jetzt kalibrieren';
      case 'hr':
        return 'Kalibriraj sada';
      case 'fr':
        return 'Calibrer maintenant';
      case 'ar':
        return 'معايرة الآن';
      default:
        return 'Calibrate Now';
    }
  }

  // Location Permission Strings
  String get locationPermissionTitle {
    switch (locale.languageCode) {
      case 'de':
        return 'Standortberechtigung';
      case 'hr':
        return 'Dozvola za lokaciju';
      case 'fr':
        return 'Permission de localisation';
      case 'ar':
        return 'إذن الموقع';
      default:
        return 'Location Permission';
    }
  }

  String get locationPermissionDescription {
    switch (locale.languageCode) {
      case 'de':
        return 'AlFalah benötigt Zugriff auf Ihren Standort, um präzise Gebetszeiten für Ihre aktuelle Position zu berechnen.';
      case 'hr':
        return 'AlFalah treba pristup vašoj lokaciji za izračun preciznih vremena molitve za vašu trenutnu poziciju.';
      case 'fr':
        return 'AlFalah a besoin d\'accéder à votre localisation pour calculer des heures de prière précises pour votre position actuelle.';
      case 'ar':
        return 'يحتاج الفلاح إلى الوصول إلى موقعك لحساب أوقات الصلاة الدقيقة لموقعك الحالي.';
      default:
        return 'AlFalah needs access to your location to calculate precise prayer times for your current position.';
    }
  }

  String get locationPermissionBenefit1 {
    switch (locale.languageCode) {
      case 'de':
        return 'Automatische Gebetszeiten für Ihren Standort';
      case 'hr':
        return 'Automatska vremena molitve za vašu lokaciju';
      case 'fr':
        return 'Heures de prière automatiques pour votre emplacement';
      case 'ar':
        return 'أوقات الصلاة التلقائية لموقعك';
      default:
        return 'Automatic prayer times for your location';
    }
  }

  String get locationPermissionBenefit2 {
    switch (locale.languageCode) {
      case 'de':
        return 'Genaue Qibla-Richtung vom Kompass';
      case 'hr':
        return 'Točan smjer Qible iz kompasa';
      case 'fr':
        return 'Direction Qibla précise à partir de la boussole';
      case 'ar':
        return 'اتجاه القبلة الدقيق من البوصلة';
      default:
        return 'Accurate Qibla direction from compass';
    }
  }

  String get locationPermissionOptional {
    switch (locale.languageCode) {
      case 'de':
        return 'Sie können Ihren Standort auch manuell festlegen.';
      case 'hr':
        return 'Također možete ručno postaviti svoju lokaciju.';
      case 'fr':
        return 'Vous pouvez également définir manuellement votre emplacement.';
      case 'ar':
        return 'يمكنك أيضاً تعيين موقعك يدوياً.';
      default:
        return 'You can also set your location manually.';
    }
  }

  String get notNow {
    switch (locale.languageCode) {
      case 'de':
        return 'Nicht jetzt';
      case 'hr':
        return 'Ne sada';
      case 'fr':
        return 'Pas maintenant';
      case 'ar':
        return 'ليس الآن';
      case 'es':
        return 'Ahora No';
      default:
        return 'Not Now';
    }
  }

  String get allowLocation {
    switch (locale.languageCode) {
      case 'de':
        return 'Standort erlauben';
      case 'hr':
        return 'Dopusti lokaciju';
      case 'fr':
        return 'Autoriser la localisation';
      case 'ar':
        return 'السماح بالموقع';
      case 'es':
        return 'Permitir Ubicación';
      default:
        return 'Allow Location';
    }
  }

  String get locationPermissionGranted {
    switch (locale.languageCode) {
      case 'de':
        return 'Standortberechtigung erteilt';
      case 'hr':
        return 'Dozvola za lokaciju odobrena';
      case 'fr':
        return 'Permission de localisation accordée';
      case 'ar':
        return 'تم منح إذن الموقع';
      default:
        return 'Location Permission Granted';
    }
  }

  String get locationPermissionGrantedDescription {
    switch (locale.languageCode) {
      case 'de':
        return 'Vielen Dank! AlFalah kann jetzt präzise Gebetszeiten für Ihren Standort berechnen.';
      case 'hr':
        return 'Hvala! AlFalah sada može izračunati precizna vremena molitve za vašu lokaciju.';
      case 'fr':
        return 'Merci! AlFalah peut maintenant calculer des heures de prière précises pour votre emplacement.';
      case 'ar':
        return 'شكراً! يمكن للفلاح الآن حساب أوقات الصلاة الدقيقة لموقعك.';
      default:
        return 'Thank you! AlFalah can now calculate precise prayer times for your location.';
    }
  }

  String get locationPermissionDenied {
    switch (locale.languageCode) {
      case 'de':
        return 'Standortberechtigung verweigert';
      case 'hr':
        return 'Dozvola za lokaciju odbijena';
      case 'fr':
        return 'Permission de localisation refusée';
      case 'ar':
        return 'تم رفض إذن الموقع';
      default:
        return 'Location Permission Denied';
    }
  }

  String get locationPermissionDeniedDescription {
    switch (locale.languageCode) {
      case 'de':
        return 'Kein Problem! Sie können Ihren Standort manuell in den Einstellungen festlegen.';
      case 'hr':
        return 'Nema problema! Možete ručno postaviti svoju lokaciju u postavkama.';
      case 'fr':
        return 'Pas de problème! Vous pouvez définir manuellement votre emplacement dans les paramètres.';
      case 'ar':
        return 'لا مشكلة! يمكنك تعيين موقعك يدوياً في الإعدادات.';
      default:
        return 'No problem! You can manually set your location in settings.';
    }
  }

  String get locationPermissionManualSettings {
    switch (locale.languageCode) {
      case 'de':
        return 'Sie können die Berechtigung später in den App-Einstellungen erteilen.';
      case 'hr':
        return 'Možete dati dozvolu kasnije u postavkama aplikacije.';
      case 'fr':
        return 'Vous pouvez accorder la permission plus tard dans les paramètres de l\'app.';
      case 'ar':
        return 'يمكنك منح الإذن لاحقاً في إعدادات التطبيق.';
      default:
        return 'You can grant permission later in app settings.';
    }
  }

  String get openSettings {
    switch (locale.languageCode) {
      case 'de':
        return 'Einstellungen öffnen';
      case 'hr':
        return 'Otvori postavke';
      case 'fr':
        return 'Ouvrir les paramètres';
      case 'ar':
        return 'فتح الإعدادات';
      case 'es':
        return 'Abrir Configuración';
      default:
        return 'Open Settings';
    }
  }

  String get ok {
    switch (locale.languageCode) {
      case 'de':
        return 'OK';
      case 'hr':
        return 'OK';
      case 'fr':
        return 'OK';
      case 'ar':
        return 'موافق';
      case 'es':
        return 'OK';
      default:
        return 'OK';
    }
  }

  // Additional Settings Translations
  String get general {
    switch (locale.languageCode) {
      case 'de':
        return 'Allgemein';
      case 'hr':
        return 'Općenito';
      case 'fr':
        return 'Général';
      case 'ar':
        return 'عام';
      case 'es':
        return 'General';
      default:
        return 'General';
    }
  }

  String get designStyle {
    switch (locale.languageCode) {
      case 'de':
        return 'Design-Stil';
      case 'hr':
        return 'Stil dizajna';
      case 'fr':
        return 'Style de design';
      case 'ar':
        return 'نمط التصميم';
      case 'es':
        return 'Estilo de Diseño';
      default:
        return 'Design Style';
    }
  }

  String get prayerSettings {
    switch (locale.languageCode) {
      case 'de':
        return 'Gebetseinstellungen';
      case 'hr':
        return 'Postavke molitve';
      case 'fr':
        return 'Paramètres de prière';
      case 'ar':
        return 'إعدادات الصلاة';
      case 'es':
        return 'Configuraciones de Oración';
      default:
        return 'Prayer Settings';
    }
  }

  String get hijriDateAdjustment {
    switch (locale.languageCode) {
      case 'de':
        return 'Hijri-Datum Anpassung';
      case 'hr':
        return 'Podešavanje Hijri datuma';
      case 'fr':
        return 'Ajustement de la date Hijri';
      case 'ar':
        return 'تعديل التاريخ الهجري';
      case 'es':
        return 'Ajuste de Fecha Hijri';
      default:
        return 'Hijri Date Adjustment';
    }
  }

  String get hijriValidation {
    switch (locale.languageCode) {
      case 'de':
        return 'Hijri-Validierung';
      case 'hr':
        return 'Hijri provjera';
      case 'fr':
        return 'Validation Hijri';
      case 'ar':
        return 'التحقق الهجري';
      case 'es':
        return 'Validación Hijri';
      default:
        return 'Hijri Validation';
    }
  }

  String get hijriValidationSubtitle {
    switch (locale.languageCode) {
      case 'de':
        return 'Test gegen Umm al-Qura Referenzdaten';
      case 'hr':
        return 'Test protiv Umm al-Qura referentnih podataka';
      case 'fr':
        return 'Test contre les données de référence Umm al-Qura';
      case 'ar':
        return 'اختبار ضد بيانات أم القرى المرجعية';
      case 'es':
        return 'Prueba contra datos de referencia Umm al-Qura';
      default:
        return 'Test against Umm al-Qura reference data';
    }
  }

  String get autoLocation {
    switch (locale.languageCode) {
      case 'de':
        return 'Automatischer Standort';
      case 'hr':
        return 'Automatska lokacija';
      case 'fr':
        return 'Localisation automatique';
      case 'ar':
        return 'الموقع التلقائي';
      case 'es':
        return 'Ubicación Automática';
      default:
        return 'Auto Location';
    }
  }

  String get autoLocationSubtitle {
    switch (locale.languageCode) {
      case 'de':
        return 'Automatisch Ihren Standort erkennen';
      case 'hr':
        return 'Automatski otkrivanje vaše lokacije';
      case 'fr':
        return 'Détecter automatiquement votre localisation';
      case 'ar':
        return 'اكتشاف موقعك تلقائياً';
      case 'es':
        return 'Detectar automáticamente su ubicación';
      default:
        return 'Automatically detect your location';
    }
  }

  String get notificationSettings {
    switch (locale.languageCode) {
      case 'de':
        return 'Benachrichtigungen';
      case 'hr':
        return 'Obavijesti';
      case 'fr':
        return 'Notifications';
      case 'ar':
        return 'الإشعارات';
      case 'es':
        return 'Notificaciones';
      default:
        return 'Notifications';
    }
  }

  String get enablePrayerNotifications {
    switch (locale.languageCode) {
      case 'de':
        return 'Gebetszeit-Benachrichtigungen aktivieren';
      case 'hr':
        return 'Omogući obavijesti za vremena molitve';
      case 'fr':
        return 'Activer les notifications d\'heures de prière';
      case 'ar':
        return 'تفعيل إشعارات أوقات الصلاة';
      case 'es':
        return 'Habilitar notificaciones de horarios de oración';
      default:
        return 'Enable prayer time notifications';
    }
  }

  String get adhanSound {
    switch (locale.languageCode) {
      case 'de':
        return 'Adhan-Klang';
      case 'hr':
        return 'Adhan zvuk';
      case 'fr':
        return 'Son Adhan';
      case 'ar':
        return 'صوت الأذان';
      case 'es':
        return 'Sonido Adhan';
      default:
        return 'Adhan Sound';
    }
  }

  String get playAdhanSound {
    switch (locale.languageCode) {
      case 'de':
        return 'Adhan-Klang mit Benachrichtigungen abspielen';
      case 'hr':
        return 'Reproduciraj Adhan zvuk s obavijestima';
      case 'fr':
        return 'Jouer le son Adhan avec les notifications';
      case 'ar':
        return 'تشغيل صوت الأذان مع الإشعارات';
      case 'es':
        return 'Reproducir sonido Adhan con notificaciones';
      default:
        return 'Play Adhan sound with notifications';
    }
  }

  String get resetSettings {
    switch (locale.languageCode) {
      case 'de':
        return 'Einstellungen zurücksetzen';
      case 'hr':
        return 'Resetiraj postavke';
      case 'fr':
        return 'Réinitialiser les paramètres';
      case 'ar':
        return 'إعادة تعيين الإعدادات';
      case 'es':
        return 'Restablecer Configuración';
      default:
        return 'Reset Settings';
    }
  }

  String get resetSettingsConfirmation {
    switch (locale.languageCode) {
      case 'de':
        return 'Sind Sie sicher, dass Sie alle Einstellungen auf die Standardwerte zurücksetzen möchten?';
      case 'hr':
        return 'Jeste li sigurni da želite resetirati sve postavke na zadane vrijednosti?';
      case 'fr':
        return 'Êtes-vous sûr de vouloir réinitialiser tous les paramètres aux valeurs par défaut ?';
      case 'ar':
        return 'هل أنت متأكد أنك تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟';
      case 'es':
        return '¿Está seguro de que desea restablecer todas las configuraciones a los valores predeterminados?';
      default:
        return 'Are you sure you want to reset all settings to default values?';
    }
  }

  String get cancel {
    switch (locale.languageCode) {
      case 'de':
        return 'Abbrechen';
      case 'hr':
        return 'Odustani';
      case 'fr':
        return 'Annuler';
      case 'ar':
        return 'إلغاء';
      case 'es':
        return 'Cancelar';
      default:
        return 'Cancel';
    }
  }

  String get reset {
    switch (locale.languageCode) {
      case 'de':
        return 'Zurücksetzen';
      case 'hr':
        return 'Resetiraj';
      case 'fr':
        return 'Réinitialiser';
      case 'ar':
        return 'إعادة تعيين';
      case 'es':
        return 'Restablecer';
      default:
        return 'Reset';
    }
  }

  String get debug {
    switch (locale.languageCode) {
      case 'de':
        return 'Debug';
      case 'hr':
        return 'Debug';
      case 'fr':
        return 'Debug';
      case 'ar':
        return 'تشخيص الأخطاء';
      case 'es':
        return 'Depuración';
      default:
        return 'Debug';
    }
  }

  String get data {
    switch (locale.languageCode) {
      case 'de':
        return 'Daten';
      case 'hr':
        return 'Podaci';
      case 'fr':
        return 'Données';
      case 'ar':
        return 'البيانات';
      case 'es':
        return 'Datos';
      default:
        return 'Data';
    }
  }

  String get about {
    switch (locale.languageCode) {
      case 'de':
        return 'Über';
      case 'hr':
        return 'O aplikaciji';
      case 'fr':
        return 'À propos';
      case 'ar':
        return 'حول';
      case 'es':
        return 'Acerca de';
      default:
        return 'About';
    }
  }

  String get version {
    switch (locale.languageCode) {
      case 'de':
        return 'Version';
      case 'hr':
        return 'Verzija';
      case 'fr':
        return 'Version';
      case 'ar':
        return 'الإصدار';
      case 'es':
        return 'Versión';
      default:
        return 'Version';
    }
  }

  String get retry {
    switch (locale.languageCode) {
      case 'de':
        return 'Erneut versuchen';
      case 'hr':
        return 'Pokušaj ponovo';
      case 'fr':
        return 'Réessayer';
      case 'ar':
        return 'إعادة المحاولة';
      case 'es':
        return 'Reintentar';
      default:
        return 'Retry';
    }
  }
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'de', 'hr', 'fr', 'ar', 'es', 'zh', 'ru', 'ms', 'id', 'hi'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

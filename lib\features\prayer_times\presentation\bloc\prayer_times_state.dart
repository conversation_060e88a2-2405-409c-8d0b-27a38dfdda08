part of 'prayer_times_bloc.dart';

abstract class PrayerTimesState extends Equatable {
  const PrayerTimesState();

  @override
  List<Object?> get props => [];
}

class PrayerTimesInitial extends PrayerTimesState {}

class PrayerTimesLoading extends PrayerTimesState {}

class PrayerTimesLoaded extends PrayerTimesState {
  final PrayerTimes prayerTimes;
  final double latitude;
  final double longitude;
  final int calculationMethod;

  const PrayerTimesLoaded({
    required this.prayerTimes,
    required this.latitude,
    required this.longitude,
    required this.calculationMethod,
  });

  @override
  List<Object> get props => [prayerTimes, latitude, longitude, calculationMethod];
}

class PrayerTimesRefreshing extends PrayerTimesState {
  final PrayerTimes prayerTimes;
  final double latitude;
  final double longitude;
  final int calculationMethod;

  const PrayerTimesRefreshing({
    required this.prayerTimes,
    required this.latitude,
    required this.longitude,
    required this.calculationMethod,
  });

  @override
  List<Object> get props => [prayerTimes, latitude, longitude, calculationMethod];
}

class PrayerTimesError extends PrayerTimesState {
  final String message;

  const PrayerTimesError({required this.message});

  @override
  List<Object> get props => [message];
}

import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import '../../../../core/utils/hijri_date.dart';
import '../../domain/entities/prayer_times.dart';
import '../../presentation/bloc/prayer_view_state.dart';

class ExportService {
  /// Export prayer times to CSV format
  static Future<File> exportToCsv({
    required List<PrayerTimes> prayerTimesList,
    required PrayerViewType viewType,
    required String city,
    required String languageCode,
    int hijriOffset = 0,
  }) async {
    final directory = await getApplicationDocumentsDirectory();
    final fileName = _generateFileName(city, prayerTimesList, 'csv');
    final file = File('${directory.path}/$fileName');

    final csvContent = StringBuffer();
    
    // CSV Header
    csvContent.writeln('Date,Hijri Date,<PERSON>ajr,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>');
    
    // CSV Data
    for (final prayerTimes in prayerTimesList) {
      final gregorianDate = _formatGregorianDate(prayerTimes.date);
      final hijriDate = HijriDateUtils.getHijriDateString(
        prayerTimes.date, 
        languageCode, 
        offset: hijriOffset,
      );
      
      csvContent.writeln([
        gregorianDate,
        hijriDate,
        prayerTimes.fajr,
        prayerTimes.dhuhr,
        prayerTimes.asr,
        prayerTimes.maghrib,
        prayerTimes.isha,
      ].join(','));
    }

    await file.writeAsString(csvContent.toString());
    return file;
  }

  /// Export prayer times to PDF format
  static Future<File> exportToPdf({
    required List<PrayerTimes> prayerTimesList,
    required PrayerViewType viewType,
    required String city,
    required String calculationMethod,
    required String timezone,
    required String languageCode,
    required bool isDarkMode,
    int hijriOffset = 0,
  }) async {
    final directory = await getApplicationDocumentsDirectory();
    final fileName = _generateFileName(city, prayerTimesList, 'pdf');
    final file = File('${directory.path}/$fileName');

    final pdf = pw.Document();
    
    // Colors for light/dark mode
    final backgroundColor = isDarkMode ? PdfColors.grey900 : PdfColors.white;
    final textColor = isDarkMode ? PdfColors.white : PdfColors.black;
    final headerColor = isDarkMode ? PdfColors.blue300 : PdfColors.blue800;
    final borderColor = isDarkMode ? PdfColors.grey600 : PdfColors.grey300;

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // Title
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(16),
              decoration: pw.BoxDecoration(
                color: headerColor,
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Prayer Times - ${_getViewTypeTitle(viewType)}',
                    style: pw.TextStyle(
                      fontSize: 20,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.white,
                    ),
                  ),
                  pw.SizedBox(height: 8),
                  pw.Text(
                    'Location: $city',
                    style: const pw.TextStyle(fontSize: 12, color: PdfColors.white),
                  ),
                  pw.Text(
                    'Calculation Method: $calculationMethod',
                    style: const pw.TextStyle(fontSize: 12, color: PdfColors.white),
                  ),
                  pw.Text(
                    'Timezone: $timezone',
                    style: const pw.TextStyle(fontSize: 12, color: PdfColors.white),
                  ),
                  pw.Text(
                    'Period: ${_formatDateRange(prayerTimesList)}',
                    style: const pw.TextStyle(fontSize: 12, color: PdfColors.white),
                  ),
                ],
              ),
            ),
            pw.SizedBox(height: 20),
            
            // Prayer times table
            pw.Table(
              border: pw.TableBorder.all(color: borderColor),
              columnWidths: {
                0: const pw.FlexColumnWidth(2),
                1: const pw.FlexColumnWidth(2),
                2: const pw.FlexColumnWidth(1.5),
                3: const pw.FlexColumnWidth(1.5),
                4: const pw.FlexColumnWidth(1.5),
                5: const pw.FlexColumnWidth(1.5),
                6: const pw.FlexColumnWidth(1.5),
              },
              children: [
                // Header row
                pw.TableRow(
                  decoration: pw.BoxDecoration(color: borderColor),
                  children: [
                    _buildTableCell('Date', textColor, isHeader: true),
                    _buildTableCell('Hijri Date', textColor, isHeader: true),
                    _buildTableCell('Fajr', textColor, isHeader: true),
                    _buildTableCell('Dhuhr', textColor, isHeader: true),
                    _buildTableCell('Asr', textColor, isHeader: true),
                    _buildTableCell('Maghrib', textColor, isHeader: true),
                    _buildTableCell('Isha', textColor, isHeader: true),
                  ],
                ),
                
                // Data rows
                ...prayerTimesList.map((prayerTimes) {
                  final gregorianDate = _formatGregorianDate(prayerTimes.date);
                  final hijriDate = HijriDateUtils.getHijriDateString(
                    prayerTimes.date, 
                    languageCode, 
                    offset: hijriOffset,
                  );
                  
                  return pw.TableRow(
                    children: [
                      _buildTableCell(gregorianDate, textColor),
                      _buildTableCell(_getCompactHijriDate(hijriDate), textColor),
                      _buildTableCell(prayerTimes.fajr, textColor),
                      _buildTableCell(prayerTimes.dhuhr, textColor),
                      _buildTableCell(prayerTimes.asr, textColor),
                      _buildTableCell(prayerTimes.maghrib, textColor),
                      _buildTableCell(prayerTimes.isha, textColor),
                    ],
                  );
                }).toList(),
              ],
            ),
            
            pw.SizedBox(height: 20),
            
            // Footer
            pw.Text(
              'Generated on ${DateTime.now().toString().split('.')[0]}',
              style: pw.TextStyle(fontSize: 10, color: textColor),
            ),
            if (hijriOffset != 0)
              pw.Text(
                'Note: Hijri dates adjusted by $hijriOffset day${hijriOffset.abs() > 1 ? 's' : ''}',
                style: pw.TextStyle(fontSize: 10, color: textColor),
              ),
          ];
        },
      ),
    );

    final pdfBytes = await pdf.save();
    await file.writeAsBytes(pdfBytes);
    return file;
  }

  /// Share exported file
  static Future<void> shareFile(File file, String title) async {
    await Share.shareXFiles(
      [XFile(file.path)],
      text: title,
    );
  }

  /// Print PDF file
  static Future<void> printPdf(Uint8List pdfBytes, String title) async {
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdfBytes,
      name: title,
    );
  }

  // Helper methods
  static String _generateFileName(String city, List<PrayerTimes> prayerTimesList, String extension) {
    if (prayerTimesList.isEmpty) return 'PrayerTimes_Empty.$extension';
    
    final startDate = prayerTimesList.first.date;
    final endDate = prayerTimesList.last.date;
    final cityClean = city.replaceAll(RegExp(r'[^\w\s-]'), '').replaceAll(' ', '_');
    
    if (prayerTimesList.length == 1) {
      return 'PrayerTimes_${cityClean}_${_formatDateForFilename(startDate)}.$extension';
    } else {
      return 'PrayerTimes_${cityClean}_${_formatDateForFilename(startDate)}_to_${_formatDateForFilename(endDate)}.$extension';
    }
  }

  static String _formatDateForFilename(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  static String _formatGregorianDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  static String _formatDateRange(List<PrayerTimes> prayerTimesList) {
    if (prayerTimesList.isEmpty) return 'No dates';
    if (prayerTimesList.length == 1) return _formatGregorianDate(prayerTimesList.first.date);
    
    return '${_formatGregorianDate(prayerTimesList.first.date)} - ${_formatGregorianDate(prayerTimesList.last.date)}';
  }

  static String _getViewTypeTitle(PrayerViewType viewType) {
    switch (viewType) {
      case PrayerViewType.today:
        return 'Today';
      case PrayerViewType.week:
        return 'Week';
      case PrayerViewType.month:
        return 'Month';
    }
  }

  static String _getCompactHijriDate(String fullHijriDate) {
    // Remove "(Umm al-Qura)" suffix for PDF compactness
    return fullHijriDate.replaceAll(' (Umm al-Qura)', '');
  }

  static pw.Widget _buildTableCell(String text, PdfColor textColor, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 10 : 9,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: textColor,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }
}

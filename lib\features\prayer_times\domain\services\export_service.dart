import 'dart:io';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import '../entities/prayer_times.dart';
import '../../../../core/utils/hijri_date.dart';

class ExportService {
  /// Export prayer times to PDF
  static Future<Uint8List> generatePDF({
    required List<PrayerTimes> prayerTimesList,
    required String title,
    required String languageCode,
    required String viewType,
    int hijriOffset = 0,
  }) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // Header
            pw.Header(
              level: 0,
              child: pw.Text(
                title,
                style: pw.TextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
            pw.SizedBox(height: 20),
            
            // Prayer times table
            pw.Table(
              border: pw.TableBorder.all(),
              columnWidths: {
                0: const pw.FlexColumnWidth(2.5),
                1: const pw.FlexColumnWidth(1.2),
                2: const pw.FlexColumnWidth(1.2),
                3: const pw.FlexColumnWidth(1.2),
                4: const pw.FlexColumnWidth(1.2),
                5: const pw.FlexColumnWidth(1.2),
              },
              children: [
                // Header row
                pw.TableRow(
                  decoration: const pw.BoxDecoration(
                    color: PdfColors.grey300,
                  ),
                  children: [
                    _buildTableCell('Date', isHeader: true),
                    _buildTableCell('Fajr', isHeader: true),
                    _buildTableCell('Dhuhr', isHeader: true),
                    _buildTableCell('Asr', isHeader: true),
                    _buildTableCell('Maghrib', isHeader: true),
                    _buildTableCell('Isha', isHeader: true),
                  ],
                ),
                // Data rows
                ...prayerTimesList.map((prayerTimes) {
                  final gregorianDate = DateFormat('MMM d').format(prayerTimes.date);
                  final hijriDate = HijriDateUtils.getHijriDateString(
                    prayerTimes.date,
                    languageCode,
                    offset: hijriOffset,
                  ).split(' AH')[0]; // Remove "AH (Umm al-Qura)" suffix for compactness

                  return pw.TableRow(
                    children: [
                      _buildTableCell('$gregorianDate\n$hijriDate'),
                      _buildTableCell(prayerTimes.fajr),
                      _buildTableCell(prayerTimes.dhuhr),
                      _buildTableCell(prayerTimes.asr),
                      _buildTableCell(prayerTimes.maghrib),
                      _buildTableCell(prayerTimes.isha),
                    ],
                  );
                }).toList(),
              ],
            ),
            
            pw.SizedBox(height: 20),
            
            // Footer
            pw.Spacer(),
            pw.Container(
              padding: const pw.EdgeInsets.only(top: 20),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Generated on ${DateTime.now().toString().split('.')[0]}',
                    style: const pw.TextStyle(
                      fontSize: 10,
                      color: PdfColors.grey600,
                    ),
                  ),
                  pw.Text(
                    'Location: ${prayerTimesList.isNotEmpty ? "${prayerTimesList.first.city}, ${prayerTimesList.first.country}" : "Unknown"}',
                    style: const pw.TextStyle(
                      fontSize: 10,
                      color: PdfColors.grey600,
                    ),
                  ),
                  pw.Text(
                    'View: $viewType',
                    style: const pw.TextStyle(
                      fontSize: 10,
                      color: PdfColors.grey600,
                    ),
                  ),
                ],
              ),
            ),
          ];
        },
      ),
    );

    return pdf.save();
  }

  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// Export prayer times to CSV
  static String generateCSV({
    required List<PrayerTimes> prayerTimesList,
    required String languageCode,
    int hijriOffset = 0,
  }) {
    final buffer = StringBuffer();

    // Header
    buffer.writeln('Date,Hijri Date,Fajr,Dhuhr,Asr,Maghrib,Isha');

    // Data rows
    for (final prayerTimes in prayerTimesList) {
      final gregorianDate = '${prayerTimes.date.year}-${prayerTimes.date.month.toString().padLeft(2, '0')}-${prayerTimes.date.day.toString().padLeft(2, '0')}';
      final hijriDate = HijriDateUtils.getHijriDateString(
        prayerTimes.date,
        languageCode,
        offset: hijriOffset,
      ).split(' AH')[0]; // Remove "AH (Umm al-Qura)" suffix

      buffer.writeln([
        gregorianDate,
        '"$hijriDate"',
        prayerTimes.fajr,
        prayerTimes.dhuhr,
        prayerTimes.asr,
        prayerTimes.maghrib,
        prayerTimes.isha,
      ].join(','));
    }

    return buffer.toString();
  }

  /// Share prayer times as PDF
  static Future<void> sharePDF({
    required List<PrayerTimes> prayerTimesList,
    required String title,
    required String languageCode,
    required String viewType,
  }) async {
    try {
      final pdfData = await generatePDF(
        prayerTimesList: prayerTimesList,
        title: title,
        languageCode: languageCode,
        viewType: viewType,
      );

      final fileName = _generateFileName(prayerTimesList, viewType, 'pdf');
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/$fileName');
      await file.writeAsBytes(pdfData);

      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Prayer Times - $title',
      );
    } catch (e) {
      throw Exception('Failed to share PDF: $e');
    }
  }

  /// Share prayer times as CSV
  static Future<void> shareCSV({
    required List<PrayerTimes> prayerTimesList,
    required String title,
    required String languageCode,
    required String viewType,
  }) async {
    try {
      final csvData = generateCSV(
        prayerTimesList: prayerTimesList,
        languageCode: languageCode,
      );

      final fileName = _generateFileName(prayerTimesList, viewType, 'csv');
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/$fileName');
      await file.writeAsString(csvData);

      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Prayer Times - $title',
      );
    } catch (e) {
      throw Exception('Failed to share CSV: $e');
    }
  }

  /// Print prayer times
  static Future<void> printPrayerTimes({
    required List<PrayerTimes> prayerTimesList,
    required String title,
    required String languageCode,
    required String viewType,
  }) async {
    try {
      final pdfData = await generatePDF(
        prayerTimesList: prayerTimesList,
        title: title,
        languageCode: languageCode,
        viewType: viewType,
      );

      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdfData,
      );
    } catch (e) {
      throw Exception('Failed to print: $e');
    }
  }

  /// Generate filename with date range
  static String _generateFileName(List<PrayerTimes> prayerTimesList, String viewType, String extension) {
    if (prayerTimesList.isEmpty) return 'PrayerTimes.$extension';

    final city = prayerTimesList.first.city.replaceAll(' ', '_');
    final firstDate = prayerTimesList.first.date;
    final lastDate = prayerTimesList.last.date;

    if (prayerTimesList.length == 1) {
      // Single day
      final dateStr = '${firstDate.year}-${firstDate.month.toString().padLeft(2, '0')}-${firstDate.day.toString().padLeft(2, '0')}';
      return 'PrayerTimes_${city}_$dateStr.$extension';
    } else {
      // Date range
      final firstDateStr = '${firstDate.year}-${firstDate.month.toString().padLeft(2, '0')}-${firstDate.day.toString().padLeft(2, '0')}';
      final lastDateStr = '${lastDate.year}-${lastDate.month.toString().padLeft(2, '0')}-${lastDate.day.toString().padLeft(2, '0')}';
      return 'PrayerTimes_${city}_${firstDateStr}_${lastDateStr}.$extension';
    }
  }
}

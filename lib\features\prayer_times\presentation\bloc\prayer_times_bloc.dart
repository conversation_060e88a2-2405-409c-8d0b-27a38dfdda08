import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:get_it/get_it.dart';
import '../../../../core/app_config.dart';
import '../../domain/entities/prayer_times.dart';
import '../../domain/usecases/get_prayer_times.dart';
import '../../domain/usecases/cache_prayer_times.dart';
import '../../../notifications/presentation/bloc/notification_bloc.dart';
import '../../../settings/presentation/bloc/settings_bloc.dart';
import '../../../notifications/data/datasources/notification_datasource.dart';
import '../../../notifications/data/datasources/notification_settings_datasource.dart';

part 'prayer_times_event.dart';
part 'prayer_times_state.dart';

class PrayerTimesBloc extends Bloc<PrayerTimesEvent, PrayerTimesState> {
  final GetPrayerTimes getPrayerTimes;
  final CachePrayerTimes cachePrayerTimes;

  PrayerTimesBloc({
    required this.getPrayerTimes,
    required this.cachePrayerTimes,
  }) : super(PrayerTimesInitial()) {
    on<LoadPrayerTimes>(_onLoadPrayerTimes);
    on<RefreshPrayerTimes>(_onRefreshPrayerTimes);
    on<CacheMonthlyPrayerTimes>(_onCacheMonthlyPrayerTimes);
    on<UpdateLocation>(_onUpdateLocation);
    on<UpdateCalculationMethod>(_onUpdateCalculationMethod);
  }

  Future<void> _onLoadPrayerTimes(
    LoadPrayerTimes event,
    Emitter<PrayerTimesState> emit,
  ) async {
    emit(PrayerTimesLoading());

    final result = await getPrayerTimes(GetPrayerTimesParams(
      date: event.date,
      latitude: event.latitude,
      longitude: event.longitude,
      calculationMethod: event.calculationMethod,
    ));

    result.fold(
      (failure) => emit(PrayerTimesError(message: failure.message)),
      (prayerTimes) {
        // Automatically schedule notifications when prayer times are loaded
        _scheduleNotifications(prayerTimes, event);
        
        emit(PrayerTimesLoaded(
          prayerTimes: prayerTimes,
          latitude: event.latitude,
          longitude: event.longitude,
          calculationMethod: event.calculationMethod,
        ));
      },
    );
  }

  Future<void> _onRefreshPrayerTimes(
    RefreshPrayerTimes event,
    Emitter<PrayerTimesState> emit,
  ) async {
    if (state is PrayerTimesLoaded) {
      final currentState = state as PrayerTimesLoaded;
      emit(PrayerTimesRefreshing(
        prayerTimes: currentState.prayerTimes,
        latitude: currentState.latitude,
        longitude: currentState.longitude,
        calculationMethod: currentState.calculationMethod,
      ));

      final result = await getPrayerTimes(GetPrayerTimesParams(
        date: event.date ?? DateTime.now(),
        latitude: currentState.latitude,
        longitude: currentState.longitude,
        calculationMethod: currentState.calculationMethod,
      ));

      result.fold(
        (failure) => emit(PrayerTimesError(message: failure.message)),
        (prayerTimes) {
          // Automatically schedule notifications when prayer times are refreshed
          _scheduleNotifications(prayerTimes, LoadPrayerTimes(
            date: event.date ?? DateTime.now(),
            latitude: currentState.latitude,
            longitude: currentState.longitude,
            calculationMethod: currentState.calculationMethod,
          ));
          
          emit(PrayerTimesLoaded(
            prayerTimes: prayerTimes,
            latitude: currentState.latitude,
            longitude: currentState.longitude,
            calculationMethod: currentState.calculationMethod,
          ));
        },
      );
    }
  }

  // Helper method to schedule notifications
  void _scheduleNotifications(PrayerTimes prayerTimes, LoadPrayerTimes event) {
    try {
      // Access the notification bloc and settings bloc through GetIt or context
      // For now, we'll use a simple approach by accessing through the global context
      // In a real app, you might want to inject these dependencies
      
      // Get the settings bloc to check if notifications are enabled
      final settingsBloc = GetIt.instance<SettingsBloc>();
      final settingsState = settingsBloc.state;
      
      // Only schedule notifications if they are enabled in settings
      if (settingsState.notificationsEnabled) {
        // Get the notification data source
        final notificationDataSource = GetIt.instance<NotificationDataSource>();
        final notificationSettingsDataSource = GetIt.instance<NotificationSettingsDatasource>();
        
        // Schedule selective notifications based on user settings
        notificationDataSource.scheduleSelectivePrayerNotifications(
          prayerTimes,
          settingsState.adhanEnabled,
          notificationSettingsDataSource,
        );
      }
    } catch (e) {
      // Silently handle notification scheduling errors
      print('Failed to schedule notifications: $e');
    }
  }

  Future<void> _onCacheMonthlyPrayerTimes(
    CacheMonthlyPrayerTimes event,
    Emitter<PrayerTimesState> emit,
  ) async {
    if (state is PrayerTimesLoaded) {
      final currentState = state as PrayerTimesLoaded;
      
      final result = await cachePrayerTimes(CachePrayerTimesParams(
        month: event.month,
        latitude: currentState.latitude,
        longitude: currentState.longitude,
        calculationMethod: currentState.calculationMethod,
      ));

      result.fold(
        (failure) {
          // Don't emit error for caching failure, just log it
          // The app should continue working even if caching fails
        },
        (cachedPrayerTimes) {
          // Successfully cached, no need to change state
        },
      );
    }
  }

  Future<void> _onUpdateLocation(
    UpdateLocation event,
    Emitter<PrayerTimesState> emit,
  ) async {
    if (state is PrayerTimesLoaded) {
      final currentState = state as PrayerTimesLoaded;
      
      emit(PrayerTimesLoading());

      final result = await getPrayerTimes(GetPrayerTimesParams(
        date: DateTime.now(),
        latitude: event.latitude,
        longitude: event.longitude,
        calculationMethod: currentState.calculationMethod,
      ));

      result.fold(
        (failure) => emit(PrayerTimesError(message: failure.message)),
        (prayerTimes) => emit(PrayerTimesLoaded(
          prayerTimes: prayerTimes,
          latitude: event.latitude,
          longitude: event.longitude,
          calculationMethod: currentState.calculationMethod,
        )),
      );
    }
  }

  Future<void> _onUpdateCalculationMethod(
    UpdateCalculationMethod event,
    Emitter<PrayerTimesState> emit,
  ) async {
    if (state is PrayerTimesLoaded) {
      final currentState = state as PrayerTimesLoaded;
      
      emit(PrayerTimesLoading());

      final result = await getPrayerTimes(GetPrayerTimesParams(
        date: DateTime.now(),
        latitude: currentState.latitude,
        longitude: currentState.longitude,
        calculationMethod: event.calculationMethod,
      ));

      result.fold(
        (failure) => emit(PrayerTimesError(message: failure.message)),
        (prayerTimes) => emit(PrayerTimesLoaded(
          prayerTimes: prayerTimes,
          latitude: currentState.latitude,
          longitude: currentState.longitude,
          calculationMethod: event.calculationMethod,
        )),
      );
    }
  }
}

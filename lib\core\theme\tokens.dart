import 'package:flutter/material.dart';

/// Central color tokens for the Cream & Gold theme system
/// Provides all colors for light and dark modes without hard-coded values in UI
class ColorTokens {
  // Private constructor to prevent instantiation
  ColorTokens._();

  /// Light Theme Colors
  static const Color lightBG = Color(0xFFFAF7F1);
  static const Color lightSurface = Color(0xFFFFFCF6);
  static const Color lightPrimaryTaupe = Color(0xFF8A7B68);
  static const Color lightGold = Color(0xFFC7A351);
  static const Color lightGoldSoft = Color(0xFFE3C972);
  static const Color lightText = Color(0xFF2C2A27);
  static const Color lightTextMuted = Color(0xFF7A746B);
  static const Color lightDivider = Color(0xFFE8E2D8);
  static const Color lightSurfaceVariant = Color(0xFFF1ECE2);
  static const Color lightOutline = Color(0xFFCFC7BA);

  /// Dark Theme Colors
  static const Color darkBG = Color(0xFF151311);
  static const Color darkSurface = Color(0xFF1C1916);
  static const Color darkPrimaryGold = Color(0xFFDCC79A);
  static const Color darkGold = Color(0xFFCFAF56);
  static const Color darkGoldDeep = Color(0xFFB5922F);
  static const Color darkText = Color(0xFFEDE9E3);
  static const Color darkTextMuted = Color(0xFFA59D90);
  static const Color darkDivider = Color(0xFF2B2620);
  static const Color darkSurfaceVariant = Color(0xFF2B2620);
  static const Color darkOutline = Color(0xFF5A5044);

  /// Classic Theme Colors (Updated darker green for Design 1)
  static const Color classicLightPrimary = Color(0xFF1B5E20); // Darker green as requested
  static const Color classicLightSecondary = Color(0xFF2E7D32);
  static const Color classicLightAccent = Color(0xFFFF8F00);
  static const Color classicLightSurface = Color(0xFFF8F9FA);
  static const Color classicLightCard = Color(0xFFFFFFFF);
  static const Color classicLightDivider = Color(0xFFE0E0E0);
  static const Color classicLightText = Color(0xFF212121);
  static const Color classicLightTextSecondary = Color(0xFF757575);

  /// Classic Dark Colors
  static const Color classicDarkPrimary = Color(0xFF66BB6A);
  static const Color classicDarkSecondary = Color(0xFF81C784);
  static const Color classicDarkAccent = Color(0xFFFFB74D);
  static const Color classicDarkSurface = Color(0xFF121212);
  static const Color classicDarkCard = Color(0xFF1E1E1E);

  /// Special Colors
  static const Color ramadanBadgeLight = Color(0xFFC7A351); // Gold for Ramadan badge
  static const Color ramadanBadgeText = Color(0xFF1A1405); // Dark text for badge
  static const Color snackBarLight = Color(0xFF2C2A27);
  static const Color snackBarDark = Color(0xFFEDE9E3);

  /// Print/PDF Colors (No opacity)
  static const Color printBackground = Color(0xFFFFFFFF);
  static const Color printText = Color(0xFF2C2A27);
  static const Color printAccent = Color(0xFFC7A351);
  static const Color printTableLines = Color(0xFFD0D5DD);
  static const Color printZebraLight = Color(0xFFF6F7F9);

  /// Glassmorphism Colors
  static const Color glassLightFill = Color.fromRGBO(255, 255, 255, 0.15);
  static const Color glassDarkFill = Color.fromRGBO(255, 255, 255, 0.08);
  static const Color glassBorder = Color.fromRGBO(255, 255, 255, 0.30);

  /// Helper methods to get colors based on brightness
  static Color getBGColor(bool isDark) {
    return isDark ? darkBG : lightBG;
  }

  static Color getSurfaceColor(bool isDark) {
    return isDark ? darkSurface : lightSurface;
  }

  static Color getPrimaryColor(bool isDark) {
    return isDark ? darkPrimaryGold : lightPrimaryTaupe;
  }

  static Color getGoldColor(bool isDark) {
    return isDark ? darkGold : lightGold;
  }

  static Color getTextColor(bool isDark) {
    return isDark ? darkText : lightText;
  }

  static Color getTextMutedColor(bool isDark) {
    return isDark ? darkTextMuted : lightTextMuted;
  }

  static Color getDividerColor(bool isDark) {
    return isDark ? darkDivider : lightDivider;
  }

  static Color getSurfaceVariantColor(bool isDark) {
    return isDark ? darkSurfaceVariant : lightSurfaceVariant;
  }

  static Color getOutlineColor(bool isDark) {
    return isDark ? darkOutline : lightOutline;
  }

  /// Classic theme helpers
  static Color getClassicPrimaryColor(bool isDark) {
    return isDark ? classicDarkPrimary : classicLightPrimary;
  }

  static Color getClassicSurfaceColor(bool isDark) {
    return isDark ? classicDarkSurface : classicLightSurface;
  }

  static Color getClassicTextColor(bool isDark) {
    return isDark ? darkText : classicLightText;
  }
}
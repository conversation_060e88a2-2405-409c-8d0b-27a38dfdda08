import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/l10n/app_localizations.dart';
import '../../../../core/theme/design_tokens.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../../domain/entities/qibla_direction.dart';
import '../bloc/qibla_bloc.dart';
import '../widgets/qibla_compass_widget.dart';
import '../widgets/calibration_guide_widget.dart';
import '../../data/services/compass_sensor_service.dart';

class QiblaCompassPage extends StatefulWidget {
  const QiblaCompassPage({super.key});

  @override
  State<QiblaCompassPage> createState() => _QiblaCompassPageState();
}

class _QiblaCompassPageState extends State<QiblaCompassPage> {
  @override
  void initState() {
    super.initState();
    // Start compass when page loads
    context.read<QiblaBloc>().add(const StartQiblaCompass());
  }

  @override
  void dispose() {
    // Stop compass when page is disposed
    context.read<QiblaBloc>().add(const StopQiblaCompass());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.qiblaCompass),
        backgroundColor: DesignTokens.getCardColor(context),
        foregroundColor: DesignTokens.getTextColor(context),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<QiblaBloc>().add(const CalibrateCompass());
            },
            tooltip: l10n.calibrateCompass,
          ),
          // Compass Mode Button
          BlocBuilder<QiblaBloc, QiblaState>(
            builder: (context, qiblaState) => IconButton(
              icon: Icon(
                Icons.explore,
                color: qiblaState.mode == QiblaMode.compass
                  ? Theme.of(context).colorScheme.primary
                  : null,
              ),
              onPressed: () {
                context.read<QiblaBloc>().add(const SwitchQiblaMode(QiblaMode.compass));
              },
              tooltip: l10n.compassMode,
            ),
          ),
          // Map Mode Button
          BlocBuilder<QiblaBloc, QiblaState>(
            builder: (context, qiblaState) => IconButton(
              icon: Icon(
                Icons.map,
                color: qiblaState.mode == QiblaMode.mapFallback
                  ? Theme.of(context).colorScheme.primary
                  : null,
              ),
              onPressed: () {
                context.read<QiblaBloc>().add(const SwitchQiblaMode(QiblaMode.mapFallback));
              },
              tooltip: l10n.mapMode,
            ),
          ),
          // Manual Location Button
          IconButton(
            icon: const Icon(Icons.edit_location),
            onPressed: _showManualLocationDialog,
            tooltip: l10n.setManualLocation,
          ),
        ],
      ),
      backgroundColor: DesignTokens.getSurfaceColor(context),
      body: BlocBuilder<QiblaBloc, QiblaState>(
        builder: (context, state) {
          if (state.isLoading) {
            return _buildLoadingView(l10n);
          }

          if (state.error != null) {
            return _buildErrorView(l10n, state.error!);
          }

          if (state.qiblaDirection == null) {
            return _buildInitialView(l10n);
          }

          return _buildCompassView(state.qiblaDirection!, l10n);
        },
      ),
    );
  }

  Widget _buildLoadingView(AppLocalizations l10n) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            l10n.initializingCompass,
            style: DesignTokens.bodyLarge.copyWith(
              color: DesignTokens.getTextColor(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(AppLocalizations l10n, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.withOpacity(0.7),
            ),
            const SizedBox(height: 16),
            Text(
              l10n.compassError,
              style: DesignTokens.titleLarge.copyWith(
                color: DesignTokens.getTextColor(context),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: DesignTokens.bodyMedium.copyWith(
                color: DesignTokens.getTextColor(context, secondary: true),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                context.read<QiblaBloc>().add(const StartQiblaCompass());
              },
              icon: const Icon(Icons.refresh),
              label: Text(l10n.tryAgain),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                context.read<QiblaBloc>().add(const RequestLocationPermission());
              },
              child: Text(l10n.requestPermission),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInitialView(AppLocalizations l10n) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.explore,
              size: 64,
              color: DesignTokens.lightPrimary,
            ),
            const SizedBox(height: 16),
            Text(
              l10n.qiblaCompass,
              style: DesignTokens.titleLarge.copyWith(
                color: DesignTokens.getTextColor(context),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              l10n.qiblaCompassDescription,
              style: DesignTokens.bodyMedium.copyWith(
                color: DesignTokens.getTextColor(context, secondary: true),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                context.read<QiblaBloc>().add(const StartQiblaCompass());
              },
              icon: const Icon(Icons.play_arrow),
              label: Text(l10n.startCompass),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompassView(QiblaDirection qiblaDirection, AppLocalizations l10n) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesignTokens.spacingM),
      child: Column(
        children: [
          // Status message
          _buildStatusMessage(qiblaDirection, l10n),
          const SizedBox(height: DesignTokens.spacingM),

          // Compass widget
          Center(
            child: QiblaCompassWidget(
              qiblaDirection: qiblaDirection,
              size: 300,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingL),

          // Information cards
          _buildInformationCards(qiblaDirection, l10n),
          const SizedBox(height: DesignTokens.spacingM),

          // Enhanced calibration guide (if needed)
          if (qiblaDirection.compassStatus == CompassStatus.calibrating ||
              qiblaDirection.compassStatus == CompassStatus.disturbed)
            _buildEnhancedCalibrationGuide(qiblaDirection, l10n),
        ],
      ),
    );
  }

  Widget _buildStatusMessage(QiblaDirection qiblaDirection, AppLocalizations l10n) {
    String message;
    Color color;
    IconData icon;

    switch (qiblaDirection.compassStatus) {
      case CompassStatus.ok:
        message = l10n.compassReady;
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case CompassStatus.calibrating:
        message = '${l10n.compassCalibrating} - Please move your device in a figure-8 pattern';
        color = Colors.orange;
        icon = Icons.sync;
        break;
      case CompassStatus.disturbed:
        message = l10n.compassDisturbed;
        color = Colors.red;
        icon = Icons.warning;
        break;
      case CompassStatus.notAvailable:
        message = l10n.compassNotAvailable;
        color = Colors.grey;
        icon = Icons.error;
        break;
      case CompassStatus.permissionDenied:
        message = l10n.compassPermissionDenied;
        color = Colors.red;
        icon = Icons.location_disabled;
        break;
    }

    return GlassmorphismCard(
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: DesignTokens.bodyMedium.copyWith(
                color: DesignTokens.getTextColor(context),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInformationCards(QiblaDirection qiblaDirection, AppLocalizations l10n) {
    return Row(
      children: [
        Expanded(
          child: GlassmorphismCard(
            child: Column(
              children: [
                Icon(
                  Icons.straighten,
                  color: DesignTokens.lightPrimary,
                  size: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  l10n.distance,
                  style: DesignTokens.labelMedium.copyWith(
                    color: DesignTokens.getTextColor(context, secondary: true),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${qiblaDirection.distanceKm.toStringAsFixed(0)} km',
                  style: DesignTokens.titleMedium.copyWith(
                    color: DesignTokens.getTextColor(context),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: DesignTokens.spacingM),
        Expanded(
          child: GlassmorphismCard(
            child: Column(
              children: [
                Icon(
                  Icons.navigation,
                  color: DesignTokens.lightPrimary,
                  size: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  l10n.bearing,
                  style: DesignTokens.labelMedium.copyWith(
                    color: DesignTokens.getTextColor(context, secondary: true),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${qiblaDirection.bearingTrue.toStringAsFixed(0)}°',
                  style: DesignTokens.titleMedium.copyWith(
                    color: DesignTokens.getTextColor(context),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedCalibrationGuide(QiblaDirection qiblaDirection, AppLocalizations l10n) {
    // Create a mock CompassReading for the calibration widget
    // In a real implementation, this would come from the compass sensor
    final mockReading = CompassReading(
      heading: qiblaDirection.deviceHeading,
      accuracy: 15.0, // Default accuracy estimate
      status: qiblaDirection.compassStatus,
      rawHeading: qiblaDirection.deviceHeading,
      pitch: 0.0,
      roll: 0.0,
      magneticStrength: 45.0, // Typical value
      calibrationQuality: 0, // Will be updated by real sensor
      calibrationPhase: CalibrationPhase.preparation,
    );

    return CalibrationGuideWidget(
      compassReading: mockReading,
      onCalibrationComplete: () {
        // Show completion message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('✅ Calibration completed successfully!'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );
      },
    );
  }

  Widget _buildCalibrationInstructions(AppLocalizations l10n) {
    return GlassmorphismCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.orange,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                l10n.calibrationNeeded,
                style: DesignTokens.titleSmall.copyWith(
                  color: DesignTokens.getTextColor(context),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            l10n.calibrationInstructions,
            style: DesignTokens.bodyMedium.copyWith(
              color: DesignTokens.getTextColor(context, secondary: true),
            ),
          ),
          const SizedBox(height: 12),
          // Add visual representation of calibration movement
          Container(
            height: 100,
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.withOpacity(0.3)),
            ),
            child: CustomPaint(
              painter: Figure8GuidePainter(),
              size: const Size(double.infinity, 100),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Hold your device steady and move it in a figure-8 pattern as shown above. '
            'Keep the device flat and make 2-3 complete movements. '
            'The compass will automatically calibrate when complete.',
            style: DesignTokens.bodySmall.copyWith(
              color: DesignTokens.getTextColor(context, secondary: true),
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(height: 12),
          ElevatedButton.icon(
            onPressed: () {
              context.read<QiblaBloc>().add(const CalibrateCompass());
              // Show enhanced feedback
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Row(
                    children: [
                      Icon(Icons.refresh, color: Colors.white),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '🔄 Calibration started! Move device in figure-8 pattern',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ],
                  ),
                  duration: const Duration(seconds: 5),
                  backgroundColor: Colors.orange,
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            icon: const Icon(Icons.refresh),
            label: Text(l10n.calibrateNow),
          ),
        ],
      ),
    );
  }

  void _showManualLocationDialog() {
    final latController = TextEditingController();
    final lonController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Set Manual Location'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: latController,
              decoration: const InputDecoration(
                labelText: 'Latitude',
                hintText: 'e.g., 37.7749',
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: lonController,
              decoration: const InputDecoration(
                labelText: 'Longitude',
                hintText: 'e.g., -122.4194',
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final lat = double.tryParse(latController.text);
              final lon = double.tryParse(lonController.text);
              
              if (lat != null && lon != null &&
                  lat >= -90 && lat <= 90 &&
                  lon >= -180 && lon <= 180) {
                context.read<QiblaBloc>().add(
                  UpdateUserLocation(
                    latitude: lat,
                    longitude: lon,
                    isManual: true,
                  ),
                );
                Navigator.of(context).pop();
                
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Manual location set successfully'),
                    duration: Duration(seconds: 2),
                  ),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please enter valid coordinates'),
                    backgroundColor: Colors.red,
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            },
            child: const Text('Set Location'),
          ),
        ],
      ),
    );
  }
}

/// Custom painter for figure-8 calibration guide
class Figure8GuidePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.orange
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 6;

    // Draw figure-8 path
    final path = Path();

    // Left circle of figure-8
    final leftCenter = Offset(center.dx - radius, center.dy);
    path.addOval(Rect.fromCircle(center: leftCenter, radius: radius));

    // Right circle of figure-8
    final rightCenter = Offset(center.dx + radius, center.dy);
    path.addOval(Rect.fromCircle(center: rightCenter, radius: radius));

    canvas.drawPath(path, paint);

    // Draw arrow to show direction
    final arrowPaint = Paint()
      ..color = Colors.orange
      ..strokeWidth = 2
      ..style = PaintingStyle.fill;

    // Arrow at top of left circle
    final arrowPath = Path();
    final arrowStart = Offset(leftCenter.dx, leftCenter.dy - radius);
    arrowPath.moveTo(arrowStart.dx, arrowStart.dy);
    arrowPath.lineTo(arrowStart.dx - 8, arrowStart.dy - 12);
    arrowPath.lineTo(arrowStart.dx + 8, arrowStart.dy - 12);
    arrowPath.close();

    canvas.drawPath(arrowPath, arrowPaint);

    // Draw text instructions
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'Move device in this pattern',
        style: TextStyle(
          color: Colors.orange,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        size.height - textPainter.height - 5,
      ),
    );
  }

  @override
  bool shouldRepaint(Figure8GuidePainter oldDelegate) => false;
}

// Add a custom painter for the calibration guide
class CalibrationGuidePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.orange
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final path = Path();
    
    // Draw a figure-8 pattern
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final radius = size.height / 3;
    
    // Top circle
    path.moveTo(centerX, centerY - radius / 2);
    path.cubicTo(
      centerX + radius, centerY - radius / 2,
      centerX + radius, centerY + radius / 2,
      centerX, centerY + radius / 2,
    );
    
    // Bottom circle
    path.cubicTo(
      centerX - radius, centerY + radius / 2,
      centerX - radius, centerY - radius / 2,
      centerX, centerY - radius / 2,
    );
    
    canvas.drawPath(path, paint);
    
    // Draw arrow to indicate movement direction
    final arrowPaint = Paint()
      ..color = Colors.orange
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
      
    // Simple arrow at the start point
    canvas.drawLine(
      Offset(centerX + 5, centerY - radius / 2 - 5),
      Offset(centerX, centerY - radius / 2),
      arrowPaint,
    );
    canvas.drawLine(
      Offset(centerX - 5, centerY - radius / 2 - 5),
      Offset(centerX, centerY - radius / 2),
      arrowPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import 'core/app_config.dart';
import 'core/dependency_injection.dart';
import 'core/theme/app_theme.dart';
import 'core/theme/app_themes.dart';
import 'core/l10n/app_localizations.dart';
import 'core/models/prayer_notification_settings.dart';
import 'core/services/location_permission_service.dart';
import 'core/widgets/offline_indicator.dart';
import 'features/prayer_times/presentation/pages/home_page.dart';
import 'features/prayer_times/presentation/bloc/prayer_times_bloc.dart';
import 'features/prayer_times/data/models/prayer_times_model.dart';
import 'features/prayer_times/domain/repositories/prayer_times_repository.dart';
import 'features/settings/presentation/bloc/settings_bloc.dart';
import 'features/location/presentation/bloc/location_bloc.dart';
import 'features/notifications/presentation/bloc/notification_bloc.dart';
import 'features/notifications/data/datasources/notification_datasource.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Hive
  await Hive.initFlutter();

  // Register Hive adapters
  Hive.registerAdapter(PrayerTimesModelAdapter());
  Hive.registerAdapter(PrayerNotificationSettingsAdapter());

  // Open Hive boxes
  await Hive.openBox<PrayerTimesModel>('prayer_times');
  await Hive.openBox<PrayerNotificationSettings>('prayer_notifications');
  
  // Initialize timezone
  tz.initializeTimeZones();
  
  // Initialize notifications
  await _initializeNotifications();
  
  // Initialize dependency injection
  await DependencyInjection.init();
  
  runApp(const GebetApp());
}

Future<void> _initializeNotifications() async {
  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('@mipmap/ic_launcher');

  const DarwinInitializationSettings initializationSettingsIOS =
      DarwinInitializationSettings(
    requestAlertPermission: true,
    requestBadgePermission: true,
    requestSoundPermission: true,
  );

  const InitializationSettings initializationSettings =
      InitializationSettings(
    android: initializationSettingsAndroid,
    iOS: initializationSettingsIOS,
  );

  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: (NotificationResponse response) {
      // Handle notification tap
      print('Notification tapped: ${response.payload}');
    },
  );

  // Create notification channels for Android
  final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
      flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>();

  if (androidPlugin != null) {
    // High priority channel for prayer times
    const AndroidNotificationChannel prayerChannel = AndroidNotificationChannel(
      'prayer_times',
      'Prayer Times',
      description: 'Notifications for prayer times with Adhan sound',
      importance: Importance.max,
      enableVibration: true,
      playSound: true,
      showBadge: true,
      enableLights: true,
    );

    // Create the channel
    await androidPlugin.createNotificationChannel(prayerChannel);

    // Create additional channel for immediate notifications (fallback)
    const AndroidNotificationChannel immediateChannel = AndroidNotificationChannel(
      'prayer_times_immediate',
      'Prayer Times (Immediate)',
      description: 'Immediate prayer time notifications when countdown reaches zero',
      importance: Importance.max,
      enableVibration: true,
      playSound: true,
      showBadge: true,
      enableLights: true,
    );

    await androidPlugin.createNotificationChannel(immediateChannel);

    // Request exact alarm permission for Android 12+
    try {
      final hasExactAlarmPermission = await androidPlugin.requestExactAlarmsPermission();
      print('Exact alarm permission: $hasExactAlarmPermission');
    } catch (e) {
      print('Failed to request exact alarm permission: $e');
    }

    // Request notification permission
    try {
      final hasNotificationPermission = await androidPlugin.requestNotificationsPermission();
      print('Notification permission: $hasNotificationPermission');
    } catch (e) {
      print('Failed to request notification permission: $e');
    }
  }
}

class GebetApp extends StatelessWidget {
  const GebetApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        BlocProvider<PrayerTimesBloc>(
          create: (context) => DependencyInjection.instance<PrayerTimesBloc>(),
        ),
        BlocProvider<SettingsBloc>(
          create: (context) => DependencyInjection.instance<SettingsBloc>()
            ..add(LoadSettings()),
        ),
        BlocProvider<LocationBloc>(
          create: (context) => DependencyInjection.instance<LocationBloc>(),
        ),
        BlocProvider<NotificationBloc>(
          create: (context) => DependencyInjection.instance<NotificationBloc>(),
        ),
        Provider<PrayerTimesRepository>(
          create: (context) => DependencyInjection.instance<PrayerTimesRepository>(),
        ),
      ],
      child: BlocBuilder<SettingsBloc, SettingsState>(
        builder: (context, settingsState) {
          // Determine if dark mode should be used
          final brightness = MediaQuery.of(context).platformBrightness;
          final isDark = settingsState.themeMode == ThemeMode.dark ||
              (settingsState.themeMode == ThemeMode.system &&
                  brightness == Brightness.dark);
          
          return MaterialApp(
            title: 'AlFalah - Prayer Times',
            debugShowCheckedModeBanner: false,
            theme: getAppTheme(settingsState.appDesign, context, isDark: false),
            darkTheme: getAppTheme(settingsState.appDesign, context, isDark: true),
            themeMode: settingsState.themeMode,
            locale: settingsState.locale,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: AppConfig.supportedLocales,
            // Enable RTL support for Arabic
            builder: (context, child) {
              return Directionality(
                textDirection: settingsState.locale.languageCode == 'ar'
                    ? TextDirection.rtl
                    : TextDirection.ltr,
                child: child!,
              );
            },
            home: const OfflineIndicator(
              child: AppStartupWrapper(),
            ),
          );
        },
      ),
    );
  }
}

/// Wrapper widget that handles app startup tasks like location permissions
class AppStartupWrapper extends StatefulWidget {
  const AppStartupWrapper({super.key});

  @override
  State<AppStartupWrapper> createState() => _AppStartupWrapperState();
}

class _AppStartupWrapperState extends State<AppStartupWrapper> {
  bool _hasCheckedPermissions = false;

  @override
  void initState() {
    super.initState();
    // Delay permission check to ensure the widget tree is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkLocationPermissions();
    });
  }

  Future<void> _checkLocationPermissions() async {
    if (_hasCheckedPermissions) return;
    _hasCheckedPermissions = true;

    try {
      // Check location permissions
      final shouldRequest = await LocationPermissionService.shouldRequestPermissionAtStartup();
      if (shouldRequest && mounted) {
        await LocationPermissionService.requestLocationPermissionAtStartup(context);
      }
      
      // Also request notification permissions for prayer times
      await _requestNotificationPermissions();
    } catch (e) {
      // Silently handle permission errors - app should still work
      debugPrint('Permission error: $e');
    }
  }
  
  Future<void> _requestNotificationPermissions() async {
    try {
      final notificationDataSource = DependencyInjection.instance.isRegistered<NotificationDataSource>()
          ? DependencyInjection.instance<NotificationDataSource>()
          : null;
      
      if (notificationDataSource != null) {
        final granted = await notificationDataSource.requestPermissions();
        if (granted) {
          debugPrint('Notification permissions granted successfully');
        } else {
          debugPrint('Notification permissions denied');
        }
      }
    } catch (e) {
      debugPrint('Failed to request notification permissions: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return const HomePage();
  }
}

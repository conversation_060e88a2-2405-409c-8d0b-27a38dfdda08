import 'package:flutter/material.dart';
import 'dart:async';
import 'package:audioplayers/audioplayers.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../../../../core/app_config.dart';
import '../../../../core/l10n/app_localizations.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../../domain/entities/prayer_times.dart';
import '../../../notifications/data/datasources/notification_datasource.dart';
import '../../../notifications/data/datasources/notification_settings_datasource.dart';
import '../../../settings/presentation/bloc/settings_bloc.dart';

class NextPrayerCard extends StatefulWidget {
  final PrayerTimes prayerTimes;

  const NextPrayerCard({
    super.key,
    required this.prayerTimes,
  });

  @override
  State<NextPrayerCard> createState() => _NextPrayerCardState();
}

class _NextPrayerCardState extends State<NextPrayerCard> {
  Timer? _timer;
  Duration? _timeRemaining;
  PrayerTime? _currentNextPrayer;
  bool _notificationTriggered = false;
  final AudioPlayer _audioPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _updateTimeRemaining();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateTimeRemaining();
      _checkAndTriggerNotification();
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }

  void _updateTimeRemaining() {
    setState(() {
      _timeRemaining = widget.prayerTimes.getTimeUntilNextPrayer();
      _currentNextPrayer = widget.prayerTimes.getNextPrayer();
    });
  }

  void _checkAndTriggerNotification() {
    // Only trigger notification when countdown reaches zero and hasn't been triggered yet
    if (_timeRemaining != null &&
        _timeRemaining!.inSeconds <= 0 &&
        !_notificationTriggered &&
        _currentNextPrayer != null) {

      print('🔔 COUNTDOWN REACHED ZERO! Triggering immediate notification for ${_currentNextPrayer!.name}');
      _notificationTriggered = true;
      _triggerImmediateNotification(_currentNextPrayer!);
    }

    // Debug output every 30 seconds
    if (_timeRemaining != null && _timeRemaining!.inSeconds % 30 == 0 && _timeRemaining!.inSeconds > 0) {
      print('⏰ Time remaining until ${_currentNextPrayer?.name}: ${_timeRemaining!.inMinutes}m ${_timeRemaining!.inSeconds % 60}s');
    }
  }

  void _triggerImmediateNotification(PrayerTime prayer) async {
    try {
      // Get settings to check if notifications and adhan are enabled
      final settingsBloc = GetIt.instance<SettingsBloc>();
      final settingsState = settingsBloc.state;

      // Check if notifications are enabled for this specific prayer time
      final notificationSettingsDataSource = GetIt.instance<NotificationSettingsDatasource>();
      final prayerId = _getPrayerId(prayer.name);
      final isPrayerNotificationEnabled = notificationSettingsDataSource.isNotificationEnabled(
        date: widget.prayerTimes.date,
        prayerId: prayerId,
      );

      print('🔔 Checking notification settings for ${prayer.name}:');
      print('   - Global notifications enabled: ${settingsState.notificationsEnabled}');
      print('   - Prayer-specific notification enabled: $isPrayerNotificationEnabled');
      print('   - Global adhan enabled: ${settingsState.adhanEnabled}');

      // Only trigger notification if both global and prayer-specific notifications are enabled
      if (settingsState.notificationsEnabled && isPrayerNotificationEnabled) {
        // Play adhan sound if enabled
        if (settingsState.adhanEnabled) {
          await _playAdhanSound(settingsState.adhanSound);
        }

        // Show immediate notification
        await _showImmediateNotification(prayer);
        print('✅ Notification and adhan triggered for ${prayer.name}');
      } else {
        print('❌ Notification skipped for ${prayer.name} - disabled in settings');
      }
    } catch (e) {
      print('Error triggering immediate notification: $e');
    }
  }

  Future<void> _playAdhanSound(String adhanSound) async {
    try {
      // Don't play if silent
      if (adhanSound == 'silent') {
        print('Silent mode - no adhan sound played');
        return;
      }

      // Map adhan sound setting to asset path
      String assetPath;
      switch (adhanSound) {
        case 'Adhan_1_short.mp3':
          assetPath = 'audio/Adhan_1_short.mp3';
          break;
        case 'Adhan_1_long.mp3':
          assetPath = 'audio/Adhan_1_long.mp3';
          break;
        case 'Adhan_2_short.mp3':
          assetPath = 'audio/Adhan_2_short.mp3';
          break;
        case 'Adhan_2_long.mp3':
          assetPath = 'audio/Adhan_2_long.mp3';
          break;
        case 'Adhan_3_short.mp3':
          assetPath = 'audio/Adhan_3_short.mp3';
          break;
        case 'Adhan_3_long.mp3':
          assetPath = 'audio/Adhan_3_long.mp3';
          break;
        default:
          assetPath = 'audio/Adhan_1_short.mp3'; // Default fallback
      }

      // Stop any currently playing sound
      await _audioPlayer.stop();

      // Play the adhan sound
      await _audioPlayer.play(AssetSource(assetPath));
      print('Playing adhan sound: $assetPath');

    } catch (e) {
      print('Error playing adhan sound: $e');
      // Try to play default system notification sound as fallback
      try {
        await _audioPlayer.play(AssetSource('audio/Adhan_1_short.mp3'));
      } catch (fallbackError) {
        print('Fallback adhan sound also failed: $fallbackError');
      }
    }
  }

  Future<void> _showImmediateNotification(PrayerTime prayer) async {
    try {
      // Use the global notification plugin from main.dart
      final FlutterLocalNotificationsPlugin notifications = FlutterLocalNotificationsPlugin();

      // Create an immediate notification
      const androidDetails = AndroidNotificationDetails(
        'prayer_times',
        'Prayer Times',
        channelDescription: 'Immediate prayer time notifications',
        importance: Importance.max,
        priority: Priority.high,
        showWhen: true,
        enableVibration: true,
        playSound: true,
        fullScreenIntent: true,
        category: AndroidNotificationCategory.alarm,
        visibility: NotificationVisibility.public,
        autoCancel: false,
        ongoing: false,
        silent: false,
        channelShowBadge: true,
        largeIcon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
      );

      const notificationDetails = NotificationDetails(android: androidDetails);

      // Show immediate notification
      await notifications.show(
        1000, // Special ID for immediate notifications
        'Prayer Time - ${prayer.name}',
        'It\'s time for ${prayer.name} prayer',
        notificationDetails,
      );

      print('Immediate notification shown for ${prayer.name}');
    } catch (e) {
      print('Error showing immediate notification: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final now = DateTime.now();
    final currentDate = DateTime(now.year, now.month, now.day);
    final prayerDate = DateTime(widget.prayerTimes.date.year, widget.prayerTimes.date.month, widget.prayerTimes.date.day);
    final nextPrayer = widget.prayerTimes.getNextPrayer();
    
    // Check if we're dealing with prayers from today and if they've passed
    final isToday = currentDate.isAtSameMomentAs(prayerDate);
    final todaysPrayersPassed = isToday && nextPrayer == null;
    
    // If all prayers for today have passed, we need to show tomorrow's first prayer (Fajr)
    if (todaysPrayersPassed) {
      final tomorrowDate = currentDate.add(const Duration(days: 1));
      return GlassmorphismCard(
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConfig.cardBorderRadius),
            color: Theme.of(context).colorScheme.primary,
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppConfig.largePadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      l10n.nextPrayer,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Icon(
                      Icons.wb_twilight, // Fajr icon for tomorrow
                      color: Theme.of(context).colorScheme.onPrimary,
                      size: 24,
                    ),
                  ],
                ),
                const SizedBox(height: AppConfig.smallPadding),
                Text(
                  l10n.fajr,
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConfig.smallPadding),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Tomorrow - ${_formatDate(tomorrowDate)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.8),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      'Check back tomorrow for prayer times',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    }
    
    if (nextPrayer == null) {
      return const SizedBox.shrink();
    }

    return GlassmorphismCard(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConfig.cardBorderRadius),
          color: Theme.of(context).colorScheme.primary,
          // No opacity - solid colors only as per specification
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.largePadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    l10n.nextPrayer,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Icon(
                    _getPrayerIcon(nextPrayer.name),
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: 24,
                  ),
                ],
              ),
              const SizedBox(height: AppConfig.smallPadding),
              Text(
                _getPrayerDisplayName(nextPrayer.name, l10n),
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppConfig.smallPadding),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isToday ? 'Today - ${_formatDate(currentDate)}' : _formatDate(prayerDate),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        nextPrayer.time,
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  if (_timeRemaining != null)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          l10n.timeRemaining,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.8),
                          ),
                        ),
                        Text(
                          _formatDuration(_timeRemaining!),
                          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                            color: _getAccentColor(context),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Get accent color based on current theme design
  Color _getAccentColor(BuildContext context) {
    // Use the theme's primary color for consistency
    // This ensures Emerald theme uses #0E5F4B in both light and dark mode
    return Theme.of(context).colorScheme.onPrimary;
  }

  IconData _getPrayerIcon(String prayerName) {
    switch (prayerName.toLowerCase()) {
      case 'fajr':
        return Icons.wb_twilight;
      case 'dhuhr':
        return Icons.wb_sunny;
      case 'asr':
        return Icons.wb_sunny_outlined;
      case 'maghrib':
        return Icons.wb_twilight;
      case 'isha':
        return Icons.nightlight;
      default:
        return Icons.access_time;
    }
  }

  String _getPrayerDisplayName(String prayerName, AppLocalizations l10n) {
    switch (prayerName.toLowerCase()) {
      case 'fajr':
        return l10n.fajr;
      case 'dhuhr':
        return l10n.dhuhr;
      case 'asr':
        return l10n.asr;
      case 'maghrib':
        return l10n.maghrib;
      case 'isha':
        return l10n.isha;
      default:
        return prayerName;
    }
  }

  String _getPrayerId(String prayerName) {
    return prayerName.toLowerCase();
  }

  String _formatDuration(Duration duration) {
    if (duration.isNegative) {
      return '00:00:00';
    }
    
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    return '${hours.toString().padLeft(2, '0')}:'
           '${minutes.toString().padLeft(2, '0')}:'
           '${seconds.toString().padLeft(2, '0')}';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final dateToCheck = DateTime(date.year, date.month, date.day);
    
    if (dateToCheck.isAtSameMomentAs(today)) {
      return 'Today';
    } else if (dateToCheck.isAtSameMomentAs(tomorrow)) {
      return 'Tomorrow';
    } else {
      // Use a simple format for other dates
      final months = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
      ];
      return '${months[date.month - 1]} ${date.day}';
    }
  }
}

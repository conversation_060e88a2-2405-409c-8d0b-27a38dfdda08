import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/app_config.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../bloc/settings_bloc.dart';

class LanguageSelector extends StatelessWidget {
  const LanguageSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return GlassmorphismContainer(
      forceTransparent: true,
      borderRadius: 20,
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'Select Language',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          BlocBuilder<SettingsBloc, SettingsState>(
            builder: (context, state) {
              return SizedBox(
                height: 400, // Fixed height to make it scrollable
                child: ListView.builder(
                  itemCount: AppConfig.supportedLocales.length,
                  itemBuilder: (context, index) {
                    final locale = AppConfig.supportedLocales[index];
                    final isSelected = state.locale == locale;
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Colors.grey[300],
                        child: Text(
                          _getLanguageFlag(locale),
                          style: const TextStyle(fontSize: 20),
                        ),
                      ),
                      title: Text(_getLanguageName(locale)),
                      subtitle: Text(_getLanguageNativeName(locale)),
                      trailing: isSelected
                          ? Icon(
                              Icons.check,
                              color: Theme.of(context).colorScheme.primary,
                            )
                          : null,
                      onTap: () {
                        context.read<SettingsBloc>().add(UpdateLocale(locale: locale));
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  String _getLanguageName(Locale locale) {
    switch (locale.languageCode) {
      case 'en':
        return 'English';
      case 'de':
        return 'German';
      case 'hr':
        return 'Croatian';
      case 'fr':
        return 'French';
      case 'ar':
        return 'Arabic';
      case 'es':
        return 'Spanish';
      case 'zh':
        return 'Chinese';
      case 'ru':
        return 'Russian';
      case 'ms':
        return 'Malaysian';
      case 'id':
        return 'Indonesian';
      case 'hi':
        return 'Hindi';
      default:
        return 'English';
    }
  }

  String _getLanguageNativeName(Locale locale) {
    switch (locale.languageCode) {
      case 'en':
        return 'English';
      case 'de':
        return 'Deutsch';
      case 'hr':
        return 'Hrvatski';
      case 'fr':
        return 'Français';
      case 'ar':
        return 'العربية';
      case 'es':
        return 'Español';
      case 'zh':
        return '中文';
      case 'ru':
        return 'Русский';
      case 'ms':
        return 'Bahasa Melayu';
      case 'id':
        return 'Bahasa Indonesia';
      case 'hi':
        return 'हिन्दी';
      default:
        return 'English';
    }
  }

  String _getLanguageFlag(Locale locale) {
    switch (locale.languageCode) {
      case 'en':
        return '🇺🇸';
      case 'de':
        return '🇩🇪';
      case 'hr':
        return '🇭🇷';
      case 'fr':
        return '🇫🇷';
      case 'ar':
        return '🇸🇦';
      case 'es':
        return '🇪🇸';
      case 'zh':
        return '🇨🇳';
      case 'ru':
        return '🇷🇺';
      case 'ms':
        return '🇲🇾';
      case 'id':
        return '🇮🇩';
      case 'hi':
        return '🇮🇳';
      default:
        return '🇺🇸';
    }
  }
}

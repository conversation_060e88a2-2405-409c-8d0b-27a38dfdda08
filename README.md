# Gebet - Prayer Times App

A modern Flutter application for prayer time calculations with offline support and multi-language features.

## Features

### ✅ Completed
- **Project Architecture**: Clean architecture with BLoC state management
- **Prayer Time Calculation**: Aladhan API integration with multiple calculation methods
- **Offline Caching**: SQLite/Hive database for month-ahead prayer time caching
- **Modern UI**: Clean, flat design with tasteful typography and glassy cards
- **Multi-language Support**: EN (default), DE, HR, FR, AR with RTL support
- **Calculation Methods**: Support for Umm al-Qura (default), MWL, Egyptian, ISNA, and more

### 🚧 In Progress
- Location Services (GPS + manual location)
- Interactive World Map
- Notification System
- User Customization Features
- App Assets Creation

## Project Structure

```
lib/
├── core/                     # Core functionality
│   ├── app_config.dart      # App configuration and constants
│   ├── dependency_injection.dart # DI setup
│   ├── theme/               # App theming
│   ├── l10n/               # Localization
│   ├── error/              # Error handling
│   └── usecases/           # Base use case classes
├── features/               # Feature modules
│   ├── prayer_times/       # Prayer times calculation
│   │   ├── data/          # Data layer (API, cache)
│   │   ├── domain/        # Business logic
│   │   └── presentation/  # UI layer (BLoC, widgets)
│   ├── location/          # Location services
│   ├── settings/          # App settings
│   └── notifications/     # Prayer notifications
└── main.dart              # App entry point
```

## Key Technologies

- **Flutter**: Cross-platform mobile development
- **BLoC**: State management
- **Hive**: Local database for caching
- **Dio**: HTTP client for API calls
- **Aladhan API**: Prayer times calculation service
- **Clean Architecture**: Separation of concerns

## API Integration

The app integrates with the Aladhan API (https://api.aladhan.com) for accurate prayer time calculations:

- **Coordinate-based**: Get prayer times using latitude/longitude
- **City-based**: Get prayer times using city and country
- **Address-based**: Get prayer times using full address
- **Monthly caching**: Fetch and cache entire months for offline use

## Calculation Methods

Supports multiple Islamic prayer time calculation methods:

1. University of Islamic Sciences, Karachi
2. Islamic Society of North America (ISNA)
3. Muslim World League (MWL)
4. **Umm al-Qura, Makkah** (Default)
5. Egyptian General Authority of Survey
6. Institute of Geophysics, University of Tehran
7. Gulf Region
8. Kuwait
9. Qatar
10. Majlis Ugama Islam Singapura, Singapore
11. Union Organization islamic de France
12. Diyanet İşleri Başkanlığı, Turkey
13. Spiritual Administration of Muslims of Russia

## Getting Started

### Prerequisites

- Flutter SDK (>=3.10.0)
- Dart SDK (>=3.0.0)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd gebet
```

2. Install dependencies:
```bash
flutter pub get
```

3. Generate Hive type adapters:
```bash
flutter packages pub run build_runner build
```

4. Run the app:
```bash
flutter run
```

## Development Status

This is an active development project. The core prayer time calculation functionality is complete and working. Additional features like location services, notifications, and user customization are being implemented.

## Contributing

This project follows clean architecture principles and uses BLoC for state management. When contributing:

1. Follow the existing project structure
2. Write tests for new features
3. Update documentation
4. Follow Flutter/Dart best practices

## License

[Add your license information here]

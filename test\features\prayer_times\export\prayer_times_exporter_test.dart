import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:gebet/features/prayer_times/domain/entities/prayer_times.dart';
import 'package:gebet/features/prayer_times/export/prayer_times_exporter.dart';

void main() {
  group('PrayerTimesExporter', () {
    late List<PrayerTimes> testPrayerTimes;
    
    setUp(() {
      // Create test data
      testPrayerTimes = [
        PrayerTimes(
          date: DateTime(2024, 1, 1),
          fajr: '05:30',
          dhuhr: '12:15',
          asr: '15:30',
          maghrib: '18:00',
          isha: '19:30',
        ),
        PrayerTimes(
          date: DateTime(2024, 1, 2),
          fajr: '05:31',
          dhuhr: '12:16',
          asr: '15:31',
          maghrib: '18:01',
          isha: '19:31',
        ),
        PrayerTimes(
          date: DateTime(2024, 1, 3),
          fajr: '05:32',
          dhuhr: '12:17',
          asr: '15:32',
          maghrib: '18:02',
          isha: '19:32',
        ),
      ];
    });
    
    group('PDF Generation', () {
      testWidgets('should generate PDF for today view', (WidgetTester tester) async {
        // Build a minimal app for context
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                return Scaffold(
                  body: ElevatedButton(
                    onPressed: () async {
                      try {
                        final pdf = await PrayerTimesExporter._generatePdf(
                          prayerTimesList: [testPrayerTimes.first],
                          viewType: 'today',
                          cityName: 'Test City',
                          hijriOffset: 0,
                          landscape: false,
                        );
                        
                        expect(pdf, isNotNull);
                        expect(pdf.document.pdfPageList.pages.length, equals(1));
                      } catch (e) {
                        fail('PDF generation failed: $e');
                      }
                    },
                    child: const Text('Test PDF'),
                  ),
                );
              },
            ),
          ),
        );
        
        await tester.tap(find.text('Test PDF'));
        await tester.pumpAndSettle();
      });
      
      testWidgets('should generate PDF for week view', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                return Scaffold(
                  body: ElevatedButton(
                    onPressed: () async {
                      try {
                        final pdf = await PrayerTimesExporter._generatePdf(
                          prayerTimesList: testPrayerTimes,
                          viewType: 'week',
                          cityName: 'Test City',
                          hijriOffset: 0,
                          landscape: false,
                        );
                        
                        expect(pdf, isNotNull);
                        expect(pdf.document.pdfPageList.pages.length, equals(1));
                      } catch (e) {
                        fail('PDF generation failed: $e');
                      }
                    },
                    child: const Text('Test PDF'),
                  ),
                );
              },
            ),
          ),
        );
        
        await tester.tap(find.text('Test PDF'));
        await tester.pumpAndSettle();
      });
      
      testWidgets('should generate landscape PDF', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                return Scaffold(
                  body: ElevatedButton(
                    onPressed: () async {
                      try {
                        final pdf = await PrayerTimesExporter._generatePdf(
                          prayerTimesList: testPrayerTimes,
                          viewType: 'month',
                          cityName: 'Test City',
                          hijriOffset: 0,
                          landscape: true,
                        );
                        
                        expect(pdf, isNotNull);
                        expect(pdf.document.pdfPageList.pages.length, equals(1));
                      } catch (e) {
                        fail('PDF generation failed: $e');
                      }
                    },
                    child: const Text('Test PDF'),
                  ),
                );
              },
            ),
          ),
        );
        
        await tester.tap(find.text('Test PDF'));
        await tester.pumpAndSettle();
      });
    });
    
    group('CSV Generation', () {
      test('should generate CSV content', () {
        final csvContent = PrayerTimesExporter._generateCsv(
          prayerTimesList: testPrayerTimes,
          hijriOffset: 0,
        );
        
        expect(csvContent, isNotNull);
        expect(csvContent, contains('Date,Hijri Date,Fajr,Dhuhr,Asr,Maghrib,Isha'));
        expect(csvContent, contains('2024-01-01'));
        expect(csvContent, contains('05:30'));
        expect(csvContent, contains('12:15'));
        expect(csvContent, contains('15:30'));
        expect(csvContent, contains('18:00'));
        expect(csvContent, contains('19:30'));
        
        // Check that all test data is included
        for (final prayerTimes in testPrayerTimes) {
          final dateStr = '${prayerTimes.date.year}-${prayerTimes.date.month.toString().padLeft(2, '0')}-${prayerTimes.date.day.toString().padLeft(2, '0')}';
          expect(csvContent, contains(dateStr));
          expect(csvContent, contains(prayerTimes.fajr));
          expect(csvContent, contains(prayerTimes.dhuhr));
          expect(csvContent, contains(prayerTimes.asr));
          expect(csvContent, contains(prayerTimes.maghrib));
          expect(csvContent, contains(prayerTimes.isha));
        }
      });
    });
    
    group('Filename Generation', () {
      test('should generate correct filename for single day', () {
        final filename = PrayerTimesExporter._generateFileName(
          cityName: 'Test City',
          viewType: 'today',
          startDate: DateTime(2024, 1, 1),
          endDate: DateTime(2024, 1, 1),
          extension: 'pdf',
        );
        
        expect(filename, equals('PrayerTimes_Test_City_2024-01-01.pdf'));
      });
      
      test('should generate correct filename for date range', () {
        final filename = PrayerTimesExporter._generateFileName(
          cityName: 'Test City',
          viewType: 'week',
          startDate: DateTime(2024, 1, 1),
          endDate: DateTime(2024, 1, 7),
          extension: 'csv',
        );
        
        expect(filename, equals('PrayerTimes_Test_City_2024-01-01_to_2024-01-07.csv'));
      });
      
      test('should clean city name with special characters', () {
        final filename = PrayerTimesExporter._generateFileName(
          cityName: 'München/Bayern',
          viewType: 'month',
          startDate: DateTime(2024, 1, 1),
          endDate: DateTime(2024, 1, 31),
          extension: 'pdf',
        );
        
        expect(filename, equals('PrayerTimes_MnchenBayern_2024-01-01_to_2024-01-31.pdf'));
      });
    });
    
    group('View Type Titles', () {
      test('should return correct titles for view types', () {
        expect(PrayerTimesExporter._getViewTypeTitle('today'), equals('Daily View'));
        expect(PrayerTimesExporter._getViewTypeTitle('week'), equals('Weekly View'));
        expect(PrayerTimesExporter._getViewTypeTitle('month'), equals('Monthly View'));
        expect(PrayerTimesExporter._getViewTypeTitle('unknown'), equals('Prayer Times'));
      });
    });
  });
}

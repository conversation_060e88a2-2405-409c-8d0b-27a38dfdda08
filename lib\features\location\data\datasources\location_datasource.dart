import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../../../../core/error/exceptions.dart';
import '../../domain/entities/location_entity.dart';

abstract class LocationDataSource {
  Future<LocationEntity> getCurrentLocation();
  Future<List<LocationEntity>> searchLocation(String query);
  Future<LocationEntity> getLocationFromCoordinates(double latitude, double longitude);
}

class LocationDataSourceImpl implements LocationDataSource {
  @override
  Future<LocationEntity> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw LocationException(message: 'Location services are disabled.');
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw PermissionException(message: 'Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw PermissionException(
          message: 'Location permissions are permanently denied, we cannot request permissions.',
        );
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      return await getLocationFromCoordinates(position.latitude, position.longitude);
    } catch (e) {
      if (e is LocationException || e is PermissionException) {
        rethrow;
      }
      throw LocationException(message: 'Failed to get current location: ${e.toString()}');
    }
  }

  @override
  Future<List<LocationEntity>> searchLocation(String query) async {
    try {
      List<Location> locations = await locationFromAddress(query);
      List<LocationEntity> results = [];

      for (Location location in locations) {
        List<Placemark> placemarks = await placemarkFromCoordinates(
          location.latitude,
          location.longitude,
        );

        if (placemarks.isNotEmpty) {
          Placemark placemark = placemarks.first;
          results.add(LocationEntity(
            latitude: location.latitude,
            longitude: location.longitude,
            city: placemark.locality ?? placemark.subAdministrativeArea ?? 'Unknown City',
            country: placemark.country ?? 'Unknown Country',
            state: placemark.administrativeArea,
            address: '${placemark.street ?? ''} ${placemark.name ?? ''}'.trim(),
            timezone: 'UTC', // Default timezone, would need additional API for accurate timezone
          ));
        }
      }

      return results;
    } catch (e) {
      throw LocationException(message: 'Failed to search location: ${e.toString()}');
    }
  }

  @override
  Future<LocationEntity> getLocationFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);

      if (placemarks.isNotEmpty) {
        Placemark placemark = placemarks.first;
        return LocationEntity(
          latitude: latitude,
          longitude: longitude,
          city: placemark.locality ?? placemark.subAdministrativeArea ?? 'Unknown City',
          country: placemark.country ?? 'Unknown Country',
          state: placemark.administrativeArea,
          address: '${placemark.street ?? ''} ${placemark.name ?? ''}'.trim(),
          timezone: 'UTC', // Default timezone
        );
      } else {
        return LocationEntity(
          latitude: latitude,
          longitude: longitude,
          city: 'Unknown City',
          country: 'Unknown Country',
          timezone: 'UTC',
        );
      }
    } catch (e) {
      throw LocationException(message: 'Failed to get location details: ${e.toString()}');
    }
  }
}

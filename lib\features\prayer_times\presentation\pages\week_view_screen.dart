import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/app_config.dart';
import '../../../../core/utils/hijri_date.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../../../settings/presentation/bloc/settings_bloc.dart';
import '../../domain/entities/prayer_times.dart';
import '../../export/services/export_service.dart';
import '../../export/prayer_times_exporter.dart';
import '../bloc/prayer_view_state.dart';
import '../widgets/export_dialog.dart';

class WeekViewScreen extends StatefulWidget {
  final List<PrayerTimes> weekPrayerTimes;
  final DateTime startDate;

  const WeekViewScreen({
    super.key,
    required this.weekPrayerTimes,
    required this.startDate,
  });

  @override
  State<WeekViewScreen> createState() => _WeekViewScreenState();
}

class _WeekViewScreenState extends State<WeekViewScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gebetszeiten – Woche'),
        actions: [
          PopupMenuButton<String>(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.more_horiz,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
            ),
            tooltip: 'Export Options',
            onSelected: (value) {
              switch (value) {
                case 'export_pdf':
                  _exportPDF();
                  break;
                case 'export_csv':
                  _exportCSV();
                  break;
                case 'share_pdf':
                  _sharePDF();
                  break;
                case 'print':
                  _printPrayerTimes();
                  break;
                case 'export_pdf_landscape':
                  _exportPDFLandscape();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export_pdf',
                child: Row(
                  children: [
                    Icon(Icons.picture_as_pdf_rounded, size: 20),
                    SizedBox(width: 12),
                    Text('Export PDF'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_csv',
                child: Row(
                  children: [
                    Icon(Icons.table_chart_rounded, size: 20),
                    SizedBox(width: 12),
                    Text('Export CSV'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'share_pdf',
                child: Row(
                  children: [
                    Icon(Icons.share_rounded, size: 20),
                    SizedBox(width: 12),
                    Text('Share PDF'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'print',
                child: Row(
                  children: [
                    Icon(Icons.print_rounded, size: 20),
                    SizedBox(width: 12),
                    Text('Print'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_pdf_landscape',
                child: Row(
                  children: [
                    Icon(Icons.crop_rotate_rounded, size: 20),
                    SizedBox(width: 12),
                    Text('PDF Landscape'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Container(
        decoration: _getBackgroundDecoration(context),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(AppConfig.defaultPadding),
            child: Column(
              children: [
                // Week header
                _buildWeekHeader(),
                const SizedBox(height: AppConfig.defaultPadding),
                
                // Prayer times table
                Expanded(
                  child: _buildPrayerTimesTable(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWeekHeader() {
    if (widget.weekPrayerTimes.isEmpty) return const SizedBox.shrink();
    
    final startDate = widget.weekPrayerTimes.first.date;
    final endDate = widget.weekPrayerTimes.last.date;
    
    return BlocBuilder<SettingsBloc, SettingsState>(
      builder: (context, state) {
        return GlassmorphismCard(
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    Icons.calendar_view_week,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Week View',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        Text(
                          '${_formatDate(startDate)} - ${_formatDate(endDate)}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      '7 days',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPrayerTimesTable() {
    if (widget.weekPrayerTimes.isEmpty) {
      return const Center(
        child: Text('No prayer times available'),
      );
    }

    return Column(
      children: [
        // Table header
        _buildTableHeader(),
        const SizedBox(height: 8),
        
        // Table rows
        Expanded(
          child: ListView.builder(
            itemCount: widget.weekPrayerTimes.length,
            itemBuilder: (context, index) {
              final prayerTimes = widget.weekPrayerTimes[index];
              final isToday = _isToday(prayerTimes.date);
              
              return _buildTableRow(prayerTimes, isToday);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              'Date',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(child: _buildHeaderCell('Fajr')),
          Expanded(child: _buildHeaderCell('Dhuhr')),
          Expanded(child: _buildHeaderCell('Asr')),
          Expanded(child: _buildHeaderCell('Maghrib')),
          Expanded(child: _buildHeaderCell('Isha')),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String text) {
    return Text(
      text,
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        fontWeight: FontWeight.w600,
        color: Theme.of(context).colorScheme.primary,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildTableRow(PrayerTimes prayerTimes, bool isToday) {
    return BlocBuilder<SettingsBloc, SettingsState>(
      builder: (context, state) {
        final languageCode = state.language.split('_')[0];
        final hijriOffset = state.hijriOffset;
        final hijriDateStr = HijriDateUtils.getHijriDateString(
          prayerTimes.date, 
          languageCode, 
          offset: hijriOffset,
        );
        
        return Container(
          margin: const EdgeInsets.only(bottom: 4),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color: isToday 
                ? Theme.of(context).colorScheme.primary.withOpacity(0.05)
                : null,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isToday 
                  ? Theme.of(context).colorScheme.primary.withOpacity(0.3)
                  : Colors.grey.withOpacity(0.2),
              width: isToday ? 2 : 0.5,
            ),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Gregorian date
                    Text(
                      '${prayerTimes.date.day}/${prayerTimes.date.month}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: isToday ? FontWeight.w600 : FontWeight.normal,
                        color: isToday ? Theme.of(context).colorScheme.primary : null,
                      ),
                    ),
                    Text(
                      _getWeekdayName(prayerTimes.date),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 2),
                    // Hijri date (compact)
                    Text(
                      _getCompactHijriDate(hijriDateStr),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppConfig.primaryGold,
                        fontSize: 10,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              Expanded(child: _buildTimeCell(prayerTimes.fajr)),
              Expanded(child: _buildTimeCell(prayerTimes.dhuhr)),
              Expanded(child: _buildTimeCell(prayerTimes.asr)),
              Expanded(child: _buildTimeCell(prayerTimes.maghrib)),
              Expanded(child: _buildTimeCell(prayerTimes.isha)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTimeCell(String time) {
    return Text(
      time,
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        fontWeight: FontWeight.w500,
      ),
      textAlign: TextAlign.center,
    );
  }

  bool _isToday(DateTime date) {
    final today = HijriDateUtils.getTodayLocal();
    return date.year == today.year &&
           date.month == today.month &&
           date.day == today.day;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getWeekdayName(DateTime date) {
    const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return weekdays[date.weekday - 1];
  }

  String _getCompactHijriDate(String fullHijriDate) {
    // Extract day and month from full Hijri date string
    // Example: "3 Rabīʿ al-awwal 1447 AH (Umm al-Qura)" -> "3 Rabīʿ"
    final parts = fullHijriDate.split(' ');
    if (parts.length >= 2) {
      final day = parts[0];
      final month = parts[1].split(' ')[0]; // Take first word of month name
      return '$day $month';
    }
    return fullHijriDate;
  }

  BoxDecoration _getBackgroundDecoration(BuildContext context) {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          Theme.of(context).colorScheme.primary.withOpacity(0.05),
          Theme.of(context).scaffoldBackgroundColor,
        ],
      ),
    );
  }

  void _exportPDF() {
    final settingsState = context.read<SettingsBloc>().state;
    final cityName = widget.weekPrayerTimes.isNotEmpty 
        ? (widget.weekPrayerTimes.first.city.isNotEmpty 
            ? widget.weekPrayerTimes.first.city 
            : 'Current Location')
        : 'Current Location';
    
    PrayerTimesExporter.exportToPdf(
      context: context,
      prayerTimesList: widget.weekPrayerTimes,
      viewType: 'week',
      cityName: cityName,
      hijriOffset: settingsState.hijriOffset,
      landscape: false,
    );
  }

  void _exportCSV() {
    final settingsState = context.read<SettingsBloc>().state;
    final cityName = widget.weekPrayerTimes.isNotEmpty 
        ? (widget.weekPrayerTimes.first.city.isNotEmpty 
            ? widget.weekPrayerTimes.first.city 
            : 'Current Location')
        : 'Current Location';
    
    PrayerTimesExporter.exportToCsv(
      context: context,
      prayerTimesList: widget.weekPrayerTimes,
      viewType: 'week',
      cityName: cityName,
      hijriOffset: settingsState.hijriOffset,
    );
  }

  void _sharePDF() {
    final settingsState = context.read<SettingsBloc>().state;
    final cityName = widget.weekPrayerTimes.isNotEmpty 
        ? (widget.weekPrayerTimes.first.city.isNotEmpty 
            ? widget.weekPrayerTimes.first.city 
            : 'Current Location')
        : 'Current Location';
    
    PrayerTimesExporter.sharePdf(
      context: context,
      prayerTimesList: widget.weekPrayerTimes,
      viewType: 'week',
      cityName: cityName,
      hijriOffset: settingsState.hijriOffset,
    );
  }

  void _printPrayerTimes() {
    final settingsState = context.read<SettingsBloc>().state;
    final cityName = widget.weekPrayerTimes.isNotEmpty 
        ? (widget.weekPrayerTimes.first.city.isNotEmpty 
            ? widget.weekPrayerTimes.first.city 
            : 'Current Location')
        : 'Current Location';
    
    PrayerTimesExporter.printPrayerTimes(
      context: context,
      prayerTimesList: widget.weekPrayerTimes,
      viewType: 'week',
      cityName: cityName,
      hijriOffset: settingsState.hijriOffset,
      landscape: false,
    );
  }

  void _exportPDFLandscape() {
    final settingsState = context.read<SettingsBloc>().state;
    final cityName = widget.weekPrayerTimes.isNotEmpty 
        ? (widget.weekPrayerTimes.first.city.isNotEmpty 
            ? widget.weekPrayerTimes.first.city 
            : 'Current Location')
        : 'Current Location';
    
    PrayerTimesExporter.exportToPdf(
      context: context,
      prayerTimesList: widget.weekPrayerTimes,
      viewType: 'week',
      cityName: cityName,
      hijriOffset: settingsState.hijriOffset,
      landscape: true,
    );
  }
}

import 'package:equatable/equatable.dart';

class PrayerTimes extends Equatable {
  final DateTime date;
  final String fajr;
  final String sunrise;
  final String dhuhr;
  final String asr;
  final String sunset;
  final String maghrib;
  final String isha;
  final String midnight;
  final String imsak;
  final int calculationMethod;
  final double latitude;
  final double longitude;
  final String city;
  final String country;
  final String timezone;

  const PrayerTimes({
    required this.date,
    required this.fajr,
    required this.sunrise,
    required this.dhuhr,
    required this.asr,
    required this.sunset,
    required this.maghrib,
    required this.isha,
    required this.midnight,
    required this.imsak,
    required this.calculationMethod,
    required this.latitude,
    required this.longitude,
    required this.city,
    required this.country,
    required this.timezone,
  });

  List<PrayerTime> get prayersList => [
        PrayerTime(name: '<PERSON>ajr', time: fajr),
        PrayerTime(name: 'Dhuhr', time: dhuhr),
        PrayerTime(name: 'Asr', time: asr),
        PrayerTime(name: '<PERSON>ghrib', time: maghrib),
        PrayerTime(name: '<PERSON><PERSON>', time: isha),
      ];

  PrayerTime? getNextPrayer() {
    final now = DateTime.now();
    final currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    
    for (final prayer in prayersList) {
      if (prayer.time.compareTo(currentTime) > 0) {
        return prayer;
      }
    }
    
    // If no prayer is left today, return Fajr of next day
    return PrayerTime(name: 'Fajr', time: fajr);
  }

  Duration? getTimeUntilNextPrayer() {
    final nextPrayer = getNextPrayer();
    if (nextPrayer == null) return null;

    final now = DateTime.now();
    final prayerTimeParts = nextPrayer.time.split(':');
    final prayerHour = int.parse(prayerTimeParts[0]);
    final prayerMinute = int.parse(prayerTimeParts[1]);
    
    var prayerDateTime = DateTime(
      now.year,
      now.month,
      now.day,
      prayerHour,
      prayerMinute,
    );
    
    // If prayer time has passed today, it's tomorrow's Fajr
    if (prayerDateTime.isBefore(now) && nextPrayer.name == 'Fajr') {
      prayerDateTime = prayerDateTime.add(const Duration(days: 1));
    }
    
    return prayerDateTime.difference(now);
  }

  @override
  List<Object?> get props => [
        date,
        fajr,
        sunrise,
        dhuhr,
        asr,
        sunset,
        maghrib,
        isha,
        midnight,
        imsak,
        calculationMethod,
        latitude,
        longitude,
        city,
        country,
        timezone,
      ];
}

class PrayerTime extends Equatable {
  final String name;
  final String time;

  const PrayerTime({
    required this.name,
    required this.time,
  });

  @override
  List<Object?> get props => [name, time];
}

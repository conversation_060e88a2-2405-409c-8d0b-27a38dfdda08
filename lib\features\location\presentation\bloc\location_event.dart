part of 'location_bloc.dart';

abstract class LocationEvent extends Equatable {
  const LocationEvent();

  @override
  List<Object> get props => [];
}

class GetCurrentLocationEvent extends LocationEvent {}

class SearchLocationEvent extends LocationEvent {
  final String query;

  const SearchLocationEvent({required this.query});

  @override
  List<Object> get props => [query];
}

class SelectLocationEvent extends LocationEvent {
  final LocationEntity location;

  const SelectLocationEvent({required this.location});

  @override
  List<Object> get props => [location];
}

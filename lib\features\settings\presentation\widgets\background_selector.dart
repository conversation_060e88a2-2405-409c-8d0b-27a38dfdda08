import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/app_config.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../bloc/settings_bloc.dart';

class BackgroundSelector extends StatelessWidget {
  const BackgroundSelector({super.key});

  static const Map<String, String> presetBackgrounds = {
    'default': 'Default',
    'mosque': 'Mosque',
    'geometric': 'Geometric Pattern',
    'nature': 'Nature',
    'kaaba': 'Kaaba',
    'calligraphy': 'Islamic Calligraphy',
  };

  @override
  Widget build(BuildContext context) {
    return GlassmorphismContainer(
      forceTransparent: true,
      borderRadius: 20,
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'Background',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: AppConfig.smallPadding),
          Text(
            'Choose a background for the app',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          BlocBuilder<SettingsBloc, SettingsState>(
            builder: (context, state) {
              return SizedBox(
                height: 300,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Preset backgrounds
                      ...presetBackgrounds.entries.map((entry) {
                        final backgroundId = entry.key;
                        final backgroundName = entry.value;
                        final isSelected = state.backgroundImage == backgroundId;

                        return ListTile(
                          leading: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: isSelected
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.grey[300]!,
                                width: 2,
                              ),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(6),
                              child: _buildBackgroundPreview(backgroundId),
                            ),
                          ),
                          title: Text(
                            backgroundName,
                            style: TextStyle(
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            ),
                          ),
                          trailing: isSelected
                              ? Icon(
                                  Icons.check,
                                  color: Theme.of(context).colorScheme.primary,
                                )
                              : null,
                          onTap: () {
                            context.read<SettingsBloc>().add(
                              UpdateBackgroundImage(image: backgroundId),
                            );
                            Navigator.pop(context);
                          },
                        );
                      }).toList(),

                      const Divider(),

                      // Custom image option
                      ListTile(
                        leading: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]!, width: 2),
                            color: Colors.grey[100],
                          ),
                          child: const Icon(Icons.add_photo_alternate),
                        ),
                        title: const Text('Custom Image'),
                        subtitle: const Text('Choose from gallery'),
                        onTap: () {
                          _pickCustomImage(context);
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBackgroundPreview(String backgroundId) {
    switch (backgroundId) {
      case 'default':
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [AppConfig.primaryGreen, AppConfig.primaryGold],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        );
      case 'mosque':
        return Container(
          color: Colors.blue[900],
          child: const Icon(Icons.mosque, color: Colors.white, size: 20),
        );
      case 'geometric':
        return Container(
          decoration: BoxDecoration(
            color: AppConfig.primaryGreen,
            gradient: LinearGradient(
              colors: [
                AppConfig.primaryGreen,
                AppConfig.primaryGreen.withOpacity(0.7),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: const Icon(Icons.grid_4x4, color: Colors.white, size: 20),
        );
      case 'nature':
        return Container(
          color: Colors.green[700],
          child: const Icon(Icons.nature, color: Colors.white, size: 20),
        );
      case 'kaaba':
        return Container(
          color: Colors.black,
          child: const Icon(Icons.account_balance, color: Colors.white, size: 20),
        );
      case 'calligraphy':
        return Container(
          color: Colors.brown[800],
          child: const Icon(Icons.text_fields, color: Colors.white, size: 20),
        );
      default:
        return Container(
          color: Colors.grey[300],
          child: const Icon(Icons.image),
        );
    }
  }

  void _pickCustomImage(BuildContext context) async {
    final ImagePicker picker = ImagePicker();
    try {
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 80,
      );

      if (image != null) {
        // Save the image path as custom background
        context.read<SettingsBloc>().add(
          UpdateBackgroundImage(image: 'custom:${image.path}'),
        );

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Custom background image set!'),
            ),
          );
          Navigator.pop(context);
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: ${e.toString()}'),
          ),
        );
      }
    }
  }
}

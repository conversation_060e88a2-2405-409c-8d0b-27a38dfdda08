import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/prayer_times.dart';
import '../repositories/prayer_times_repository.dart';

class GetPrayerTimes implements UseCase<PrayerTimes, GetPrayerTimesParams> {
  final PrayerTimesRepository repository;

  GetPrayerTimes(this.repository);

  @override
  Future<Either<Failure, PrayerTimes>> call(GetPrayerTimesParams params) async {
    // First try to get from cache
    final cacheKey = _generateCacheKey(
      params.latitude,
      params.longitude,
      params.calculationMethod,
    );

    final cachedResult = await repository.getCachedPrayerTimes(
      date: params.date,
      cacheKey: cacheKey,
    );

    return cachedResult.fold(
      (failure) => _fetchFromApi(params),
      (cachedPrayerTimes) {
        if (cachedPrayerTimes != null) {
          return Right(cachedPrayerTimes);
        }
        return _fetchFromApi(params);
      },
    );
  }

  Future<Either<Failure, PrayerTimes>> _fetchFromApi(GetPrayerTimesParams params) async {
    final result = await repository.getPrayerTimes(
      date: params.date,
      latitude: params.latitude,
      longitude: params.longitude,
      calculationMethod: params.calculationMethod,
    );

    return result.fold(
      (failure) {
        // If this is a network failure, provide a more user-friendly message
        if (failure is NetworkFailure) {
          return Left(NetworkFailure(
            message: 'No internet connection. Please check your connection and try again.',
          ));
        }
        return Left(failure);
      },
      (prayerTimes) async {
        // Cache the result
        final cacheKey = _generateCacheKey(
          params.latitude,
          params.longitude,
          params.calculationMethod,
        );

        await repository.cachePrayerTimes(
          prayerTimes: [prayerTimes],
          cacheKey: cacheKey,
        );

        return Right(prayerTimes);
      },
    );
  }

  String _generateCacheKey(double latitude, double longitude, int method) {
    return '${latitude.toStringAsFixed(4)}_${longitude.toStringAsFixed(4)}_$method';
  }
}

class GetPrayerTimesParams extends Equatable {
  final DateTime date;
  final double latitude;
  final double longitude;
  final int calculationMethod;

  const GetPrayerTimesParams({
    required this.date,
    required this.latitude,
    required this.longitude,
    required this.calculationMethod,
  });

  @override
  List<Object> get props => [date, latitude, longitude, calculationMethod];
}

import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:geolocator/geolocator.dart';
import '../../domain/entities/qibla_direction.dart';
import '../../domain/services/qibla_calculation_service.dart';
import '../../data/services/compass_sensor_service.dart';

part 'qibla_event.dart';
part 'qibla_state.dart';

class QiblaBloc extends Bloc<QiblaEvent, QiblaState> {
  final CompassSensorService _compassService;
  StreamSubscription<CompassReading>? _compassSubscription;
  StreamSubscription<Position>? _locationSubscription;

  QiblaBloc({
    required CompassSensorService compassService,
  })  : _compassService = compassService,
        super(const QiblaState()) {
    on<StartQiblaCompass>(_onStartQiblaCompass);
    on<StopQiblaCompass>(_onStopQiblaCompass);
    on<UpdateUserLocation>(_onUpdateUserLocation);
    on<CompassReadingReceived>(_onCompassReadingReceived);
    on<SwitchQiblaMode>(_onSwitchQiblaMode);
    on<CalibrateCompass>(_onCalibrateCompass);
    on<ResetQiblaCompass>(_onResetQiblaCompass);
    on<RequestLocationPermission>(_onRequestLocationPermission);
  }

  Future<void> _onStartQiblaCompass(
    StartQiblaCompass event,
    Emitter<QiblaState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, error: null));

    try {
      // Check location permission
      final hasPermission = await _checkLocationPermission();
      if (!hasPermission) {
        emit(state.copyWith(
          isLoading: false,
          error: 'Location permission required for Qibla compass',
          hasLocationPermission: false,
        ));
        return;
      }

      // Get current location
      final position = await _getCurrentLocation();
      if (position == null) {
        emit(state.copyWith(
          isLoading: false,
          error: 'Unable to get current location',
        ));
        return;
      }

      // Calculate initial Qibla direction
      final qiblaDirection = _calculateQiblaDirection(
        userLatitude: position.latitude,
        userLongitude: position.longitude,
        deviceHeading: 0.0,
        compassStatus: CompassStatus.ok,
        mode: QiblaMode.compass,
      );

      emit(state.copyWith(
        qiblaDirection: qiblaDirection,
        isLoading: false,
        isCompassActive: true,
        hasLocationPermission: true,
      ));

      // Start compass sensor
      await _startCompassSensor();

      // Start location updates
      _startLocationUpdates();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to start Qibla compass: $e',
      ));
    }
  }

  Future<void> _onStopQiblaCompass(
    StopQiblaCompass event,
    Emitter<QiblaState> emit,
  ) async {
    _stopCompassSensor();
    _stopLocationUpdates();

    emit(state.copyWith(
      isCompassActive: false,
      error: null,
    ));
  }

  Future<void> _onUpdateUserLocation(
    UpdateUserLocation event,
    Emitter<QiblaState> emit,
  ) async {
    if (state.qiblaDirection == null) return;

    final updatedDirection = _calculateQiblaDirection(
      userLatitude: event.latitude,
      userLongitude: event.longitude,
      deviceHeading: state.deviceHeading,
      compassStatus: state.compassStatus,
      mode: state.mode,
      isManual: event.isManual,
    );

    emit(state.copyWith(qiblaDirection: updatedDirection));
  }

  Future<void> _onCompassReadingReceived(
    CompassReadingReceived event,
    Emitter<QiblaState> emit,
  ) async {
    if (state.qiblaDirection == null) return;

    final updatedDirection = _calculateQiblaDirection(
      userLatitude: state.userLatitude,
      userLongitude: state.userLongitude,
      deviceHeading: event.heading,
      compassStatus: event.status,
      mode: state.mode,
      isManual: state.isManualLocation,
    );

    emit(state.copyWith(qiblaDirection: updatedDirection));
  }

  Future<void> _onSwitchQiblaMode(
    SwitchQiblaMode event,
    Emitter<QiblaState> emit,
  ) async {
    if (state.qiblaDirection == null) return;

    final updatedDirection = state.qiblaDirection!.copyWith(mode: event.mode);
    emit(state.copyWith(qiblaDirection: updatedDirection));

    // Start or stop compass based on mode
    if (event.mode == QiblaMode.compass) {
      await _startCompassSensor();
    } else {
      _stopCompassSensor();
    }
  }

  Future<void> _onCalibrateCompass(
    CalibrateCompass event,
    Emitter<QiblaState> emit,
  ) async {
    // Emit calibrating state
    if (state.qiblaDirection != null) {
      emit(state.copyWith(
        qiblaDirection: state.qiblaDirection!.copyWith(
          compassStatus: CompassStatus.calibrating,
        ),
      ));
    }

    // Stop compass sensor
    _stopCompassSensor();
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Clear any cached readings and force recalibration
    await _compassService.resetCalibration();
    
    // Restart compass sensor
    await _startCompassSensor();
    
    // Start location updates again if not running
    _startLocationUpdates();
  }

  Future<void> _onResetQiblaCompass(
    ResetQiblaCompass event,
    Emitter<QiblaState> emit,
  ) async {
    _stopCompassSensor();
    _stopLocationUpdates();

    emit(const QiblaState());
  }

  Future<void> _onRequestLocationPermission(
    RequestLocationPermission event,
    Emitter<QiblaState> emit,
  ) async {
    final hasPermission = await _requestLocationPermission();
    emit(state.copyWith(hasLocationPermission: hasPermission));
  }

  // Helper methods

  QiblaDirection _calculateQiblaDirection({
    required double userLatitude,
    required double userLongitude,
    required double deviceHeading,
    required CompassStatus compassStatus,
    required QiblaMode mode,
    bool isManual = false,
  }) {
    final bearingTrue = QiblaCalculationService.calculateTrueBearing(
      userLatitude: userLatitude,
      userLongitude: userLongitude,
    );

    final distanceKm = QiblaCalculationService.calculateDistance(
      userLatitude: userLatitude,
      userLongitude: userLongitude,
    );

    final directionDelta = QiblaCalculationService.calculateDirectionDelta(
      bearingTrue: bearingTrue,
      deviceHeading: deviceHeading,
    );

    return QiblaDirection(
      bearingTrue: bearingTrue,
      deviceHeading: deviceHeading,
      directionDelta: directionDelta,
      distanceKm: distanceKm,
      compassStatus: compassStatus,
      mode: mode,
      userLatitude: userLatitude,
      userLongitude: userLongitude,
      isManualLocation: isManual,
    );
  }

  Future<void> _startCompassSensor() async {
    await _compassService.startListening();
    _compassSubscription = _compassService.compassStream.listen(
      (reading) {
        add(CompassReadingReceived(
          heading: reading.heading,
          accuracy: reading.accuracy,
          status: reading.status,
        ));
      },
    );
  }

  void _stopCompassSensor() {
    _compassService.stopListening();
    _compassSubscription?.cancel();
    _compassSubscription = null;
  }

  void _startLocationUpdates() {
    const locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10, // Update every 10 meters
    );

    _locationSubscription = Geolocator.getPositionStream(
      locationSettings: locationSettings,
    ).listen(
      (position) {
        add(UpdateUserLocation(
          latitude: position.latitude,
          longitude: position.longitude,
          isManual: false,
        ));
      },
    );
  }

  void _stopLocationUpdates() {
    _locationSubscription?.cancel();
    _locationSubscription = null;
  }

  Future<bool> _checkLocationPermission() async {
    final permission = await Geolocator.checkPermission();
    return permission == LocationPermission.always ||
           permission == LocationPermission.whileInUse;
  }

  Future<bool> _requestLocationPermission() async {
    final permission = await Geolocator.requestPermission();
    return permission == LocationPermission.always ||
           permission == LocationPermission.whileInUse;
  }

  Future<Position?> _getCurrentLocation() async {
    try {
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> close() {
    _stopCompassSensor();
    _stopLocationUpdates();
    _compassService.dispose();
    return super.close();
  }
}

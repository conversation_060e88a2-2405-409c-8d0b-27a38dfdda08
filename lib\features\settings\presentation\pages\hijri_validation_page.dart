import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/app_config.dart';
import '../../../../core/utils/hijri_date.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../bloc/settings_bloc.dart';

class HijriValidationPage extends StatefulWidget {
  const HijriValidationPage({super.key});

  @override
  State<HijriValidationPage> createState() => _HijriValidationPageState();
}

class _HijriValidationPageState extends State<HijriValidationPage> {
  Map<String, dynamic>? _validationResults;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _runValidation();
  }

  void _runValidation() {
    setState(() {
      _isLoading = true;
    });

    Future.delayed(const Duration(milliseconds: 500), () {
      final settingsState = context.read<SettingsBloc>().state;
      final results = HijriDateUtils.validateHijriConversion(
        offset: settingsState.hijriOffset,
      );
      
      setState(() {
        _validationResults = results;
        _isLoading = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Hijri Validation'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _runValidation,
          ),
        ],
      ),
      body: Container(
        decoration: _getBackgroundDecoration(context),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(AppConfig.defaultPadding),
            child: Column(
              children: [
                // Header
                _buildHeader(),
                const SizedBox(height: AppConfig.defaultPadding),
                
                // Results
                Expanded(
                  child: _isLoading ? _buildLoading() : _buildResults(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return BlocBuilder<SettingsBloc, SettingsState>(
      builder: (context, state) {
        return GlassmorphismCard(
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    Icons.verified,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Umm al-Qura Validation',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        Text(
                          'Testing against official KSA reference data',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppConfig.primaryGold.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: AppConfig.primaryGold.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      'Offset: ${state.hijriOffset}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppConfig.primaryGold,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoading() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Running validation tests...'),
        ],
      ),
    );
  }

  Widget _buildResults() {
    if (_validationResults == null) {
      return const Center(child: Text('No validation results available'));
    }

    final results = _validationResults!;
    final passed = results['passed'] as int;
    final failed = results['failed'] as int;
    final total = results['total'] as int;
    final details = results['details'] as List<Map<String, dynamic>>;

    return Column(
      children: [
        // Summary
        _buildSummary(passed, failed, total),
        const SizedBox(height: AppConfig.defaultPadding),
        
        // Details
        Expanded(
          child: ListView.builder(
            itemCount: details.length,
            itemBuilder: (context, index) {
              final detail = details[index];
              return _buildDetailCard(detail);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSummary(int passed, int failed, int total) {
    final isAllPassed = failed == 0;
    
    return GlassmorphismCard(
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                isAllPassed ? Icons.check_circle : Icons.error,
                color: isAllPassed ? Colors.green : Colors.red,
                size: 32,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isAllPassed ? 'All Tests Passed!' : 'Some Tests Failed',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isAllPassed ? Colors.green : Colors.red,
                      ),
                    ),
                    Text(
                      '$passed/$total tests passed',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (!isAllPassed) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.warning, color: Colors.red, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Some reference dates don\'t match. Consider adjusting the Hijri offset.',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailCard(Map<String, dynamic> detail) {
    final passed = detail['passed'] as bool;
    final description = detail['description'] as String;
    final expected = detail['expected'] as String?;
    final actual = detail['actual'] as String?;
    final error = detail['error'] as String?;

    return GlassmorphismCard(
      margin: const EdgeInsets.only(bottom: AppConfig.smallPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                passed ? Icons.check_circle : Icons.error,
                color: passed ? Colors.green : Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          if (!passed && expected != null && actual != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Expected: $expected',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.green,
                    ),
                  ),
                  Text(
                    'Actual: $actual',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
            ),
          ],
          if (error != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                'Error: $error',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.red,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  BoxDecoration _getBackgroundDecoration(BuildContext context) {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          Theme.of(context).colorScheme.primary.withOpacity(0.05),
          Theme.of(context).scaffoldBackgroundColor,
        ],
      ),
    );
  }
}

import 'dart:convert';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import '../models/prayer_times_model.dart';

/// Local cache datasource for prayer times
/// Stores prayer times locally for offline access
class PrayerTimesCacheDatasource {
  static const String _boxName = 'prayer_times_cache';
  static const String _preloadStatusKey = 'preload_status';
  
  late Box<String> _box;
  
  /// Initialize the cache
  Future<void> init() async {
    _box = await Hive.openBox<String>(_boxName);
  }
  
  /// Generate cache key for prayer times
  String _generateCacheKey({
    required double latitude,
    required double longitude,
    required int calculationMethod,
    required DateTime date,
  }) {
    final dateStr = DateFormat('yyyy-MM').format(date);
    final latStr = latitude.toStringAsFixed(2);
    final lonStr = longitude.toStringAsFixed(2);
    return 'prayer_times_${calculationMethod}_${latStr}_${lonStr}_$dateStr';
  }
  
  /// Cache prayer times for a month
  Future<void> cachePrayerTimes({
    required List<PrayerTimesModel> prayerTimesList,
    required double latitude,
    required double longitude,
    required int calculationMethod,
    required DateTime month,
  }) async {
    final cacheKey = _generateCacheKey(
      latitude: latitude,
      longitude: longitude,
      calculationMethod: calculationMethod,
      date: month,
    );
    
    final jsonList = prayerTimesList.map((pt) => pt.toJson()).toList();
    final jsonString = json.encode(jsonList);
    
    await _box.put(cacheKey, jsonString);
    
    // Update last cached timestamp
    await _box.put('${cacheKey}_timestamp', DateTime.now().toIso8601String());
  }
  
  /// Get cached prayer times for a month
  Future<List<PrayerTimesModel>?> getCachedPrayerTimes({
    required double latitude,
    required double longitude,
    required int calculationMethod,
    required DateTime month,
  }) async {
    final cacheKey = _generateCacheKey(
      latitude: latitude,
      longitude: longitude,
      calculationMethod: calculationMethod,
      date: month,
    );
    
    final jsonString = _box.get(cacheKey);
    if (jsonString == null) return null;
    
    try {
      final jsonList = json.decode(jsonString) as List<dynamic>;
      return jsonList
          .map((json) => PrayerTimesModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      // Remove corrupted cache entry
      await _box.delete(cacheKey);
      return null;
    }
  }
  
  /// Check if prayer times are cached for a specific month
  bool isCached({
    required double latitude,
    required double longitude,
    required int calculationMethod,
    required DateTime month,
  }) {
    final cacheKey = _generateCacheKey(
      latitude: latitude,
      longitude: longitude,
      calculationMethod: calculationMethod,
      date: month,
    );
    
    return _box.containsKey(cacheKey);
  }
  
  /// Get cache timestamp for a specific month
  DateTime? getCacheTimestamp({
    required double latitude,
    required double longitude,
    required int calculationMethod,
    required DateTime month,
  }) {
    final cacheKey = _generateCacheKey(
      latitude: latitude,
      longitude: longitude,
      calculationMethod: calculationMethod,
      date: month,
    );
    
    final timestampStr = _box.get('${cacheKey}_timestamp');
    if (timestampStr == null) return null;
    
    try {
      return DateTime.parse(timestampStr);
    } catch (e) {
      return null;
    }
  }
  
  /// Check if cache is expired (older than 30 days)
  bool isCacheExpired({
    required double latitude,
    required double longitude,
    required int calculationMethod,
    required DateTime month,
  }) {
    final timestamp = getCacheTimestamp(
      latitude: latitude,
      longitude: longitude,
      calculationMethod: calculationMethod,
      month: month,
    );
    
    if (timestamp == null) return true;
    
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    return difference.inDays > 30;
  }
  
  /// Clear expired cache entries
  Future<void> clearExpiredCache() async {
    final keys = _box.keys.toList();
    final now = DateTime.now();
    
    for (final key in keys) {
      if (key.toString().endsWith('_timestamp')) {
        final timestampStr = _box.get(key);
        if (timestampStr != null) {
          try {
            final timestamp = DateTime.parse(timestampStr);
            if (now.difference(timestamp).inDays > 30) {
              // Remove both timestamp and data
              await _box.delete(key);
              final dataKey = key.toString().replaceAll('_timestamp', '');
              await _box.delete(dataKey);
            }
          } catch (e) {
            // Remove corrupted entries
            await _box.delete(key);
          }
        }
      }
    }
  }
  
  /// Clear all cache
  Future<void> clearAllCache() async {
    await _box.clear();
  }
  
  /// Get cache size in MB
  double getCacheSizeMB() {
    int totalSize = 0;
    for (final value in _box.values) {
      totalSize += value.length * 2; // Approximate UTF-16 encoding
    }
    return totalSize / (1024 * 1024);
  }
  
  /// Get number of cached months
  int getCachedMonthsCount() {
    return _box.keys
        .where((key) => !key.toString().endsWith('_timestamp') && 
                       !key.toString().startsWith('preload_'))
        .length;
  }
  
  /// Preload prayer times for next N days
  Future<void> setPreloadStatus({
    required DateTime lastPreloadDate,
    required int preloadDays,
  }) async {
    final status = {
      'lastPreloadDate': lastPreloadDate.toIso8601String(),
      'preloadDays': preloadDays,
    };
    await _box.put(_preloadStatusKey, json.encode(status));
  }
  
  /// Get preload status
  Map<String, dynamic>? getPreloadStatus() {
    final statusStr = _box.get(_preloadStatusKey);
    if (statusStr == null) return null;
    
    try {
      return json.decode(statusStr) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }
  
  /// Check if preload is needed
  bool isPreloadNeeded({int preloadDays = 30}) {
    final status = getPreloadStatus();
    if (status == null) return true;
    
    try {
      final lastPreloadDate = DateTime.parse(status['lastPreloadDate']);
      final daysSincePreload = DateTime.now().difference(lastPreloadDate).inDays;
      return daysSincePreload > 7; // Refresh weekly
    } catch (e) {
      return true;
    }
  }
  
  /// Get cached months for debugging
  List<String> getCachedMonths() {
    return _box.keys
        .where((key) => !key.toString().endsWith('_timestamp') && 
                       !key.toString().startsWith('preload_'))
        .map((key) => key.toString())
        .toList();
  }
}

import 'package:hive/hive.dart';

part 'prayer_notification_settings.g.dart';

@HiveType(typeId: 3)
class PrayerNotificationSettings extends HiveObject {
  @HiveField(0)
  bool fajrEnabled;

  @HiveField(1)
  bool dhuhrEnabled;

  @HiveField(2)
  bool asrEnabled;

  @HiveField(3)
  bool maghribEnabled;

  @HiveField(4)
  bool ishaEnabled;

  @HiveField(5)
  String adhanSound;

  @HiveField(6)
  bool globalNotificationsEnabled;

  PrayerNotificationSettings({
    this.fajrEnabled = true,
    this.dhuhrEnabled = true,
    this.asrEnabled = true,
    this.maghribEnabled = true,
    this.ishaEnabled = true,
    this.adhanSound = 'default_adhan.mp3',
    this.globalNotificationsEnabled = true,
  });

  bool isPrayerEnabled(String prayerName) {
    switch (prayerName.toLowerCase()) {
      case 'fajr':
        return fajrEnabled;
      case 'dhuhr':
        return dhuhrEnabled;
      case 'asr':
        return asrEnabled;
      case 'maghrib':
        return maghribEnabled;
      case 'isha':
        return ishaEnabled;
      default:
        return false;
    }
  }

  void setPrayerEnabled(String prayerName, bool enabled) {
    switch (prayerName.toLowerCase()) {
      case 'fajr':
        fajrEnabled = enabled;
        break;
      case 'dhuhr':
        dhuhrEnabled = enabled;
        break;
      case 'asr':
        asrEnabled = enabled;
        break;
      case 'maghrib':
        maghribEnabled = enabled;
        break;
      case 'isha':
        ishaEnabled = enabled;
        break;
    }
    // Don't call save() here - let the caller handle it
  }

  Map<String, dynamic> toJson() {
    return {
      'fajrEnabled': fajrEnabled,
      'dhuhrEnabled': dhuhrEnabled,
      'asrEnabled': asrEnabled,
      'maghribEnabled': maghribEnabled,
      'ishaEnabled': ishaEnabled,
      'adhanSound': adhanSound,
      'globalNotificationsEnabled': globalNotificationsEnabled,
    };
  }

  factory PrayerNotificationSettings.fromJson(Map<String, dynamic> json) {
    return PrayerNotificationSettings(
      fajrEnabled: json['fajrEnabled'] ?? true,
      dhuhrEnabled: json['dhuhrEnabled'] ?? true,
      asrEnabled: json['asrEnabled'] ?? true,
      maghribEnabled: json['maghribEnabled'] ?? true,
      ishaEnabled: json['ishaEnabled'] ?? true,
      adhanSound: json['adhanSound'] ?? 'default_adhan.mp3',
      globalNotificationsEnabled: json['globalNotificationsEnabled'] ?? true,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:gebet/main.dart' as app;
import 'package:gebet/features/prayer_times/domain/entities/prayer_times.dart';
import 'package:gebet/features/prayer_times/presentation/widgets/prayer_times_table.dart';
import 'package:gebet/features/prayer_times/presentation/widgets/export_dialog.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('Export Integration Tests', () {
    testWidgets('should show export dialog and options', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();
      
      // Wait for the app to load
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Navigate to week view (if available)
      // This test assumes there's a way to navigate to week/month view
      // You might need to adjust based on your actual navigation structure
      
      // Look for export button or menu
      final exportButton = find.byIcon(Icons.file_download);
      if (exportButton.evaluate().isNotEmpty) {
        await tester.tap(exportButton);
        await tester.pumpAndSettle();
        
        // Verify export dialog appears
        expect(find.text('Export & Share'), findsOneWidget);
        expect(find.text('Export as PDF'), findsOneWidget);
        expect(find.text('Export as CSV'), findsOneWidget);
        expect(find.text('Share PDF'), findsOneWidget);
        expect(find.text('Print'), findsOneWidget);
        
        // Close dialog
        await tester.tap(find.text('Close'));
        await tester.pumpAndSettle();
      }
    });
    
    testWidgets('should handle export actions without crashing', (WidgetTester tester) async {
      // Create a test widget with prayer times table
      final testPrayerTimes = [
        PrayerTimes(
          date: DateTime.now(),
          fajr: '05:30',
          dhuhr: '12:15',
          asr: '15:30',
          maghrib: '18:00',
          isha: '19:30',
        ),
      ];
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PrayerTimesTable(
              prayerTimesList: testPrayerTimes,
              viewType: 'today',
              cityName: 'Test City',
              showExportActions: true,
            ),
          ),
        ),
      );
      
      await tester.pumpAndSettle();
      
      // Look for export buttons
      final pdfButton = find.text('Export as PDF');
      final csvButton = find.text('Export as CSV');
      
      if (pdfButton.evaluate().isNotEmpty) {
        // Test PDF export button doesn't crash
        await tester.tap(pdfButton);
        await tester.pumpAndSettle();
        
        // The actual export might fail due to platform limitations in tests,
        // but the app shouldn't crash
        expect(tester.takeException(), isNull);
      }
      
      if (csvButton.evaluate().isNotEmpty) {
        // Test CSV export button doesn't crash
        await tester.tap(csvButton);
        await tester.pumpAndSettle();
        
        // The actual export might fail due to platform limitations in tests,
        // but the app shouldn't crash
        expect(tester.takeException(), isNull);
      }
    });
    
    testWidgets('should show export options in more menu', (WidgetTester tester) async {
      final testPrayerTimes = [
        PrayerTimes(
          date: DateTime.now(),
          fajr: '05:30',
          dhuhr: '12:15',
          asr: '15:30',
          maghrib: '18:00',
          isha: '19:30',
        ),
      ];
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PrayerTimesTable(
              prayerTimesList: testPrayerTimes,
              viewType: 'week',
              cityName: 'Test City',
              showExportActions: true,
            ),
          ),
        ),
      );
      
      await tester.pumpAndSettle();
      
      // Look for more options button
      final moreButton = find.byIcon(Icons.more_vert);
      if (moreButton.evaluate().isNotEmpty) {
        await tester.tap(moreButton);
        await tester.pumpAndSettle();
        
        // Verify bottom sheet with options appears
        expect(find.text('Share PDF'), findsOneWidget);
        expect(find.text('Print'), findsOneWidget);
        expect(find.text('Export as PDF (Landscape)'), findsOneWidget);
        
        // Close bottom sheet by tapping outside or back button
        await tester.tapAt(const Offset(50, 50));
        await tester.pumpAndSettle();
      }
    });
  });
  
  group('Export Dialog Tests', () {
    testWidgets('should display all export options', (WidgetTester tester) async {
      final testPrayerTimes = [
        PrayerTimes(
          date: DateTime.now(),
          fajr: '05:30',
          dhuhr: '12:15',
          asr: '15:30',
          maghrib: '18:00',
          isha: '19:30',
        ),
      ];
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => ExportDialog(
                      prayerTimesList: testPrayerTimes,
                      viewType: PrayerViewType.today,
                      cityName: 'Test City',
                    ),
                  );
                },
                child: const Text('Show Export Dialog'),
              ),
            ),
          ),
        ),
      );
      
      await tester.pumpAndSettle();
      
      // Open dialog
      await tester.tap(find.text('Show Export Dialog'));
      await tester.pumpAndSettle();
      
      // Verify all export options are present
      expect(find.text('Export & Share'), findsOneWidget);
      expect(find.text('Export as PDF'), findsOneWidget);
      expect(find.text('Export as CSV'), findsOneWidget);
      expect(find.text('Share PDF'), findsOneWidget);
      expect(find.text('Print'), findsOneWidget);
      expect(find.text('Export as PDF (Landscape)'), findsOneWidget);
      expect(find.text('Close'), findsOneWidget);
      
      // Test close button
      await tester.tap(find.text('Close'));
      await tester.pumpAndSettle();
      
      // Dialog should be closed
      expect(find.text('Export & Share'), findsNothing);
    });
  });
}

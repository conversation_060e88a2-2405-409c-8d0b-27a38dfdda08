import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:timezone/timezone.dart' as tz;

/// Debug utilities for notification testing
class NotificationDebugUtils {
  static FlutterLocalNotificationsPlugin? _notificationsPlugin;

  /// Initialize notification plugin for debugging
  static Future<void> _initializePlugin() async {
    if (_notificationsPlugin != null) return;

    _notificationsPlugin = FlutterLocalNotificationsPlugin();
    
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const initSettings = InitializationSettings(android: androidSettings);
    
    await _notificationsPlugin!.initialize(initSettings);
  }

  /// Check notification system status
  static Future<Map<String, dynamic>> checkNotificationStatus() async {
    await _initializePlugin();
    
    final status = await Permission.notification.status;
    final exactAlarmStatus = await Permission.scheduleExactAlarm.status;
    
    return {
      'notification_permission': status.toString(),
      'exact_alarm_permission': exactAlarmStatus.toString(),
      'plugin_initialized': _notificationsPlugin != null,
    };
  }

  /// Send immediate test notification
  static Future<bool> sendTestNotification() async {
    try {
      await _initializePlugin();
      
      const androidDetails = AndroidNotificationDetails(
        'test_channel',
        'Test Notifications',
        channelDescription: 'Testing notification functionality',
        importance: Importance.max,
        priority: Priority.high,
      );
      
      const notificationDetails = NotificationDetails(android: androidDetails);
      
      await _notificationsPlugin!.show(
        999,
        'Test Notification',
        'This is a test notification from AlFalah',
        notificationDetails,
      );
      
      return true;
    } catch (e) {
      print('Test notification error: $e');
      return false;
    }
  }

  /// Schedule test notification for 10 seconds
  static Future<bool> scheduleTestNotification() async {
    try {
      await _initializePlugin();
      
      const androidDetails = AndroidNotificationDetails(
        'test_channel',
        'Test Notifications',
        channelDescription: 'Testing scheduled notification functionality',
        importance: Importance.max,
        priority: Priority.high,
      );
      
      const notificationDetails = NotificationDetails(android: androidDetails);
      
      final scheduledTime = tz.TZDateTime.now(tz.local).add(const Duration(seconds: 10));
      
      await _notificationsPlugin!.zonedSchedule(
        998,
        'Scheduled Test',
        'This notification was scheduled 10 seconds ago',
        scheduledTime,
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      );
      
      return true;
    } catch (e) {
      print('Scheduled test notification error: $e');
      return false;
    }
  }

  /// Request notification permissions
  static Future<bool> requestPermissions() async {
    final notificationStatus = await Permission.notification.request();
    final exactAlarmStatus = await Permission.scheduleExactAlarm.request();
    
    return notificationStatus.isGranted && exactAlarmStatus.isGranted;
  }

  /// Clear test notifications
  static Future<void> clearTestNotifications() async {
    await _initializePlugin();
    await _notificationsPlugin!.cancel(999);
    await _notificationsPlugin!.cancel(998);
  }
}
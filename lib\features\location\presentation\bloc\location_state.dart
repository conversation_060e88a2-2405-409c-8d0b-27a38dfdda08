part of 'location_bloc.dart';

abstract class LocationState extends Equatable {
  const LocationState();

  @override
  List<Object> get props => [];
}

class LocationInitial extends LocationState {}

class LocationLoading extends LocationState {}

class LocationSearching extends LocationState {}

class LocationLoaded extends LocationState {
  final LocationEntity location;

  const LocationLoaded({required this.location});

  @override
  List<Object> get props => [location];
}

class LocationSearchResults extends LocationState {
  final List<LocationEntity> locations;

  const LocationSearchResults({required this.locations});

  @override
  List<Object> get props => [locations];
}

class LocationError extends LocationState {
  final String message;

  const LocationError({required this.message});

  @override
  List<Object> get props => [message];
}
